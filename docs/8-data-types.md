# Data Types in Go

This comprehensive guide covers all data types in Go, from basic types to complex structures, including their internal representations, performance characteristics, and best practices.

## Table of Contents

1. [Type System Overview](#type-system-overview)
2. [Basic Types](#basic-types)
3. [Numeric Types](#numeric-types)
4. [String Type](#string-type)
5. [Boolean Type](#boolean-type)
6. [Composite Types](#composite-types)
7. [Arrays](#arrays)
8. [Slices](#slices)
9. [Maps](#maps)
10. [Structs](#structs)
11. [Pointers](#pointers)
12. [Interfaces](#interfaces)
13. [Channels](#channels)
14. [Functions as Types](#functions-as-types)
15. [Type Conversion](#type-conversion)
16. [Type Assertions](#type-assertions)
17. [Custom Types](#custom-types)
18. [Type Embedding](#type-embedding)
19. [Memory Layout](#memory-layout)
20. [Performance Considerations](#performance-considerations)

## Type System Overview

Go has a static type system with strong typing and explicit type conversion. Understanding the type system is crucial for writing efficient and safe Go code.

### Type Categories

```go
// Basic types (built-in)
var number int = 42
var text string = "hello"
var flag bool = true

// Composite types (constructed from other types)
var numbers []int = []int{1, 2, 3}
var person struct{ name string; age int }
var mapping map[string]int = make(map[string]int)

// Reference types (contain references to underlying data)
var slice []int        // Reference to array
var channel chan int   // Reference to channel
var pointer *int       // Reference to memory location
var iface interface{}  // Reference to concrete type

// Function types
var fn func(int) string
```

### Type Safety

```go
// Go enforces strict type safety
var i int = 42
var f float64 = 3.14

// i = f        // Error: cannot use float64 as int
i = int(f)      // OK: explicit conversion

// No implicit conversions between different types
var i32 int32 = 42
var i64 int64 = 100

// i32 = i64    // Error: cannot use int64 as int32
i32 = int32(i64)  // OK: explicit conversion
```

### Zero Values

Every type in Go has a zero value - the value a variable has when declared but not initialized:

```go
var i int           // 0
var f float64       // 0.0
var b bool          // false
var s string        // ""
var p *int          // nil
var slice []int     // nil
var m map[string]int // nil
var ch chan int     // nil
var fn func()       // nil
var iface interface{} // nil

// Composite types have zero values too
var arr [3]int      // [0, 0, 0]
var person struct {
    name string
    age  int
}                   // {name: "", age: 0}
```

## Basic Types

Go's basic types are the building blocks for all other types.

### Numeric Types Overview

```go
// Signed integers
int8    // -128 to 127
int16   // -32,768 to 32,767
int32   // -2,147,483,648 to 2,147,483,647
int64   // -9,223,372,036,854,775,808 to 9,223,372,036,854,775,807
int     // Platform dependent (32 or 64 bit)

// Unsigned integers
uint8   // 0 to 255
uint16  // 0 to 65,535
uint32  // 0 to 4,294,967,295
uint64  // 0 to 18,446,744,073,709,551,615
uint    // Platform dependent (32 or 64 bit)

// Floating-point numbers
float32 // IEEE-754 32-bit floating-point
float64 // IEEE-754 64-bit floating-point

// Complex numbers
complex64  // Complex number with float32 real and imaginary parts
complex128 // Complex number with float64 real and imaginary parts

// Aliases
byte    // Alias for uint8
rune    // Alias for int32 (represents Unicode code point)
```

### Type Aliases vs Type Definitions

```go
// Type alias (Go 1.9+)
type MyInt = int        // MyInt is exactly the same as int
type MyString = string  // MyString is exactly the same as string

var i MyInt = 42
var j int = 100
i = j                   // OK: MyInt and int are the same type

// Type definition (creates new type)
type UserID int         // UserID is a new type based on int
type ProductID int      // ProductID is a new type based on int

var uid UserID = 123
var pid ProductID = 456
// uid = pid            // Error: cannot use ProductID as UserID
uid = UserID(pid)       // OK: explicit conversion
```

## Numeric Types

Numeric types in Go are designed for performance and precision.

### Integer Types

```go
// Platform-dependent integers
var i int = 42          // 32-bit on 32-bit systems, 64-bit on 64-bit systems
var u uint = 42         // 32-bit on 32-bit systems, 64-bit on 64-bit systems

// Fixed-size integers
var i8 int8 = 127       // 1 byte
var i16 int16 = 32767   // 2 bytes
var i32 int32 = 2147483647  // 4 bytes
var i64 int64 = 9223372036854775807  // 8 bytes

// Unsigned integers
var u8 uint8 = 255      // 1 byte
var u16 uint16 = 65535  // 2 bytes
var u32 uint32 = 4294967295  // 4 bytes
var u64 uint64 = 18446744073709551615  // 8 bytes

// Special integer types
var b byte = 255        // Alias for uint8, used for raw data
var r rune = 'A'        // Alias for int32, used for Unicode code points
```

### Integer Overflow and Underflow

```go
// Integer overflow wraps around
var u8 uint8 = 255
u8++                    // u8 becomes 0 (wraps around)

var i8 int8 = 127
i8++                    // i8 becomes -128 (wraps around)

// Detecting overflow
func addUint8(a, b uint8) (uint8, bool) {
    result := a + b
    overflow := result < a  // If result is less than a, overflow occurred
    return result, overflow
}

// Safe arithmetic
import "math"

func safeAddInt(a, b int) (int, error) {
    if b > 0 && a > math.MaxInt-b {
        return 0, errors.New("integer overflow")
    }
    if b < 0 && a < math.MinInt-b {
        return 0, errors.New("integer underflow")
    }
    return a + b, nil
}
```

### Floating-Point Types

```go
// Floating-point precision
var f32 float32 = 3.14159265359  // Limited precision
var f64 float64 = 3.14159265359  // Higher precision

fmt.Printf("float32: %.10f\n", f32)  // 3.1415927410
fmt.Printf("float64: %.10f\n", f64)  // 3.1415926536

// Special floating-point values
import "math"

var posInf = math.Inf(1)    // Positive infinity
var negInf = math.Inf(-1)   // Negative infinity
var notANumber = math.NaN() // Not a Number

// Checking special values
if math.IsInf(posInf, 1) {
    fmt.Println("Positive infinity")
}
if math.IsNaN(notANumber) {
    fmt.Println("Not a number")
}
```

### Complex Numbers

```go
// Complex number creation
var c64 complex64 = 1 + 2i
var c128 complex128 = 3 + 4i

// Using complex() function
var c1 = complex(1.0, 2.0)      // complex128
var c2 = complex(float32(1), float32(2))  // complex64

// Extracting real and imaginary parts
realPart := real(c128)          // 3.0
imagPart := imag(c128)          // 4.0

// Complex arithmetic
var a = 1 + 2i
var b = 3 + 4i
sum := a + b                    // 4 + 6i
product := a * b                // -5 + 10i

// Magnitude and phase
import "math/cmplx"
magnitude := cmplx.Abs(a)       // sqrt(1² + 2²) = sqrt(5)
phase := cmplx.Phase(a)         // atan2(2, 1)
```

## String Type

Strings in Go are immutable sequences of bytes, typically containing UTF-8 encoded text.

### String Internals

```go
// String structure (conceptual)
type StringHeader struct {
    Data uintptr  // Pointer to underlying byte array
    Len  int      // Length in bytes
}

// String literals
var s1 = "Hello, World!"           // Regular string
var s2 = `Raw string with
multiple lines and \n escapes`     // Raw string literal

// String properties
var text = "Hello, 世界"
fmt.Println(len(text))             // 13 (bytes, not characters)
fmt.Println(utf8.RuneCountInString(text))  // 9 (Unicode characters)
```

### String Operations

```go
// String concatenation
var first = "Hello"
var second = "World"
var combined = first + ", " + second  // "Hello, World"

// Efficient string building
import "strings"

var builder strings.Builder
builder.WriteString("Hello")
builder.WriteString(", ")
builder.WriteString("World")
result := builder.String()           // "Hello, World"

// String comparison
var a = "apple"
var b = "banana"
fmt.Println(a < b)                   // true (lexicographic order)
fmt.Println(a == b)                  // false

// String contains, prefix, suffix
fmt.Println(strings.Contains("hello world", "world"))  // true
fmt.Println(strings.HasPrefix("hello", "he"))          // true
fmt.Println(strings.HasSuffix("world", "ld"))          // true
```

### Runes and UTF-8

```go
// Rune (Unicode code point)
var r rune = 'A'                     // 65
var unicode rune = '世'              // 19990

// String to runes conversion
var text = "Hello, 世界"
runes := []rune(text)                // Convert to slice of runes
fmt.Println(len(runes))              // 9 (Unicode characters)

// Iterating over string
for i, r := range text {
    fmt.Printf("Index %d: %c (U+%04X)\n", i, r, r)
}

// Byte vs rune iteration
text := "世界"
// Byte iteration
for i := 0; i < len(text); i++ {
    fmt.Printf("Byte %d: %d\n", i, text[i])
}
// Rune iteration
for i, r := range text {
    fmt.Printf("Rune at byte %d: %c\n", i, r)
}
```

### String Conversion

```go
// String to []byte and back
var s = "Hello"
var bytes = []byte(s)                // Convert to byte slice
var back = string(bytes)             // Convert back to string

// String to []rune and back
var runes = []rune(s)                // Convert to rune slice
var backFromRunes = string(runes)    // Convert back to string

// Numeric conversions
import "strconv"

var num = 42
var str = strconv.Itoa(num)          // "42"
var parsed, err = strconv.Atoi(str)  // 42, nil

var f = 3.14159
var fStr = strconv.FormatFloat(f, 'f', 2, 64)  // "3.14"
var parsedF, err = strconv.ParseFloat(fStr, 64) // 3.14, nil
```

## Boolean Type

The boolean type represents truth values.

```go
// Boolean literals
var isTrue bool = true
var isFalse bool = false
var defaultBool bool                 // false (zero value)

// Boolean operations
var a = true
var b = false

var and = a && b                     // false
var or = a || b                      // true
var not = !a                         // false

// Short-circuit evaluation
func expensiveOperation() bool {
    fmt.Println("Expensive operation called")
    return true
}

var result = false && expensiveOperation()  // expensiveOperation not called
var result2 = true || expensiveOperation()  // expensiveOperation not called

// Boolean in conditions
if isTrue {
    fmt.Println("This will execute")
}

// Comparison operations return bool
var x = 10
var y = 20
var greater = x > y                  // false
var equal = x == y                   // false
var notEqual = x != y                // true
```

## Composite Types

Composite types are constructed from other types.

### Arrays

Arrays are fixed-size sequences of elements of the same type.

```go
// Array declaration and initialization
var arr1 [5]int                      // [0, 0, 0, 0, 0]
var arr2 = [5]int{1, 2, 3, 4, 5}     // [1, 2, 3, 4, 5]
var arr3 = [...]int{1, 2, 3}         // [1, 2, 3] (compiler counts)

// Partial initialization
var arr4 = [5]int{1, 3}              // [1, 0, 3, 0, 0]
var arr5 = [5]int{0: 10, 2: 20, 4: 30}  // [10, 0, 20, 0, 30]

// Array operations
fmt.Println(len(arr2))               // 5
fmt.Println(arr2[0])                 // 1
arr2[0] = 100                        // Modify element

// Array comparison
var a = [3]int{1, 2, 3}
var b = [3]int{1, 2, 3}
fmt.Println(a == b)                  // true

// Arrays are values (copied when assigned)
var original = [3]int{1, 2, 3}
var copy = original                  // Copies the entire array
copy[0] = 100
fmt.Println(original[0])             // 1 (unchanged)
fmt.Println(copy[0])                 // 100
```

### Slices

Slices are dynamic arrays that provide a more flexible interface to sequences.

```go
// Slice creation
var slice1 []int                     // nil slice
var slice2 = []int{1, 2, 3, 4, 5}    // Slice literal
var slice3 = make([]int, 5)          // Length 5, capacity 5, all zeros
var slice4 = make([]int, 3, 10)      // Length 3, capacity 10

// Slice from array
var arr = [5]int{1, 2, 3, 4, 5}
var slice5 = arr[1:4]                // [2, 3, 4]
var slice6 = arr[:3]                 // [1, 2, 3]
var slice7 = arr[2:]                 // [3, 4, 5]
var slice8 = arr[:]                  // [1, 2, 3, 4, 5]

// Slice properties
fmt.Println(len(slice2))             // 5 (length)
fmt.Println(cap(slice2))             // 5 (capacity)

// Slice operations
slice2 = append(slice2, 6)           // Add element
slice2 = append(slice2, 7, 8, 9)     // Add multiple elements
slice2 = append(slice2, []int{10, 11}...)  // Append another slice

// Slice copying
var source = []int{1, 2, 3, 4, 5}
var dest = make([]int, len(source))
copy(dest, source)                   // Copy elements
```

### Slice Internals

```go
// Slice structure (conceptual)
type SliceHeader struct {
    Data uintptr  // Pointer to underlying array
    Len  int      // Current length
    Cap  int      // Capacity
}

// Slice growth
var s []int
for i := 0; i < 1000; i++ {
    s = append(s, i)
    // Go automatically grows the slice when capacity is exceeded
    // Growth strategy: typically doubles capacity when needed
}

// Manual capacity management
var s = make([]int, 0, 1000)         // Pre-allocate capacity
for i := 0; i < 1000; i++ {
    s = append(s, i)                 // No reallocation needed
}
```

### Maps

Maps are key-value data structures (hash tables).

```go
// Map creation
var map1 map[string]int              // nil map
var map2 = make(map[string]int)      // Empty map
var map3 = map[string]int{           // Map literal
    "apple":  5,
    "banana": 3,
    "orange": 8,
}

// Map operations
map2["key1"] = 100                   // Set value
value := map2["key1"]                // Get value
value, exists := map2["key1"]        // Get value with existence check

delete(map2, "key1")                 // Delete key
fmt.Println(len(map3))               // Number of key-value pairs

// Map iteration
for key, value := range map3 {
    fmt.Printf("%s: %d\n", key, value)
}

// Keys only
for key := range map3 {
    fmt.Println(key)
}

// Values only
for _, value := range map3 {
    fmt.Println(value)
}
```

### Map Internals

```go
// Maps are reference types
var original = map[string]int{"a": 1, "b": 2}
var reference = original             // Both point to same underlying map
reference["c"] = 3
fmt.Println(original["c"])           // 3 (original is modified)

// Map zero value is nil
var nilMap map[string]int
// nilMap["key"] = 1                 // Panic: assignment to entry in nil map

// Check for nil map
if nilMap == nil {
    nilMap = make(map[string]int)
}
nilMap["key"] = 1                    // Safe

// Map key requirements
// Keys must be comparable (==, !=)
// Valid key types: basic types, arrays, structs with comparable fields
// Invalid key types: slices, maps, functions
```

### Structs

Structs group related data together.

```go
// Struct definition
type Person struct {
    Name    string
    Age     int
    Email   string
    IsActive bool
}

// Struct variable declaration
var person Person                     // Zero value struct
var person = Person{}                 // Same as above

// Struct initialization
var person = Person{
    Name:     "John Doe",
    Age:      30,
    Email:    "<EMAIL>",
    IsActive: true,
}

// Positional initialization (not recommended)
var person = Person{"John Doe", 30, "<EMAIL>", true}

// Partial initialization
var person = Person{Name: "John", Age: 30}  // Email: "", IsActive: false

// Accessing struct fields
fmt.Println(person.Name)              // "John"
person.Age = 31                       // Modify field
```

### Struct Memory Layout

```go
// Struct padding and alignment
type Example struct {
    a bool    // 1 byte
    // 7 bytes padding
    b int64   // 8 bytes
    c bool    // 1 byte
    // 7 bytes padding
}
// Total: 24 bytes (not 10 bytes)

// Optimized struct layout
type OptimizedExample struct {
    b int64   // 8 bytes
    a bool    // 1 byte
    c bool    // 1 byte
    // 6 bytes padding
}
// Total: 16 bytes

// Check struct size
import "unsafe"
fmt.Println(unsafe.Sizeof(Example{}))          // 24
fmt.Println(unsafe.Sizeof(OptimizedExample{})) // 16
```

## Pointers

Pointers are a fundamental type in Go that store memory addresses of other variables.

### Pointer Type System

```go
// Pointer type syntax
*T                      // Pointer to type T
**T                     // Pointer to pointer to type T
*[5]int                 // Pointer to array of 5 ints
*[]int                  // Pointer to slice
*map[string]int         // Pointer to map
*struct{ name string }  // Pointer to struct
*func(int) string       // Pointer to function

// Pointer declaration
var p *int              // Pointer to int, zero value is nil
var s *string           // Pointer to string, zero value is nil
var arr *[5]int         // Pointer to array
var slice *[]int        // Pointer to slice
```

### Pointer Operations

```go
// Address operator (&)
var x int = 42
var p *int = &x         // p points to x's memory address

// Dereference operator (*)
fmt.Println(*p)         // 42 (value at address p points to)
*p = 100               // Changes x to 100
fmt.Println(x)         // 100

// Pointer to pointer
var pp **int = &p      // Pointer to pointer to int
fmt.Println(**pp)      // 100

// nil pointer
var nilPtr *int        // nil
if nilPtr == nil {
    fmt.Println("Pointer is nil")
}
```

### Pointer Memory Model

```go
// Memory layout conceptual representation
type PointerHeader struct {
    Addr uintptr        // Memory address (8 bytes on 64-bit)
}

// Pointer size is always the same regardless of pointed type
var pInt *int           // 8 bytes on 64-bit system
var pString *string     // 8 bytes on 64-bit system
var pStruct *LargeStruct // 8 bytes on 64-bit system

// Check pointer size
import "unsafe"
fmt.Println(unsafe.Sizeof(pInt))     // 8 (on 64-bit)
fmt.Println(unsafe.Sizeof(pString))  // 8 (on 64-bit)
fmt.Println(unsafe.Sizeof(pStruct))  // 8 (on 64-bit)
```

### Pointer Safety

```go
// Nil pointer safety
func safePointerUsage() {
    var p *int

    // Always check for nil before dereferencing
    if p != nil {
        fmt.Println(*p)     // Safe
    }

    // Safe initialization
    var x int = 42
    p = &x
    fmt.Println(*p)         // Safe: 42

    // Using new() function
    p = new(int)            // Allocates zero value and returns pointer
    *p = 42
    fmt.Println(*p)         // Safe: 42
}

// Common pointer mistakes
func pointerMistakes() {
    // Mistake 1: Dereferencing nil pointer
    var p *int
    // fmt.Println(*p)      // PANIC: runtime error

    // Mistake 2: Comparing pointers incorrectly
    var a, b int = 42, 42
    var pa, pb *int = &a, &b
    fmt.Println(pa == pb)   // false (different addresses)
    fmt.Println(*pa == *pb) // true (same values)
}
```

### Pointer Arithmetic (Not Allowed)

```go
// Go does NOT support pointer arithmetic (unlike C/C++)
func noPointerArithmetic() {
    arr := [3]int{1, 2, 3}
    p := &arr[0]

    // p++                  // Error: invalid operation
    // p += 1               // Error: invalid operation
    // p - q                // Error: invalid operation

    // Use slices for sequential access instead
    slice := arr[:]
    for i, v := range slice {
        fmt.Printf("Index %d: %d\n", i, v)
    }
}

// Use unsafe package for pointer arithmetic (dangerous)
import "unsafe"

func unsafePointerArithmetic() {
    arr := [3]int{1, 2, 3}
    p := unsafe.Pointer(&arr[0])

    // Move to next element (dangerous!)
    p = unsafe.Pointer(uintptr(p) + unsafe.Sizeof(arr[0]))
    nextValue := *(*int)(p)     // 2

    // This is dangerous and not recommended!
}
```

### Pointer vs Value Semantics

```go
// Value semantics (copy)
func valueSemantics() {
    type Person struct {
        Name string
        Age  int
    }

    var p1 = Person{Name: "John", Age: 30}
    var p2 = p1                 // Copy entire struct
    p2.Age = 31                 // Only p2 is modified

    fmt.Println(p1.Age)         // 30 (unchanged)
    fmt.Println(p2.Age)         // 31 (modified)
}

// Pointer semantics (reference)
func pointerSemantics() {
    type Person struct {
        Name string
        Age  int
    }

    var p1 = &Person{Name: "John", Age: 30}
    var p2 = p1                 // Copy pointer (same object)
    p2.Age = 31                 // Modifies the same object

    fmt.Println(p1.Age)         // 31 (modified)
    fmt.Println(p2.Age)         // 31 (same object)
}
```

### Pointer Performance Considerations

```go
// Large struct - prefer pointers to avoid copying
type LargeStruct struct {
    data [1000]int
    // ... many fields
}

// Inefficient: copies entire struct
func processValueLarge(ls LargeStruct) {
    // Process struct (1000 * 8 = 8000 bytes copied)
}

// Efficient: passes pointer
func processPointerLarge(ls *LargeStruct) {
    // Process struct (8 bytes copied)
}

// Small struct - value semantics might be better
type SmallStruct struct {
    x, y int
}

// For small structs, value semantics can be more efficient
func processValueSmall(ss SmallStruct) {
    // Only 16 bytes copied, no indirection
}

func processPointerSmall(ss *SmallStruct) {
    // 8 bytes copied, but requires indirection
}
```

## Interfaces

Interfaces define method sets and enable polymorphism in Go.

```go
// Interface definition
type Writer interface {
    Write([]byte) (int, error)
}

type Reader interface {
    Read([]byte) (int, error)
}

// Interface composition
type ReadWriter interface {
    Reader
    Writer
}

// Empty interface (can hold any type)
var anything interface{}
anything = 42
anything = "hello"
anything = []int{1, 2, 3}

// Interface implementation is implicit
type MyWriter struct{}

func (mw MyWriter) Write(data []byte) (int, error) {
    // Implementation
    return len(data), nil
}

// MyWriter automatically implements Writer interface
var w Writer = MyWriter{}
```

### Interface Internals

```go
// Interface structure (conceptual)
type InterfaceHeader struct {
    Type  *Type        // Pointer to type information
    Value unsafe.Pointer // Pointer to actual value
}

// Interface with methods (non-empty interface)
type iface struct {
    tab  *itab         // Interface table
    data unsafe.Pointer // Pointer to value
}

// Empty interface
type eface struct {
    _type *_type       // Type information
    data  unsafe.Pointer // Pointer to value
}
```

## Channels

Channels are Go's way of communicating between goroutines.

```go
// Channel types
chan T          // Bidirectional channel
chan<- T        // Send-only channel
<-chan T        // Receive-only channel

// Channel creation
var ch chan int                    // nil channel
var ch = make(chan int)            // Unbuffered channel
var ch = make(chan int, 10)        // Buffered channel with capacity 10

// Channel operations
ch <- 42                           // Send value
value := <-ch                      // Receive value
value, ok := <-ch                  // Receive with ok check
close(ch)                          // Close channel

// Channel direction restrictions
func sender(ch chan<- int) {       // Can only send
    ch <- 42
}

func receiver(ch <-chan int) {     // Can only receive
    value := <-ch
}
```

## Functions as Types

Functions are first-class types in Go.

```go
// Function type declaration
type HandlerFunc func(http.ResponseWriter, *http.Request)
type ProcessorFunc func(data []byte) ([]byte, error)
type ValidatorFunc func(value string) bool

// Function variables
var handler HandlerFunc
var processor ProcessorFunc

// Function literals (anonymous functions)
var add = func(a, b int) int {
    return a + b
}

// Higher-order functions
func applyOperation(a, b int, op func(int, int) int) int {
    return op(a, b)
}

result := applyOperation(5, 3, add)        // 8
result = applyOperation(5, 3, func(x, y int) int {
    return x * y
})                                         // 15
```

## Type Conversion

Go requires explicit type conversion between different types, ensuring type safety.

### Basic Type Conversion

```go
// Numeric type conversions
var i int = 42
var f float64 = float64(i)         // int to float64
var u uint = uint(i)               // int to uint
var b byte = byte(i)               // int to byte (if value fits)

// Floating-point conversions
var f32 float32 = 3.14
var f64 float64 = float64(f32)     // float32 to float64
var i2 int = int(f64)              // float64 to int (truncates)

// String conversions
var s string = string(i)           // int to string (Unicode code point)
var s2 string = strconv.Itoa(i)    // int to string representation
var num, err = strconv.Atoi(s2)    // string to int

// Slice conversions
var str = "hello"
var bytes []byte = []byte(str)     // string to []byte
var str2 string = string(bytes)    // []byte to string

// Rune conversions
var r rune = 'A'                   // rune literal
var i3 int32 = int32(r)            // rune to int32 (same underlying type)
var s3 string = string(r)          // rune to string
```

### Type Conversion Rules

```go
// 1. Conversion between numeric types
var i8 int8 = 100
var i16 int16 = int16(i8)          // Explicit conversion required
var f float32 = float32(i16)       // int to float

// 2. Conversion between custom types
type Celsius float64
type Fahrenheit float64

var c Celsius = 100
var f Fahrenheit = Fahrenheit(c * 9/5 + 32)  // Explicit conversion required

// 3. Cannot convert between incompatible types
type Person struct{ Name string }
type Employee struct{ Name string }

var p Person = Person{Name: "John"}
// var e Employee = Employee(p)    // Error: cannot convert

// Must use field-by-field assignment or conversion function
var e Employee = Employee{Name: p.Name}

// 4. Pointer conversions
var i int = 42
var pi *int = &i
var pf *float64 = (*float64)(unsafe.Pointer(pi))  // Unsafe conversion
```

### Conversion Safety and Overflow

```go
// Conversion can cause overflow/underflow
var big int64 = 1000000000000
var small int8 = int8(big)         // Overflow: result is unpredictable

// Safe conversion with bounds checking
func safeInt64ToInt8(val int64) (int8, error) {
    if val < math.MinInt8 || val > math.MaxInt8 {
        return 0, fmt.Errorf("value %d out of int8 range", val)
    }
    return int8(val), nil
}

// Floating-point precision loss
var precise float64 = 3.141592653589793
var less float32 = float32(precise)  // Precision loss

// String to numeric conversion errors
var str = "not a number"
var num, err = strconv.Atoi(str)     // err != nil
if err != nil {
    // Handle conversion error
}
```

### Performance Considerations

```go
// String to []byte conversion copies data
var str = "hello world"
var bytes = []byte(str)              // Copies string data

// Use unsafe for zero-copy conversion (dangerous)
import "unsafe"

func stringToBytes(s string) []byte {
    return *(*[]byte)(unsafe.Pointer(
        &struct {
            string
            Cap int
        }{s, len(s)},
    ))
}

// []byte to string conversion also copies
var bytes = []byte("hello")
var str = string(bytes)              // Copies byte data

// Zero-copy []byte to string (dangerous)
func bytesToString(b []byte) string {
    return *(*string)(unsafe.Pointer(&b))
}
```

## Type Assertions

Type assertions are used to extract concrete types from interface values.

### Basic Type Assertions

```go
// Type assertion syntax: value.(Type)
var i interface{} = "hello"

// Basic type assertion (panics if wrong type)
s := i.(string)                      // s = "hello"
fmt.Println(s)

// Safe type assertion (returns value and boolean)
s, ok := i.(string)                  // s = "hello", ok = true
if ok {
    fmt.Println("i is a string:", s)
} else {
    fmt.Println("i is not a string")
}

// Type assertion with wrong type
var i2 interface{} = 42
// s2 := i2.(string)                 // Panic: interface conversion
s2, ok := i2.(string)                // s2 = "", ok = false
```

### Type Assertion Examples

```go
// Working with different types
func processValue(val interface{}) {
    switch v := val.(type) {
    case string:
        fmt.Printf("String: %s (length: %d)\n", v, len(v))
    case int:
        fmt.Printf("Integer: %d (squared: %d)\n", v, v*v)
    case bool:
        fmt.Printf("Boolean: %t\n", v)
    case []int:
        fmt.Printf("Slice of ints: %v (sum: %d)\n", v, sum(v))
    case nil:
        fmt.Println("nil value")
    default:
        fmt.Printf("Unknown type: %T, value: %v\n", v, v)
    }
}

// Usage examples
processValue("hello")                // String: hello (length: 5)
processValue(42)                     // Integer: 42 (squared: 1764)
processValue(true)                   // Boolean: true
processValue([]int{1, 2, 3})         // Slice of ints: [1 2 3] (sum: 6)
processValue(nil)                    // nil value
processValue(3.14)                   // Unknown type: float64, value: 3.14
```

### Type Switch

```go
// Type switch is a special form of type assertion
func typeSwitch(i interface{}) {
    switch v := i.(type) {
    case int:
        fmt.Printf("Integer: %d\n", v)
    case string:
        fmt.Printf("String: %s\n", v)
    case bool:
        fmt.Printf("Boolean: %t\n", v)
    case []int:
        fmt.Printf("Slice of ints: %v\n", v)
    case map[string]int:
        fmt.Printf("Map: %v\n", v)
    case func(int) int:
        fmt.Printf("Function: %v\n", v)
    case nil:
        fmt.Println("nil value")
    default:
        fmt.Printf("Unknown type: %T\n", v)
    }
}

// Multiple types in one case
func multiTypeSwitch(i interface{}) {
    switch i.(type) {
    case int, int8, int16, int32, int64:
        fmt.Println("Integer type")
    case uint, uint8, uint16, uint32, uint64:
        fmt.Println("Unsigned integer type")
    case float32, float64:
        fmt.Println("Floating-point type")
    case string:
        fmt.Println("String type")
    default:
        fmt.Println("Other type")
    }
}
```

### Interface Type Assertions

```go
// Asserting to interface types
type Writer interface {
    Write([]byte) (int, error)
}

type Closer interface {
    Close() error
}

type WriteCloser interface {
    Writer
    Closer
}

func handleWriter(w Writer) {
    // Check if Writer also implements Closer
    if closer, ok := w.(Closer); ok {
        defer closer.Close()
        fmt.Println("Writer also implements Closer")
    }

    // Check if Writer implements WriteCloser
    if wc, ok := w.(WriteCloser); ok {
        defer wc.Close()
        fmt.Println("Writer implements WriteCloser")
    }

    // Use the writer
    w.Write([]byte("hello"))
}
```

### Type Assertion Performance

```go
// Type assertions have runtime cost
func benchmarkTypeAssertion(val interface{}) {
    // Direct type assertion
    if s, ok := val.(string); ok {
        _ = s
    }

    // Type switch (more efficient for multiple types)
    switch v := val.(type) {
    case string:
        _ = v
    case int:
        _ = v
    case bool:
        _ = v
    }
}

// Avoid repeated type assertions
func inefficient(val interface{}) {
    if s, ok := val.(string); ok {
        fmt.Println(len(s))
    }
    if s, ok := val.(string); ok {  // Repeated assertion
        fmt.Println(strings.ToUpper(s))
    }
}

func efficient(val interface{}) {
    if s, ok := val.(string); ok {
        fmt.Println(len(s))
        fmt.Println(strings.ToUpper(s))  // Reuse s
    }
}
```
