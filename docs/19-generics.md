# Generics in Go

This comprehensive guide covers generics in Go (introduced in Go 1.18), including type parameters, constraints, generic functions, types, and advanced patterns for writing reusable, type-safe code.

## Table of Contents

1. [Overview](#overview)
2. [Basic Type Parameters](#basic-type-parameters)
3. [Type Constraints](#type-constraints)
4. [Generic Functions](#generic-functions)
5. [Generic Types](#generic-types)
6. [Interface Constraints](#interface-constraints)
7. [Type Inference](#type-inference)
8. [Generic Data Structures](#generic-data-structures)
9. [Generic Algorithms](#generic-algorithms)
10. [Advanced Patterns](#advanced-patterns)
11. [Performance Considerations](#performance-considerations)
12. [Best Practices](#best-practices)
13. [Common Pitfalls](#common-pitfalls)
14. [Migration Strategies](#migration-strategies)

## Overview

Generics allow you to write functions and types that work with multiple types while maintaining type safety. They eliminate the need for interface{} and type assertions in many cases.

### Key Benefits

```go
// Before generics: Using interface{} loses type safety
func PrintSlice(items []interface{}) {
    for _, item := range items {
        fmt.Println(item)
    }
}

// With generics: Type-safe and reusable
func PrintSlice[T any](items []T) {
    for _, item := range items {
        fmt.Println(item)
    }
}
```

### Generics vs Other Languages

| Feature | Go Generics | Java Generics | C++ Templates |
|---------|-------------|---------------|---------------|
| **Type Erasure** | No | Yes | No |
| **Compile-time** | Yes | Partial | Yes |
| **Constraints** | Interface-based | Bounded wildcards | Concepts (C++20) |
| **Inference** | Yes | Limited | Yes |
| **Specialization** | Automatic | Manual | Automatic |

## Basic Type Parameters

Understanding the fundamental syntax and concepts of type parameters.

### Type Parameter Syntax

```go
// Basic type parameter syntax
func GenericFunction[T any](param T) T {
    return param
}

// Multiple type parameters
func Pair[T, U any](first T, second U) (T, U) {
    return first, second
}

// Type parameter with constraint
func Add[T ~int | ~float64](a, b T) T {
    return a + b
}

// Usage examples
func demonstrateBasicTypeParameters() {
    // Type inference
    result1 := GenericFunction(42)        // T inferred as int
    result2 := GenericFunction("hello")   // T inferred as string

    // Explicit type specification
    result3 := GenericFunction[bool](true)

    // Multiple type parameters
    pair := Pair(10, "ten")              // T=int, U=string

    // Constrained type parameter
    sum1 := Add(5, 3)                    // Works with int
    sum2 := Add(5.5, 3.2)               // Works with float64

    fmt.Printf("Results: %v, %v, %v, %v, %v, %v\n",
        result1, result2, result3, pair, sum1, sum2)
}
```

### Type Parameter Lists

```go
// Single type parameter
func Identity[T any](value T) T {
    return value
}

// Multiple type parameters
func Convert[From, To any](value From, converter func(From) To) To {
    return converter(value)
}

// Type parameters with different constraints
func Process[T comparable, U any](key T, value U) map[T]U {
    result := make(map[T]U)
    result[key] = value
    return result
}

// Type parameter with multiple constraints
func Compare[T comparable](a, b T) bool {
    return a == b
}

// Usage
func demonstrateTypeParameterLists() {
    // Identity function
    num := Identity(42)
    str := Identity("hello")

    // Converter function
    intToString := func(i int) string { return fmt.Sprintf("%d", i) }
    converted := Convert(123, intToString)

    // Process function
    processed := Process("key", 100)

    // Compare function
    equal := Compare(10, 10)
    different := Compare("a", "b")

    fmt.Printf("Identity: %v, %v\n", num, str)
    fmt.Printf("Converted: %v\n", converted)
    fmt.Printf("Processed: %v\n", processed)
    fmt.Printf("Comparisons: %v, %v\n", equal, different)
}
```

## Type Constraints

Defining and using constraints to limit type parameters.

### Built-in Constraints

```go
// any constraint (equivalent to interface{})
func PrintAny[T any](value T) {
    fmt.Printf("Value: %v, Type: %T\n", value, value)
}

// comparable constraint
func FindIndex[T comparable](slice []T, target T) int {
    for i, item := range slice {
        if item == target {
            return i
        }
    }
    return -1
}

// Usage
func demonstrateBuiltinConstraints() {
    // any constraint works with any type
    PrintAny(42)
    PrintAny("hello")
    PrintAny([]int{1, 2, 3})

    // comparable constraint works with comparable types
    numbers := []int{1, 2, 3, 4, 5}
    index := FindIndex(numbers, 3)

    strings := []string{"a", "b", "c"}
    strIndex := FindIndex(strings, "b")

    fmt.Printf("Found at index: %d, %d\n", index, strIndex)
}
```

### Custom Constraints

```go
// Numeric constraint using type union
type Numeric interface {
    ~int | ~int8 | ~int16 | ~int32 | ~int64 |
    ~uint | ~uint8 | ~uint16 | ~uint32 | ~uint64 |
    ~float32 | ~float64
}

// Ordered constraint
type Ordered interface {
    ~int | ~int8 | ~int16 | ~int32 | ~int64 |
    ~uint | ~uint8 | ~uint16 | ~uint32 | ~uint64 |
    ~float32 | ~float64 | ~string
}

// Signed integer constraint
type Signed interface {
    ~int | ~int8 | ~int16 | ~int32 | ~int64
}

// Functions using custom constraints
func Sum[T Numeric](numbers []T) T {
    var sum T
    for _, num := range numbers {
        sum += num
    }
    return sum
}

func Max[T Ordered](a, b T) T {
    if a > b {
        return a
    }
    return b
}

func Abs[T Signed](value T) T {
    if value < 0 {
        return -value
    }
    return value
}

// Usage
func demonstrateCustomConstraints() {
    // Numeric constraint
    intSum := Sum([]int{1, 2, 3, 4, 5})
    floatSum := Sum([]float64{1.1, 2.2, 3.3})

    // Ordered constraint
    maxInt := Max(10, 20)
    maxString := Max("apple", "banana")

    // Signed constraint
    absValue := Abs(-42)

    fmt.Printf("Sums: %v, %v\n", intSum, floatSum)
    fmt.Printf("Max values: %v, %v\n", maxInt, maxString)
    fmt.Printf("Absolute value: %v\n", absValue)
}
```

### Constraint Composition

```go
// Combining constraints
type StringLike interface {
    ~string
}

type Printable interface {
    String() string
}

type PrintableString interface {
    StringLike
    Printable
}

// Method constraint
type Stringer interface {
    String() string
}

type Lengther interface {
    Len() int
}

type StringerLengther interface {
    Stringer
    Lengther
}

// Functions with composed constraints
func ProcessString[T StringLike](s T) T {
    // Can use string operations on T
    return T(strings.ToUpper(string(s)))
}

func PrintWithLength[T StringerLengther](item T) {
    fmt.Printf("Item: %s, Length: %d\n", item.String(), item.Len())
}

// Example type that satisfies StringerLengther
type MyString string

func (s MyString) String() string {
    return string(s)
}

func (s MyString) Len() int {
    return len(s)
}

// Usage
func demonstrateConstraintComposition() {
    // StringLike constraint
    processed := ProcessString("hello world")
    fmt.Printf("Processed: %v\n", processed)

    // StringerLengther constraint
    myStr := MyString("example")
    PrintWithLength(myStr)
}
```

## Generic Functions

Creating reusable functions with type parameters.

### Basic Generic Functions

```go
// Generic utility functions
func Map[T, U any](slice []T, fn func(T) U) []U {
    result := make([]U, len(slice))
    for i, item := range slice {
        result[i] = fn(item)
    }
    return result
}

func Filter[T any](slice []T, predicate func(T) bool) []T {
    var result []T
    for _, item := range slice {
        if predicate(item) {
            result = append(result, item)
        }
    }
    return result
}

func Reduce[T, U any](slice []T, initial U, fn func(U, T) U) U {
    result := initial
    for _, item := range slice {
        result = fn(result, item)
    }
    return result
}

func Contains[T comparable](slice []T, target T) bool {
    for _, item := range slice {
        if item == target {
            return true
        }
    }
    return false
}

// Usage examples
func demonstrateGenericFunctions() {
    numbers := []int{1, 2, 3, 4, 5}

    // Map: transform each element
    doubled := Map(numbers, func(n int) int { return n * 2 })
    strings := Map(numbers, func(n int) string { return fmt.Sprintf("num_%d", n) })

    // Filter: select elements
    evens := Filter(numbers, func(n int) bool { return n%2 == 0 })

    // Reduce: aggregate elements
    sum := Reduce(numbers, 0, func(acc, n int) int { return acc + n })
    product := Reduce(numbers, 1, func(acc, n int) int { return acc * n })

    // Contains: check membership
    hasThree := Contains(numbers, 3)
    hasTen := Contains(numbers, 10)

    fmt.Printf("Doubled: %v\n", doubled)
    fmt.Printf("Strings: %v\n", strings)
    fmt.Printf("Evens: %v\n", evens)
    fmt.Printf("Sum: %d, Product: %d\n", sum, product)
    fmt.Printf("Has 3: %t, Has 10: %t\n", hasThree, hasTen)
}
```

### Generic Functions with Multiple Constraints

```go
// Function with multiple type parameters and constraints
func SortedKeys[K Ordered, V any](m map[K]V) []K {
    keys := make([]K, 0, len(m))
    for k := range m {
        keys = append(keys, k)
    }

    // Sort keys
    sort.Slice(keys, func(i, j int) bool {
        return keys[i] < keys[j]
    })

    return keys
}

// Generic function with method constraint
func ProcessItems[T Stringer](items []T) []string {
    result := make([]string, len(items))
    for i, item := range items {
        result[i] = item.String()
    }
    return result
}

// Generic function with multiple constraints
func FindMinMax[T Ordered](slice []T) (T, T, error) {
    if len(slice) == 0 {
        var zero T
        return zero, zero, errors.New("empty slice")
    }

    min, max := slice[0], slice[0]
    for _, item := range slice[1:] {
        if item < min {
            min = item
        }
        if item > max {
            max = item
        }
    }

    return min, max, nil
}

// Usage
func demonstrateMultipleConstraints() {
    // SortedKeys function
    scores := map[string]int{
        "Alice": 95,
        "Bob":   87,
        "Charlie": 92,
    }
    sortedNames := SortedKeys(scores)
    fmt.Printf("Sorted names: %v\n", sortedNames)

    // FindMinMax function
    numbers := []int{3, 1, 4, 1, 5, 9, 2, 6}
    min, max, err := FindMinMax(numbers)
    if err == nil {
        fmt.Printf("Min: %d, Max: %d\n", min, max)
    }

    words := []string{"apple", "banana", "cherry"}
    minWord, maxWord, _ := FindMinMax(words)
    fmt.Printf("Min word: %s, Max word: %s\n", minWord, maxWord)
}
```

## Generic Types

Creating generic structs, interfaces, and type definitions.

### Generic Structs

```go
// Generic container types
type Pair[T, U any] struct {
    First  T
    Second U
}

func (p Pair[T, U]) String() string {
    return fmt.Sprintf("(%v, %v)", p.First, p.Second)
}

func (p Pair[T, U]) Swap() Pair[U, T] {
    return Pair[U, T]{First: p.Second, Second: p.First}
}

// Generic optional type
type Optional[T any] struct {
    value *T
}

func Some[T any](value T) Optional[T] {
    return Optional[T]{value: &value}
}

func None[T any]() Optional[T] {
    return Optional[T]{value: nil}
}

func (o Optional[T]) IsSome() bool {
    return o.value != nil
}

func (o Optional[T]) IsNone() bool {
    return o.value == nil
}

func (o Optional[T]) Unwrap() T {
    if o.value == nil {
        panic("called Unwrap on None value")
    }
    return *o.value
}

func (o Optional[T]) UnwrapOr(defaultValue T) T {
    if o.value == nil {
        return defaultValue
    }
    return *o.value
}

// Generic result type
type Result[T, E any] struct {
    value T
    err   E
    isOk  bool
}

func Ok[T, E any](value T) Result[T, E] {
    return Result[T, E]{value: value, isOk: true}
}

func Err[T, E any](err E) Result[T, E] {
    return Result[T, E]{err: err, isOk: false}
}

func (r Result[T, E]) IsOk() bool {
    return r.isOk
}

func (r Result[T, E]) IsErr() bool {
    return !r.isOk
}

func (r Result[T, E]) Unwrap() T {
    if !r.isOk {
        panic("called Unwrap on Err value")
    }
    return r.value
}

func (r Result[T, E]) UnwrapErr() E {
    if r.isOk {
        panic("called UnwrapErr on Ok value")
    }
    return r.err
}

// Usage
func demonstrateGenericTypes() {
    // Pair type
    intStringPair := Pair[int, string]{First: 42, Second: "answer"}
    swapped := intStringPair.Swap()
    fmt.Printf("Original: %v, Swapped: %v\n", intStringPair, swapped)

    // Optional type
    someValue := Some(100)
    noneValue := None[int]()

    fmt.Printf("Some value: %d\n", someValue.UnwrapOr(0))
    fmt.Printf("None value: %d\n", noneValue.UnwrapOr(42))

    // Result type
    okResult := Ok[string, error]("success")
    errResult := Err[string, error](errors.New("failure"))

    if okResult.IsOk() {
        fmt.Printf("Success: %s\n", okResult.Unwrap())
    }

    if errResult.IsErr() {
        fmt.Printf("Error: %v\n", errResult.UnwrapErr())
    }
}
```

### Generic Interfaces

```go
// Generic interface definitions
type Container[T any] interface {
    Add(item T)
    Remove(item T) bool
    Contains(item T) bool
    Size() int
    Clear()
}

type Iterator[T any] interface {
    Next() (T, bool)
    HasNext() bool
}

type Comparable[T any] interface {
    CompareTo(other T) int
}

// Generic interface with type constraints
type Numeric[T ~int | ~float64] interface {
    Add(other T) T
    Subtract(other T) T
    Multiply(other T) T
    Divide(other T) T
}

// Implementation of generic interface
type SimpleList[T comparable] struct {
    items []T
}

func NewSimpleList[T comparable]() *SimpleList[T] {
    return &SimpleList[T]{items: make([]T, 0)}
}

func (sl *SimpleList[T]) Add(item T) {
    sl.items = append(sl.items, item)
}

func (sl *SimpleList[T]) Remove(item T) bool {
    for i, existing := range sl.items {
        if existing == item {
            sl.items = append(sl.items[:i], sl.items[i+1:]...)
            return true
        }
    }
    return false
}

func (sl *SimpleList[T]) Contains(item T) bool {
    for _, existing := range sl.items {
        if existing == item {
            return true
        }
    }
    return false
}

func (sl *SimpleList[T]) Size() int {
    return len(sl.items)
}

func (sl *SimpleList[T]) Clear() {
    sl.items = sl.items[:0]
}

// Usage
func demonstrateGenericInterfaces() {
    // Using generic interface implementation
    var container Container[string] = NewSimpleList[string]()

    container.Add("apple")
    container.Add("banana")
    container.Add("cherry")

    fmt.Printf("Size: %d\n", container.Size())
    fmt.Printf("Contains banana: %t\n", container.Contains("banana"))

    container.Remove("banana")
    fmt.Printf("Size after removal: %d\n", container.Size())
    fmt.Printf("Contains banana: %t\n", container.Contains("banana"))
}
```

## Interface Constraints

Advanced constraint patterns using interfaces.

### Method-based Constraints

```go
// Interface constraint with methods
type Serializable interface {
    Serialize() ([]byte, error)
    Deserialize([]byte) error
}

type Validator interface {
    Validate() error
}

type Cacheable interface {
    CacheKey() string
    TTL() time.Duration
}

// Combined interface constraints
type Entity interface {
    Serializable
    Validator
    Cacheable
}

// Generic function using method constraints
func ProcessEntity[T Entity](entity T) error {
    // Validate first
    if err := entity.Validate(); err != nil {
        return fmt.Errorf("validation failed: %w", err)
    }

    // Serialize
    data, err := entity.Serialize()
    if err != nil {
        return fmt.Errorf("serialization failed: %w", err)
    }

    // Cache with key and TTL
    cacheKey := entity.CacheKey()
    ttl := entity.TTL()

    fmt.Printf("Processed entity: key=%s, size=%d bytes, ttl=%v\n",
        cacheKey, len(data), ttl)

    return nil
}

// Example implementation
type User struct {
    ID   int    `json:"id"`
    Name string `json:"name"`
    Email string `json:"email"`
}

func (u User) Serialize() ([]byte, error) {
    return json.Marshal(u)
}

func (u *User) Deserialize(data []byte) error {
    return json.Unmarshal(data, u)
}

func (u User) Validate() error {
    if u.Name == "" {
        return errors.New("name is required")
    }
    if u.Email == "" {
        return errors.New("email is required")
    }
    return nil
}

func (u User) CacheKey() string {
    return fmt.Sprintf("user:%d", u.ID)
}

func (u User) TTL() time.Duration {
    return 5 * time.Minute
}

// Usage
func demonstrateMethodConstraints() {
    user := User{
        ID:    1,
        Name:  "Alice",
        Email: "<EMAIL>",
    }

    if err := ProcessEntity(user); err != nil {
        fmt.Printf("Error: %v\n", err)
    }
}
```

## Type Inference

Understanding how Go infers generic types automatically.

### Automatic Type Inference

```go
// Functions with type inference
func Identity[T any](value T) T {
    return value
}

func MakePair[T, U any](first T, second U) Pair[T, U] {
    return Pair[T, U]{First: first, Second: second}
}

func demonstrateTypeInference() {
    // Type inference from arguments
    num := Identity(42)           // T inferred as int
    str := Identity("hello")      // T inferred as string
    slice := Identity([]int{1, 2, 3}) // T inferred as []int

    // Multiple type inference
    pair1 := MakePair(10, "ten")     // T=int, U=string
    pair2 := MakePair(3.14, true)    // T=float64, U=bool

    // Explicit type specification when needed
    explicit := Identity[float64](42) // Explicitly specify T=float64

    fmt.Printf("Inferred types: %T, %T, %T\n", num, str, slice)
    fmt.Printf("Pairs: %v, %v\n", pair1, pair2)
    fmt.Printf("Explicit: %v (%T)\n", explicit, explicit)
}
```

### Partial Type Inference

```go
// Function with mixed inference
func Convert[From, To any](value From, converter func(From) To) To {
    return converter(value)
}

func ProcessWithDefault[T any, U comparable](items []T, processor func(T) U, defaultValue U) []U {
    result := make([]U, len(items))
    for i, item := range items {
        processed := processor(item)
        if processed == defaultValue {
            result[i] = defaultValue
        } else {
            result[i] = processed
        }
    }
    return result
}

func demonstratePartialInference() {
    // From inferred, To specified
    result1 := Convert(42, func(i int) string { return fmt.Sprintf("%d", i) })

    // T inferred, U inferred from defaultValue
    numbers := []int{1, 2, 3, 4, 5}
    doubled := ProcessWithDefault(numbers, func(n int) int { return n * 2 }, 0)

    // Sometimes explicit specification needed
    strings := []string{"a", "b", "c"}
    lengths := ProcessWithDefault[string, int](strings, func(s string) int { return len(s) }, 0)

    fmt.Printf("Converted: %v\n", result1)
    fmt.Printf("Doubled: %v\n", doubled)
    fmt.Printf("Lengths: %v\n", lengths)
}
```

## Generic Data Structures

Building reusable data structures with generics.

### Generic Stack

```go
type Stack[T any] struct {
    items []T
}

func NewStack[T any]() *Stack[T] {
    return &Stack[T]{items: make([]T, 0)}
}

func (s *Stack[T]) Push(item T) {
    s.items = append(s.items, item)
}

func (s *Stack[T]) Pop() (T, bool) {
    if len(s.items) == 0 {
        var zero T
        return zero, false
    }

    index := len(s.items) - 1
    item := s.items[index]
    s.items = s.items[:index]
    return item, true
}

func (s *Stack[T]) Peek() (T, bool) {
    if len(s.items) == 0 {
        var zero T
        return zero, false
    }

    return s.items[len(s.items)-1], true
}

func (s *Stack[T]) IsEmpty() bool {
    return len(s.items) == 0
}

func (s *Stack[T]) Size() int {
    return len(s.items)
}
```

### Generic Queue

```go
type Queue[T any] struct {
    items []T
    front int
    rear  int
    size  int
}

func NewQueue[T any](capacity int) *Queue[T] {
    return &Queue[T]{
        items: make([]T, capacity),
        front: 0,
        rear:  0,
        size:  0,
    }
}

func (q *Queue[T]) Enqueue(item T) bool {
    if q.size == len(q.items) {
        return false // Queue is full
    }

    q.items[q.rear] = item
    q.rear = (q.rear + 1) % len(q.items)
    q.size++
    return true
}

func (q *Queue[T]) Dequeue() (T, bool) {
    if q.size == 0 {
        var zero T
        return zero, false
    }

    item := q.items[q.front]
    q.front = (q.front + 1) % len(q.items)
    q.size--
    return item, true
}

func (q *Queue[T]) IsEmpty() bool {
    return q.size == 0
}

func (q *Queue[T]) IsFull() bool {
    return q.size == len(q.items)
}

func (q *Queue[T]) Size() int {
    return q.size
}
```

### Generic Binary Tree

```go
type TreeNode[T any] struct {
    Value T
    Left  *TreeNode[T]
    Right *TreeNode[T]
}

type BinaryTree[T any] struct {
    root    *TreeNode[T]
    compare func(T, T) int
}

func NewBinaryTree[T any](compare func(T, T) int) *BinaryTree[T] {
    return &BinaryTree[T]{compare: compare}
}

func (bt *BinaryTree[T]) Insert(value T) {
    bt.root = bt.insertNode(bt.root, value)
}

func (bt *BinaryTree[T]) insertNode(node *TreeNode[T], value T) *TreeNode[T] {
    if node == nil {
        return &TreeNode[T]{Value: value}
    }

    cmp := bt.compare(value, node.Value)
    if cmp < 0 {
        node.Left = bt.insertNode(node.Left, value)
    } else if cmp > 0 {
        node.Right = bt.insertNode(node.Right, value)
    }

    return node
}

func (bt *BinaryTree[T]) Search(value T) bool {
    return bt.searchNode(bt.root, value)
}

func (bt *BinaryTree[T]) searchNode(node *TreeNode[T], value T) bool {
    if node == nil {
        return false
    }

    cmp := bt.compare(value, node.Value)
    if cmp == 0 {
        return true
    } else if cmp < 0 {
        return bt.searchNode(node.Left, value)
    } else {
        return bt.searchNode(node.Right, value)
    }
}

func (bt *BinaryTree[T]) InorderTraversal(visit func(T)) {
    bt.inorderNode(bt.root, visit)
}

func (bt *BinaryTree[T]) inorderNode(node *TreeNode[T], visit func(T)) {
    if node != nil {
        bt.inorderNode(node.Left, visit)
        visit(node.Value)
        bt.inorderNode(node.Right, visit)
    }
}

// Usage
func demonstrateGenericDataStructures() {
    // Generic stack
    intStack := NewStack[int]()
    intStack.Push(1)
    intStack.Push(2)
    intStack.Push(3)

    for !intStack.IsEmpty() {
        if value, ok := intStack.Pop(); ok {
            fmt.Printf("Popped: %d\n", value)
        }
    }

    // Generic queue
    stringQueue := NewQueue[string](5)
    stringQueue.Enqueue("first")
    stringQueue.Enqueue("second")
    stringQueue.Enqueue("third")

    for !stringQueue.IsEmpty() {
        if value, ok := stringQueue.Dequeue(); ok {
            fmt.Printf("Dequeued: %s\n", value)
        }
    }

    // Generic binary tree
    intTree := NewBinaryTree(func(a, b int) int {
        if a < b {
            return -1
        } else if a > b {
            return 1
        }
        return 0
    })

    values := []int{5, 3, 7, 1, 9, 4, 6}
    for _, v := range values {
        intTree.Insert(v)
    }

    fmt.Print("Inorder traversal: ")
    intTree.InorderTraversal(func(value int) {
        fmt.Printf("%d ", value)
    })
    fmt.Println()

    fmt.Printf("Search 4: %t\n", intTree.Search(4))
    fmt.Printf("Search 8: %t\n", intTree.Search(8))
}
```

## Generic Algorithms

Implementing common algorithms with generics.

### Sorting Algorithms

```go
// Generic bubble sort
func BubbleSort[T Ordered](slice []T) {
    n := len(slice)
    for i := 0; i < n-1; i++ {
        for j := 0; j < n-i-1; j++ {
            if slice[j] > slice[j+1] {
                slice[j], slice[j+1] = slice[j+1], slice[j]
            }
        }
    }
}

// Generic quick sort
func QuickSort[T Ordered](slice []T) {
    if len(slice) <= 1 {
        return
    }
    quickSortHelper(slice, 0, len(slice)-1)
}

func quickSortHelper[T Ordered](slice []T, low, high int) {
    if low < high {
        pi := partition(slice, low, high)
        quickSortHelper(slice, low, pi-1)
        quickSortHelper(slice, pi+1, high)
    }
}

func partition[T Ordered](slice []T, low, high int) int {
    pivot := slice[high]
    i := low - 1

    for j := low; j < high; j++ {
        if slice[j] <= pivot {
            i++
            slice[i], slice[j] = slice[j], slice[i]
        }
    }

    slice[i+1], slice[high] = slice[high], slice[i+1]
    return i + 1
}

// Generic sort with custom comparator
func SortWithComparator[T any](slice []T, less func(T, T) bool) {
    n := len(slice)
    for i := 0; i < n-1; i++ {
        for j := 0; j < n-i-1; j++ {
            if !less(slice[j], slice[j+1]) {
                slice[j], slice[j+1] = slice[j+1], slice[j]
            }
        }
    }
}
```

### Search Algorithms

```go
// Generic binary search
func BinarySearch[T Ordered](slice []T, target T) int {
    left, right := 0, len(slice)-1

    for left <= right {
        mid := (left + right) / 2

        if slice[mid] == target {
            return mid
        } else if slice[mid] < target {
            left = mid + 1
        } else {
            right = mid - 1
        }
    }

    return -1
}

// Generic linear search
func LinearSearch[T comparable](slice []T, target T) int {
    for i, item := range slice {
        if item == target {
            return i
        }
    }
    return -1
}

// Generic search with predicate
func SearchWithPredicate[T any](slice []T, predicate func(T) bool) int {
    for i, item := range slice {
        if predicate(item) {
            return i
        }
    }
    return -1
}

// Usage
func demonstrateGenericAlgorithms() {
    // Sorting
    numbers := []int{64, 34, 25, 12, 22, 11, 90}
    fmt.Printf("Original: %v\n", numbers)

    numbersCopy := make([]int, len(numbers))
    copy(numbersCopy, numbers)
    BubbleSort(numbersCopy)
    fmt.Printf("Bubble sorted: %v\n", numbersCopy)

    copy(numbersCopy, numbers)
    QuickSort(numbersCopy)
    fmt.Printf("Quick sorted: %v\n", numbersCopy)

    // Custom comparator sort
    words := []string{"banana", "apple", "cherry", "date"}
    SortWithComparator(words, func(a, b string) bool {
        return len(a) < len(b) // Sort by length
    })
    fmt.Printf("Sorted by length: %v\n", words)

    // Searching
    sortedNumbers := []int{1, 3, 5, 7, 9, 11, 13, 15}
    index := BinarySearch(sortedNumbers, 7)
    fmt.Printf("Binary search for 7: index %d\n", index)

    linearIndex := LinearSearch([]string{"a", "b", "c", "d"}, "c")
    fmt.Printf("Linear search for 'c': index %d\n", linearIndex)

    // Search with predicate
    people := []struct{ Name string; Age int }{
        {"Alice", 25},
        {"Bob", 30},
        {"Charlie", 35},
    }

    adultIndex := SearchWithPredicate(people, func(p struct{ Name string; Age int }) bool {
        return p.Age >= 30
    })
    fmt.Printf("First adult at index: %d\n", adultIndex)
}
```

## Best Practices

Guidelines for effective use of generics in Go.

### When to Use Generics

```go
// Good: Use generics for type-safe containers
type SafeMap[K comparable, V any] struct {
    mu   sync.RWMutex
    data map[K]V
}

func NewSafeMap[K comparable, V any]() *SafeMap[K, V] {
    return &SafeMap[K, V]{
        data: make(map[K]V),
    }
}

func (sm *SafeMap[K, V]) Set(key K, value V) {
    sm.mu.Lock()
    defer sm.mu.Unlock()
    sm.data[key] = value
}

func (sm *SafeMap[K, V]) Get(key K) (V, bool) {
    sm.mu.RLock()
    defer sm.mu.RUnlock()
    value, exists := sm.data[key]
    return value, exists
}

// Good: Use generics for algorithms that work with multiple types
func Reverse[T any](slice []T) {
    for i, j := 0, len(slice)-1; i < j; i, j = i+1, j-1 {
        slice[i], slice[j] = slice[j], slice[i]
    }
}

// Avoid: Don't use generics for simple functions that don't benefit
// Bad example:
func BadPrint[T any](value T) {
    fmt.Println(value) // fmt.Println already accepts interface{}
}

// Good alternative:
func Print(value interface{}) {
    fmt.Println(value)
}
```

### Constraint Design

```go
// Good: Use meaningful constraint names
type Numeric interface {
    ~int | ~int8 | ~int16 | ~int32 | ~int64 |
    ~uint | ~uint8 | ~uint16 | ~uint32 | ~uint64 |
    ~float32 | ~float64
}

type Addable interface {
    ~int | ~int8 | ~int16 | ~int32 | ~int64 |
    ~uint | ~uint8 | ~uint16 | ~uint32 | ~uint64 |
    ~float32 | ~float64 | ~string
}

// Good: Compose constraints when appropriate
type SignedInteger interface {
    ~int | ~int8 | ~int16 | ~int32 | ~int64
}

type UnsignedInteger interface {
    ~uint | ~uint8 | ~uint16 | ~uint32 | ~uint64
}

type Integer interface {
    SignedInteger | UnsignedInteger
}

// Avoid: Overly complex constraints
// Bad:
type OverlyComplex[T interface {
    ~int | ~string
    fmt.Stringer
    comparable
    interface{ Method1(); Method2(); Method3() }
}] struct {
    value T
}

// Good: Break down complex constraints
type Stringable interface {
    fmt.Stringer
    comparable
}

type WithMethods interface {
    Method1()
    Method2()
    Method3()
}

type SimpleConstraint interface {
    ~int | ~string
}

type BetterComplex[T interface {
    SimpleConstraint
    Stringable
    WithMethods
}] struct {
    value T
}
```

### Generic Function Design

```go
// Good: Clear, focused generic functions
func Clone[T any](slice []T) []T {
    if slice == nil {
        return nil
    }
    result := make([]T, len(slice))
    copy(result, slice)
    return result
}

func Unique[T comparable](slice []T) []T {
    seen := make(map[T]bool)
    var result []T

    for _, item := range slice {
        if !seen[item] {
            seen[item] = true
            result = append(result, item)
        }
    }

    return result
}

// Good: Use type parameters for return types
func Zero[T any]() T {
    var zero T
    return zero
}

func Ptr[T any](value T) *T {
    return &value
}

// Avoid: Too many type parameters
// Bad:
func OverParameterized[T, U, V, W, X, Y, Z any](
    a T, b U, c V, d W, e X, f Y, g Z,
) (T, U, V, W, X, Y, Z) {
    return a, b, c, d, e, f, g
}

// Good: Group related parameters
func ProcessPair[T, U any](first T, second U) (T, U) {
    return first, second
}

func ProcessTriple[T any](first, second, third T) (T, T, T) {
    return first, second, third
}
```

## Common Pitfalls

Avoiding common mistakes when using generics.

### Type Inference Issues

```go
// Problem: Ambiguous type inference
func Ambiguous[T any](value interface{}) T {
    return value.(T) // Type assertion needed
}

// Usage that fails:
// result := Ambiguous(42) // Cannot infer T

// Solution: Explicit type specification
func demonstrateTypeInferenceIssues() {
    // Must specify type explicitly
    result := Ambiguous[int](42)
    fmt.Printf("Result: %v\n", result)

    // Or redesign function to avoid ambiguity
    result2 := ConvertTo[int](42)
    fmt.Printf("Result2: %v\n", result2)
}

func ConvertTo[T any](value interface{}) T {
    return value.(T)
}
```

### Zero Value Handling

```go
// Problem: Zero values in generic code
func FindFirst[T comparable](slice []T, target T) T {
    for _, item := range slice {
        if item == target {
            return item
        }
    }
    // Problem: returning zero value without indication of not found
    var zero T
    return zero
}

// Solution: Return additional boolean or use Optional
func FindFirstSafe[T comparable](slice []T, target T) (T, bool) {
    for _, item := range slice {
        if item == target {
            return item, true
        }
    }
    var zero T
    return zero, false
}

func FindFirstOptional[T comparable](slice []T, target T) Optional[T] {
    for _, item := range slice {
        if item == target {
            return Some(item)
        }
    }
    return None[T]()
}
```

### Constraint Violations

```go
// Problem: Constraint too restrictive
func BadSum[T int | float64](numbers []T) T { // Only int and float64
    var sum T
    for _, num := range numbers {
        sum += num
    }
    return sum
}

// Solution: Use broader constraint
func GoodSum[T Numeric](numbers []T) T {
    var sum T
    for _, num := range numbers {
        sum += num
    }
    return sum
}

// Problem: Missing constraint
func BadCompare[T any](a, b T) bool {
    return a == b // Compile error: T is not comparable
}

// Solution: Add comparable constraint
func GoodCompare[T comparable](a, b T) bool {
    return a == b
}
```

### Performance Pitfalls

```go
// Problem: Unnecessary boxing/unboxing
func SlowProcess[T any](items []interface{}) []T {
    result := make([]T, len(items))
    for i, item := range items {
        result[i] = item.(T) // Type assertion for each item
    }
    return result
}

// Solution: Use proper generic types from start
func FastProcess[T any](items []T) []T {
    result := make([]T, len(items))
    copy(result, items) // No type assertions needed
    return result
}

// Problem: Inefficient constraint checking
func SlowConstraintCheck[T Numeric](value T) bool {
    // Complex constraint checking at runtime
    switch any(value).(type) {
    case int, int8, int16, int32, int64,
         uint, uint8, uint16, uint32, uint64,
         float32, float64:
        return true
    default:
        return false
    }
}

// Solution: Trust the constraint system
func FastConstraintCheck[T Numeric](value T) bool {
    // Constraint is checked at compile time
    return true // If it compiles, constraint is satisfied
}
```

## Performance Considerations

Understanding the performance implications of generics.

### Compilation Performance

```go
// Generic functions are instantiated for each type used
func GenericFunction[T any](value T) T {
    // This function will be compiled separately for each type T used
    return value
}

// Usage creates multiple instantiations
func demonstrateInstantiation() {
    GenericFunction(42)        // Instantiation for int
    GenericFunction("hello")   // Instantiation for string
    GenericFunction(3.14)      // Instantiation for float64
    GenericFunction([]int{})   // Instantiation for []int

    // Each instantiation increases binary size
}

// Strategy: Use interfaces when type safety isn't critical
func NonGenericFunction(value interface{}) interface{} {
    return value // Single function, smaller binary
}
```

### Runtime Performance

```go
import "testing"

// Benchmark generic vs interface{} performance
func BenchmarkGenericSum(b *testing.B) {
    numbers := make([]int, 1000)
    for i := range numbers {
        numbers[i] = i
    }

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        _ = Sum(numbers) // Generic function
    }
}

func BenchmarkInterfaceSum(b *testing.B) {
    numbers := make([]interface{}, 1000)
    for i := range numbers {
        numbers[i] = i
    }

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        _ = InterfaceSum(numbers) // Interface{} function
    }
}

func InterfaceSum(numbers []interface{}) int {
    sum := 0
    for _, num := range numbers {
        sum += num.(int) // Type assertion overhead
    }
    return sum
}

// Generic version is typically faster due to:
// - No type assertions
// - Better compiler optimizations
// - No boxing/unboxing overhead
```

### Memory Considerations

```go
// Generic types have same memory layout as non-generic equivalents
type GenericPair[T, U any] struct {
    First  T
    Second U
}

type IntStringPair struct {
    First  int
    Second string
}

func demonstrateMemoryLayout() {
    genericPair := GenericPair[int, string]{First: 42, Second: "hello"}
    regularPair := IntStringPair{First: 42, Second: "hello"}

    // Both have identical memory layout
    fmt.Printf("Generic size: %d\n", unsafe.Sizeof(genericPair))
    fmt.Printf("Regular size: %d\n", unsafe.Sizeof(regularPair))
    // Sizes will be identical
}

// Memory optimization: Use constraints to limit instantiations
type LimitedNumeric interface {
    ~int | ~float64 // Only allow int and float64
}

func OptimizedSum[T LimitedNumeric](numbers []T) T {
    // Only two instantiations possible
    var sum T
    for _, num := range numbers {
        sum += num
    }
    return sum
}
```

## Migration Strategies

Strategies for adopting generics in existing codebases.

### Gradual Migration

```go
// Step 1: Identify candidates for generics
// Before: Type-specific functions
func ReverseInts(slice []int) {
    for i, j := 0, len(slice)-1; i < j; i, j = i+1, j-1 {
        slice[i], slice[j] = slice[j], slice[i]
    }
}

func ReverseStrings(slice []string) {
    for i, j := 0, len(slice)-1; i < j; i, j = i+1, j-1 {
        slice[i], slice[j] = slice[j], slice[i]
    }
}

// Step 2: Create generic version
func ReverseGeneric[T any](slice []T) {
    for i, j := 0, len(slice)-1; i < j; i, j = i+1, j-1 {
        slice[i], slice[j] = slice[j], slice[i]
    }
}

// Step 3: Maintain backward compatibility
func ReverseIntsCompat(slice []int) {
    ReverseGeneric(slice) // Delegate to generic version
}

func ReverseStringsCompat(slice []string) {
    ReverseGeneric(slice) // Delegate to generic version
}
```

### Interface{} to Generics Migration

```go
// Before: Using interface{}
type OldContainer struct {
    items []interface{}
}

func (oc *OldContainer) Add(item interface{}) {
    oc.items = append(oc.items, item)
}

func (oc *OldContainer) Get(index int) interface{} {
    if index < 0 || index >= len(oc.items) {
        return nil
    }
    return oc.items[index]
}

// After: Generic version
type NewContainer[T any] struct {
    items []T
}

func (nc *NewContainer[T]) Add(item T) {
    nc.items = append(nc.items, item)
}

func (nc *NewContainer[T]) Get(index int) (T, bool) {
    if index < 0 || index >= len(nc.items) {
        var zero T
        return zero, false
    }
    return nc.items[index], true
}

// Migration wrapper
func MigrateContainer[T any](old *OldContainer) *NewContainer[T] {
    new := &NewContainer[T]{}
    for _, item := range old.items {
        if typedItem, ok := item.(T); ok {
            new.Add(typedItem)
        }
    }
    return new
}
```

### Testing Migration

```go
// Test both old and new implementations
func TestMigration(t *testing.T) {
    // Test old implementation
    oldContainer := &OldContainer{}
    oldContainer.Add(1)
    oldContainer.Add(2)
    oldContainer.Add(3)

    // Test new implementation
    newContainer := &NewContainer[int]{}
    newContainer.Add(1)
    newContainer.Add(2)
    newContainer.Add(3)

    // Test migration
    migratedContainer := MigrateContainer[int](oldContainer)

    // Verify results are equivalent
    for i := 0; i < 3; i++ {
        oldValue := oldContainer.Get(i)
        newValue, ok := newContainer.Get(i)
        migratedValue, migratedOk := migratedContainer.Get(i)

        if !ok || !migratedOk {
            t.Errorf("Failed to get item at index %d", i)
        }

        if oldValue != newValue || newValue != migratedValue {
            t.Errorf("Values don't match at index %d: old=%v, new=%v, migrated=%v",
                i, oldValue, newValue, migratedValue)
        }
    }
}
```

This comprehensive guide covers all aspects of generics in Go, from basic concepts to advanced patterns and migration strategies. Understanding these concepts will help you write more type-safe, reusable, and maintainable Go code.
