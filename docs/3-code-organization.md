# Go Code Organization and Project Structure

This chapter covers how <PERSON> organizes code, manages dependencies, and structures projects. Understanding these concepts is crucial for building maintainable Go applications.

## Table of Contents

1. [Go Modules vs GOPATH](#go-modules-vs-gopath)
2. [Go Modules (Modern Approach)](#go-modules-modern-approach)
3. [GOPATH Workspace (Legacy)](#gopath-workspace-legacy)
4. [Environment Variables](#environment-variables)
5. [Project Structure Best Practices](#project-structure-best-practices)
6. [Package System](#package-system)
7. [Go Program Structure](#go-program-structure)

## Go Modules vs GOPATH

Go has evolved significantly in how it manages code organization and dependencies:

### **Go Modules (Go 1.11+) - Current Standard**
- **Introduced**: Go 1.11 (2018)
- **Default since**: Go 1.13 (2019)
- **Benefits**:
  - Project can be located anywhere on your filesystem
  - Versioned dependency management
  - Reproducible builds
  - No need for GOPATH for most development

### **GOPATH Workspace - Legacy Approach**
- **Used before**: Go 1.11
- **Still supported**: For specific use cases
- **Limitations**:
  - All Go code must be in a single workspace
  - No built-in dependency versioning
  - Less flexible project organization

## Go Modules (Modern Approach)

### What is a Go Module?

A **Go module** is a collection of related Go packages that are versioned together as a single unit. It's defined by a `go.mod` file at the root of the project.

### Creating a New Module

```bash
# Create a new directory for your project
mkdir my-go-project
cd my-go-project

# Initialize a new module
go mod init example.com/my-go-project

# This creates a go.mod file
```

### Module Structure Example

```
my-go-project/
├── go.mod              # Module definition
├── go.sum              # Dependency checksums
├── main.go             # Main application
├── internal/           # Private packages
│   └── config/
│       └── config.go
├── pkg/                # Public packages
│   └── utils/
│       └── utils.go
└── cmd/                # Application entry points
    ├── server/
    │   └── main.go
    └── client/
        └── main.go
```

### go.mod File Structure

```go
module example.com/my-go-project

go 1.21

require (
    github.com/gorilla/mux v1.8.0
    github.com/lib/pq v1.10.9
)

require (
    github.com/gorilla/context v1.1.1 // indirect
)
```

### Working with Dependencies

```bash
# Add a dependency
go get github.com/gorilla/mux

# Add a specific version
go get github.com/gorilla/mux@v1.8.0

# Update dependencies
go get -u ./...

# Remove unused dependencies
go mod tidy

# Download dependencies
go mod download

# Verify dependencies
go mod verify
```

### Module Commands Reference

| Command | Description |
|---------|-------------|
| `go mod init <module-name>` | Initialize a new module |
| `go mod tidy` | Add missing and remove unused modules |
| `go mod download` | Download modules to local cache |
| `go mod verify` | Verify dependencies have expected content |
| `go mod graph` | Print module requirement graph |
| `go mod why <package>` | Explain why packages are needed |
| `go mod edit` | Edit go.mod from tools or scripts |

## GOPATH Workspace (Legacy)

### Understanding GOPATH

**GOPATH** is an environment variable that defines the root of your Go workspace. While not required for module-based development, understanding it is still valuable.

### Default GOPATH Locations

- **Windows**: `C:\Users\<USER>\go`
- **macOS/Linux**: `$HOME/go` or `~/go`

### GOPATH Directory Structure

```
$GOPATH/
├── src/                    # Source code
│   ├── github.com/
│   │   └── username/
│   │       └── project/
│   ├── gitlab.com/
│   └── example.com/
├── pkg/                    # Compiled packages (.a files)
│   └── linux_amd64/
│       └── github.com/
└── bin/                    # Compiled executables
    ├── myapp
    └── tool
```

### GOPATH Directories Explained

#### **src/** - Source Code
- Contains all Go source code
- Organized by import path
- Your code and third-party packages

#### **pkg/** - Compiled Packages
- Contains compiled package objects (`.a` files)
- Organized by OS and architecture
- Used to speed up compilation
- Automatically managed by Go tools

#### **bin/** - Executables
- Contains compiled executable programs
- Programs installed with `go install`
- Should be added to your system PATH

### Working with GOPATH

```bash
# Check current GOPATH
go env GOPATH

# Set GOPATH (if needed)
export GOPATH=$HOME/go

# Add Go bin to PATH
export PATH=$PATH:$(go env GOPATH)/bin
```

### **Go Programı Temel Yapısı**

1. **Her `.go` dosyası `package` tanımıyla başlar.**
    - Örneğin: `package main`
    - `main` paketi özel bir pakettir, **çalıştırılabilir (executable)** bir uygulama üretir.
    - `main` paketi **`func main()`** fonksiyonunu içermelidir. Bu programın giriş noktasıdır.
2. **Import Bölümü:**
    - Standart kütüphaneler veya harici paketler buradan içe aktarılır.
    - Örnek: `import "fmt"`
3. **Değişken, sabit ve fonksiyon tanımlamaları:**

    ```go
    const secondsInHour = 3600
    var distance = 60.8
    ```

4. **Ana Fonksiyon:**

    ```go
    func main() {
        fmt.Println("Hello Go world!!")
        distance := 60.8
        fmt.Printf("The distance in miles is %f\n", distance * 0.621)
    }
    ```

    - `fmt.Println()` → Ekrana yazı yazar
    - `fmt.Printf()` → Formatlı çıktı verir, `%f` bir **verb**’dür (float yazım biçimi)