# Go kod organizaston

- **<PERSON><PERSON><PERSON><PERSON> (Go 1.13+):**
    
    Yeni Go sürümleriyle birlikte modül sistemi geldiği için kodunuzu diskinizde istediğiniz gibi organize edebilirsiniz. Ancak, eğitim amaçlı olarak tek bir **çalışma alanı (workspace)** kullanılacaktır.
    
- **Çalışma Alanı (Workspace) Nedir?**
    
    Kodların bulunduğu klasördür.
    
    - Varsayılan yolu: `GOPATH` ortam değişkeninde tanımlıdır.
        - **Windows:** `C:\Users\<USER>\go`
        - **Mac/Linux:** `~/go`
- **`go env` Komutu:**
    
    Tüm Go ortam değişkenlerini terminalde listeler. Bu sayede `GOPATH` gibi bilgileri öğrenebilirsiniz.
    
- **Workspace İçeriği:**
    
    GOPATH içindeki klasörler:
    
    - `src`: Go kaynak <PERSON>ı (senin ve dış kütüphaneler)
    - `pkg`: <PERSON>lenmiş Go paketleri (`.a` uzantılı), <PERSON><PERSON><PERSON><PERSON>alıştırılamaz
    - `bin`: Derlenmiş çalıştırılabilir programlar
- **Örnek Klasör Yapısı:**
    
    Eğitim boyunca kullanılacak kodlar için:
    
    - `src/MasterGoProgramming` adlı bir klasör oluşturulacak
    - Her örnek kendi alt klasöründe olacak
    - Dosya ve klasör adlarında boşluk **kullanılmamalı**, onun yerine  veya `_` tercih edilmeli
- **Klasör Oluşturma Yöntemi:**
    - Terminalden, VS Code üzerinden veya dosya gezginiyle oluşturabilirsiniz.

### **Go Programı Temel Yapısı**

1. **Her `.go` dosyası `package` tanımıyla başlar.**
    - Örneğin: `package main`
    - `main` paketi özel bir pakettir, **çalıştırılabilir (executable)** bir uygulama üretir.
    - `main` paketi **`func main()`** fonksiyonunu içermelidir. Bu programın giriş noktasıdır.
2. **Import Bölümü:**
    - Standart kütüphaneler veya harici paketler buradan içe aktarılır.
    - Örnek: `import "fmt"`
3. **Değişken, sabit ve fonksiyon tanımlamaları:**
    
    ```go
    const secondsInHour = 3600
    var distance = 60.8
    ```
    
4. **Ana Fonksiyon:**
    
    ```go
    func main() {
        fmt.Println("Hello Go world!!")
        distance := 60.8
        fmt.Printf("The distance in miles is %f\n", distance * 0.621)
    }
    ```
    
    - `fmt.Println()` → Ekrana yazı yazar
    - `fmt.Printf()` → Formatlı çıktı verir, `%f` bir **verb**’dür (float yazım biçimi)