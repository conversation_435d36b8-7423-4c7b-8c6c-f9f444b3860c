# Go Code Organization and Project Structure

This chapter covers how <PERSON> organizes code, manages dependencies, and structures projects. Understanding these concepts is crucial for building maintainable Go applications.

## Table of Contents

1. [Go Modules vs GOPATH](#go-modules-vs-gopath)
2. [Go Modules (Modern Approach)](#go-modules-modern-approach)
3. [GOPATH Workspace (Legacy)](#gopath-workspace-legacy)
4. [Environment Variables](#environment-variables)
5. [Project Structure Best Practices](#project-structure-best-practices)
6. [Package System](#package-system)
7. [Go Program Structure](#go-program-structure)

## Go Modules vs GOPATH

Go has evolved significantly in how it manages code organization and dependencies:

### **Go Modules (Go 1.11+) - Current Standard**
- **Introduced**: Go 1.11 (2018)
- **Default since**: Go 1.13 (2019)
- **Benefits**:
  - Project can be located anywhere on your filesystem
  - Versioned dependency management
  - Reproducible builds
  - No need for GOPATH for most development

### **GOPATH Workspace - Legacy Approach**
- **Used before**: Go 1.11
- **Still supported**: For specific use cases
- **Limitations**:
  - All Go code must be in a single workspace
  - No built-in dependency versioning
  - Less flexible project organization

## Go Modules (Modern Approach)

### What is a Go Module?

A **Go module** is a collection of related Go packages that are versioned together as a single unit. It's defined by a `go.mod` file at the root of the project.

### Creating a New Module

```bash
# Create a new directory for your project
mkdir my-go-project
cd my-go-project

# Initialize a new module
go mod init example.com/my-go-project

# This creates a go.mod file
```

### Module Structure Example

```
my-go-project/
├── go.mod              # Module definition
├── go.sum              # Dependency checksums
├── main.go             # Main application
├── internal/           # Private packages
│   └── config/
│       └── config.go
├── pkg/                # Public packages
│   └── utils/
│       └── utils.go
└── cmd/                # Application entry points
    ├── server/
    │   └── main.go
    └── client/
        └── main.go
```

### go.mod File Structure

```go
module example.com/my-go-project

go 1.21

require (
    github.com/gorilla/mux v1.8.0
    github.com/lib/pq v1.10.9
)

require (
    github.com/gorilla/context v1.1.1 // indirect
)
```

### Working with Dependencies

```bash
# Add a dependency
go get github.com/gorilla/mux

# Add a specific version
go get github.com/gorilla/mux@v1.8.0

# Update dependencies
go get -u ./...

# Remove unused dependencies
go mod tidy

# Download dependencies
go mod download

# Verify dependencies
go mod verify
```

### Module Commands Reference

| Command | Description |
|---------|-------------|
| `go mod init <module-name>` | Initialize a new module |
| `go mod tidy` | Add missing and remove unused modules |
| `go mod download` | Download modules to local cache |
| `go mod verify` | Verify dependencies have expected content |
| `go mod graph` | Print module requirement graph |
| `go mod why <package>` | Explain why packages are needed |
| `go mod edit` | Edit go.mod from tools or scripts |

## GOPATH Workspace (Legacy)

### Understanding GOPATH

**GOPATH** is an environment variable that defines the root of your Go workspace. While not required for module-based development, understanding it is still valuable.

### Default GOPATH Locations

- **Windows**: `C:\Users\<USER>\go`
- **macOS/Linux**: `$HOME/go` or `~/go`

### GOPATH Directory Structure

```
$GOPATH/
├── src/                    # Source code
│   ├── github.com/
│   │   └── username/
│   │       └── project/
│   ├── gitlab.com/
│   └── example.com/
├── pkg/                    # Compiled packages (.a files)
│   └── linux_amd64/
│       └── github.com/
└── bin/                    # Compiled executables
    ├── myapp
    └── tool
```

### GOPATH Directories Explained

#### **src/** - Source Code
- Contains all Go source code
- Organized by import path
- Your code and third-party packages

#### **pkg/** - Compiled Packages
- Contains compiled package objects (`.a` files)
- Organized by OS and architecture
- Used to speed up compilation
- Automatically managed by Go tools

#### **bin/** - Executables
- Contains compiled executable programs
- Programs installed with `go install`
- Should be added to your system PATH

### Working with GOPATH

```bash
# Check current GOPATH
go env GOPATH

# Set GOPATH (if needed)
export GOPATH=$HOME/go

# Add Go bin to PATH
export PATH=$PATH:$(go env GOPATH)/bin
```

## Environment Variables

### Core Go Environment Variables

#### **GOROOT**
- **Purpose**: Location where Go is installed
- **Default**: Automatically detected
- **Example**: `/usr/local/go`

```bash
# Check GOROOT
go env GOROOT
```

#### **GOPATH**
- **Purpose**: Workspace root (legacy mode)
- **Default**: `$HOME/go`
- **Usage**: Less important with modules

```bash
# Check GOPATH
go env GOPATH

# Set custom GOPATH
export GOPATH=/custom/path/to/workspace
```

#### **GOOS and GOARCH**
- **Purpose**: Target operating system and architecture
- **Usage**: Cross-compilation

```bash
# Check current values
go env GOOS GOARCH

# Cross-compile for different platforms
GOOS=windows GOARCH=amd64 go build
GOOS=linux GOARCH=arm64 go build
GOOS=darwin GOARCH=amd64 go build
```

#### **GO111MODULE**
- **Purpose**: Controls module behavior
- **Values**:
  - `on`: Always use modules
  - `off`: Never use modules (GOPATH mode)
  - `auto`: Use modules if go.mod exists (default)

```bash
# Check module mode
go env GO111MODULE

# Force module mode
export GO111MODULE=on
```

#### **GOPROXY**
- **Purpose**: Module proxy for downloading dependencies
- **Default**: `https://proxy.golang.org,direct`

```bash
# Check proxy settings
go env GOPROXY

# Use private proxy
export GOPROXY=https://your-proxy.com,direct
```

#### **GOSUMDB**
- **Purpose**: Checksum database for module verification
- **Default**: `sum.golang.org`

```bash
# Check sumdb
go env GOSUMDB

# Disable sumdb (not recommended)
export GOSUMDB=off
```

#### **GOPRIVATE**
- **Purpose**: Private module patterns
- **Usage**: Skip proxy and sumdb for private repos

```bash
# Set private modules
export GOPRIVATE=github.com/yourcompany/*,gitlab.yourcompany.com/*
```

### Complete Environment Variables List

```bash
# View all Go environment variables
go env

# View specific variables
go env GOROOT GOPATH GOOS GOARCH

# Set environment variable
go env -w GOPROXY=https://your-proxy.com

# Unset environment variable
go env -u GOPROXY
```

### Common Environment Configurations

#### **Development Setup**
```bash
# ~/.bashrc or ~/.zshrc
export GOPATH=$HOME/go
export PATH=$PATH:$(go env GOROOT)/bin:$GOPATH/bin
export GO111MODULE=on
```

#### **Corporate Environment**
```bash
# Private repositories and proxy
export GOPRIVATE=github.com/yourcompany/*
export GOPROXY=https://your-proxy.com,direct
export GOSUMDB=off
```

#### **Cross-compilation Setup**
```bash
# Build for multiple platforms
#!/bin/bash
platforms=("windows/amd64" "linux/amd64" "darwin/amd64" "linux/arm64")

for platform in "${platforms[@]}"
do
    platform_split=(${platform//\// })
    GOOS=${platform_split[0]}
    GOARCH=${platform_split[1]}

    output_name='myapp'
    if [ $GOOS = "windows" ]; then
        output_name+='.exe'
    fi

    env GOOS=$GOOS GOARCH=$GOARCH go build -o $output_name
done
```

## Project Structure Best Practices

### Standard Go Project Layout

```
project/
├── cmd/                    # Main applications
│   ├── server/
│   │   └── main.go
│   └── client/
│       └── main.go
├── internal/               # Private application code
│   ├── app/
│   ├── pkg/
│   └── config/
├── pkg/                    # Public library code
│   ├── api/
│   └── utils/
├── api/                    # API definitions
│   ├── openapi/
│   └── protobuf/
├── web/                    # Web application assets
│   ├── static/
│   └── templates/
├── configs/                # Configuration files
├── deployments/            # Deployment configurations
├── docs/                   # Documentation
├── examples/               # Example code
├── scripts/                # Build and deployment scripts
├── test/                   # Test data and utilities
├── vendor/                 # Vendored dependencies (optional)
├── .gitignore
├── go.mod
├── go.sum
├── Makefile
└── README.md
```

### Directory Explanations

#### **cmd/**
- Contains main applications for the project
- Each subdirectory is an executable
- Keep main.go files small, import from internal/

#### **internal/**
- Private application and library code
- Cannot be imported by other projects
- Use for code you don't want others to import

#### **pkg/**
- Library code that can be used by external applications
- Public API of your project
- Think carefully before putting code here

#### **api/**
- API contract files (OpenAPI/Swagger, Protocol Buffers, etc.)
- API documentation

#### **web/**
- Web application specific components
- Static files, templates

### Naming Conventions

#### **Package Names**
- Use short, lowercase names
- Avoid underscores, use camelCase if needed
- Be descriptive but concise

```go
// Good
package user
package httputil
package stringutil

// Avoid
package user_management
package HTTPUtil
package string_utilities
```

#### **File Names**
- Use lowercase with underscores for separation
- Be descriptive

```
// Good
user_service.go
http_client.go
string_utils.go

// Avoid
UserService.go
HTTPClient.go
stringUtils.go
```

#### **Directory Names**
- Use lowercase
- Use hyphens for multi-word names in URLs
- Use underscores for internal directories

```
// Good
user-service/
internal/user_management/
pkg/http-client/
```

## Package System

### What is a Package?

A **package** is a collection of Go source files in the same directory that are compiled together. Every Go source file belongs to a package.

### Package Declaration

Every Go file starts with a package declaration:

```go
package main        // Executable package
package fmt         // Library package
package mypackage   // Custom package
```

### Package Types

#### **main Package**
- Special package for executable programs
- Must contain a `main()` function
- Entry point of the application

```go
package main

import "fmt"

func main() {
    fmt.Println("Hello, World!")
}
```

#### **Library Packages**
- Provide functionality to other packages
- Cannot be executed directly
- Imported by other packages

```go
package mathutil

func Add(a, b int) int {
    return a + b
}

func Multiply(a, b int) int {
    return a * b
}
```

### Importing Packages

#### **Standard Library Import**
```go
import "fmt"
import "net/http"
import "encoding/json"
```

#### **Multiple Imports**
```go
import (
    "fmt"
    "net/http"
    "encoding/json"
)
```

#### **Custom Package Import**
```go
import "example.com/myproject/pkg/utils"
import "example.com/myproject/internal/config"
```

#### **Import Aliases**
```go
import (
    "fmt"
    json "encoding/json"           // Alias
    . "math"                       // Dot import (use with caution)
    _ "github.com/lib/pq"          // Blank import (for side effects)
)
```

### Package Visibility

#### **Exported (Public)**
- Names starting with uppercase letter
- Accessible from other packages

```go
package user

type User struct {
    Name  string    // Exported
    Email string    // Exported
}

func NewUser(name, email string) *User {    // Exported
    return &User{Name: name, Email: email}
}
```

#### **Unexported (Private)**
- Names starting with lowercase letter
- Only accessible within the same package

```go
package user

type user struct {
    name  string    // Unexported
    email string    // Unexported
}

func createUser(name, email string) *user {    // Unexported
    return &user{name: name, email: email}
}
```

## Go Program Structure

### Basic Program Structure

Every Go program follows this basic structure:

```go
// 1. Package declaration
package main

// 2. Import statements
import (
    "fmt"
    "os"
)

// 3. Global variables and constants
const AppName = "MyApp"
var Version = "1.0.0"

// 4. Type definitions
type Config struct {
    Host string
    Port int
}

// 5. Function definitions
func init() {
    // Initialization code
    fmt.Println("Initializing...")
}

func main() {
    // Main program logic
    fmt.Printf("%s version %s\n", AppName, Version)
}

// 6. Other functions
func helper() {
    // Helper function
}
```

### Program Execution Order

1. **Package initialization**: All imported packages are initialized
2. **Variable initialization**: Package-level variables are initialized
3. **init() functions**: All init functions are executed
4. **main() function**: Program entry point (only in main package)

### Example: Complete Program Structure

```go
package main

import (
    "fmt"
    "log"
    "os"
)

// Constants
const (
    AppName    = "Go Example"
    AppVersion = "1.0.0"
)

// Variables
var (
    debug   bool
    logFile *os.File
)

// Types
type Application struct {
    Name    string
    Version string
    Debug   bool
}

// init function - runs before main
func init() {
    debug = os.Getenv("DEBUG") == "true"

    if debug {
        var err error
        logFile, err = os.OpenFile("app.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
        if err != nil {
            log.Fatalln("Failed to open log file:", err)
        }
        log.SetOutput(logFile)
    }
}

// main function - program entry point
func main() {
    app := Application{
        Name:    AppName,
        Version: AppVersion,
        Debug:   debug,
    }

    app.Run()

    // Cleanup
    if logFile != nil {
        logFile.Close()
    }
}

// Methods
func (a *Application) Run() {
    fmt.Printf("Starting %s v%s\n", a.Name, a.Version)

    if a.Debug {
        log.Println("Debug mode enabled")
    }

    // Application logic here
    fmt.Println("Application running...")
}
```

### Key Points

1. **Package Declaration**: Must be the first non-comment line
2. **Import Statements**: Come after package declaration
3. **Global Scope**: Constants, variables, types, and functions
4. **main Package**: Required for executable programs
5. **main Function**: Entry point for executable programs
6. **init Functions**: Run before main, used for initialization
7. **Exported Names**: Start with uppercase, accessible from other packages
8. **Unexported Names**: Start with lowercase, package-private

### File Organization Within a Package

```go
// user.go
package user

type User struct {
    ID   int
    Name string
}

// user_service.go
package user

func (u *User) Save() error {
    // Save user logic
    return nil
}

// user_repository.go
package user

func FindByID(id int) (*User, error) {
    // Database query logic
    return &User{ID: id}, nil
}
```

### Best Practices

1. **Keep packages focused**: Each package should have a single responsibility
2. **Use descriptive names**: Package and file names should be clear
3. **Minimize dependencies**: Avoid circular imports
4. **Document public APIs**: Use comments for exported functions and types
5. **Follow Go conventions**: Use gofmt, golint, and go vet
6. **Organize by feature**: Group related functionality together

This structure provides a solid foundation for organizing Go code effectively, whether you're building small utilities or large-scale applications.