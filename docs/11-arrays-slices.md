# Arrays and Slices in Go

This comprehensive guide covers arrays and slices in Go, including their differences, internal structures, performance characteristics, and best practices.

## Table of Contents

1. [Overview](#overview)
2. [Arrays](#arrays)
3. [Slices](#slices)
4. [Arrays vs Slices](#arrays-vs-slices)
5. [Slice Internals](#slice-internals)
6. [Slice Operations](#slice-operations)
7. [Multi-dimensional Arrays and Slices](#multi-dimensional-arrays-and-slices)
8. [Memory Management](#memory-management)
9. [Performance Considerations](#performance-considerations)
10. [Best Practices](#best-practices)
11. [Common Patterns](#common-patterns)
12. [Common Mistakes](#common-mistakes)

## Overview

Arrays and slices are fundamental data structures in Go for storing sequences of elements. While they may seem similar, they have important differences in behavior, performance, and use cases.

### Key Differences Summary

| Aspect | Arrays | Slices |
|--------|--------|--------|
| **Size** | Fixed at compile time | Dynamic |
| **Type** | Size is part of type | Size not part of type |
| **Memory** | Value type (copied) | Reference type |
| **Performance** | Stack allocated | Heap allocated (usually) |
| **Flexibility** | Limited | High |
| **Use Cases** | Fixed-size collections | Dynamic collections |

### When to Use What

```go
// Use arrays when:
// - Size is known at compile time
// - Size never changes
// - Performance is critical
// - Working with small, fixed collections

// Use slices when:
// - Size can change
// - Need to append/remove elements
// - Passing to functions
// - Most general-purpose cases
```

## Arrays

Arrays are fixed-size sequences of elements of the same type.

### Array Declaration and Initialization

```go
// Array declaration
var arr1 [5]int                    // [0, 0, 0, 0, 0]
var arr2 [3]string                 // ["", "", ""]

// Array initialization with values
var arr3 = [5]int{1, 2, 3, 4, 5}   // [1, 2, 3, 4, 5]
var arr4 = [3]string{"a", "b", "c"} // ["a", "b", "c"]

// Let compiler count elements
var arr5 = [...]int{1, 2, 3, 4, 5} // [1, 2, 3, 4, 5]
var arr6 = [...]string{"x", "y"}   // ["x", "y"]

// Partial initialization
var arr7 = [5]int{1, 3}            // [1, 0, 3, 0, 0]
var arr8 = [5]int{0: 10, 2: 20, 4: 30} // [10, 0, 20, 0, 30]

// Short declaration
arr9 := [3]int{1, 2, 3}
arr10 := [...]float64{1.1, 2.2, 3.3}
```

### Array Properties

```go
// Array size is part of the type
var arr1 [3]int
var arr2 [5]int
// arr1 = arr2  // Error: cannot assign [5]int to [3]int

// Array length
arr := [5]int{1, 2, 3, 4, 5}
fmt.Println(len(arr))              // 5

// Arrays are comparable if element type is comparable
arr1 := [3]int{1, 2, 3}
arr2 := [3]int{1, 2, 3}
arr3 := [3]int{1, 2, 4}

fmt.Println(arr1 == arr2)          // true
fmt.Println(arr1 == arr3)          // false

// Array indexing
fmt.Println(arr[0])                // 1 (first element)
fmt.Println(arr[4])                // 5 (last element)
arr[2] = 100                       // Modify element
```

### Array Operations

```go
// Iterating over arrays
arr := [5]int{1, 2, 3, 4, 5}

// Index-based iteration
for i := 0; i < len(arr); i++ {
    fmt.Printf("arr[%d] = %d\n", i, arr[i])
}

// Range-based iteration
for i, v := range arr {
    fmt.Printf("Index: %d, Value: %d\n", i, v)
}

// Value-only iteration
for _, v := range arr {
    fmt.Printf("Value: %d\n", v)
}

// Index-only iteration
for i := range arr {
    fmt.Printf("Index: %d\n", i)
}
```

### Arrays as Function Parameters

```go
// Arrays are passed by value (copied)
func modifyArray(arr [5]int) {
    arr[0] = 999  // This modifies the copy, not the original
}

func main() {
    original := [5]int{1, 2, 3, 4, 5}
    modifyArray(original)
    fmt.Println(original[0])  // Still 1, not 999
}

// To modify original array, pass pointer
func modifyArrayPointer(arr *[5]int) {
    arr[0] = 999  // This modifies the original
}

func main() {
    original := [5]int{1, 2, 3, 4, 5}
    modifyArrayPointer(&original)
    fmt.Println(original[0])  // Now 999
}

// Better: Use slices for function parameters
func modifySlice(slice []int) {
    slice[0] = 999  // This modifies the original
}

func main() {
    arr := [5]int{1, 2, 3, 4, 5}
    modifySlice(arr[:])  // Convert array to slice
    fmt.Println(arr[0])  // Now 999
}
```

### Array Memory Layout

```go
import "unsafe"

// Arrays are stored contiguously in memory
arr := [4]int64{1, 2, 3, 4}

fmt.Printf("Array size: %d bytes\n", unsafe.Sizeof(arr))  // 32 bytes (4 * 8)
fmt.Printf("Element size: %d bytes\n", unsafe.Sizeof(arr[0]))  // 8 bytes

// Memory addresses are contiguous
for i := 0; i < len(arr); i++ {
    fmt.Printf("arr[%d] address: %p\n", i, &arr[i])
}
// Output shows addresses differ by 8 bytes (size of int64)
```

## Slices

Slices are dynamic arrays that provide a more flexible interface to sequences.

### Slice Declaration and Initialization

```go
// Slice declaration
var slice1 []int                   // nil slice
var slice2 []string                // nil slice

// Slice initialization
var slice3 = []int{1, 2, 3, 4, 5}  // Slice literal
var slice4 = []string{"a", "b", "c"}

// Using make function
var slice5 = make([]int, 5)        // Length 5, capacity 5, all zeros
var slice6 = make([]int, 3, 10)    // Length 3, capacity 10, all zeros
var slice7 = make([]string, 0, 5)  // Length 0, capacity 5

// Short declaration
slice8 := []int{1, 2, 3}
slice9 := make([]float64, 5)
```

### Creating Slices from Arrays

```go
// Slice from array
arr := [5]int{1, 2, 3, 4, 5}

slice1 := arr[1:4]    // [2, 3, 4] (elements 1, 2, 3)
slice2 := arr[:3]     // [1, 2, 3] (elements 0, 1, 2)
slice3 := arr[2:]     // [3, 4, 5] (elements 2, 3, 4)
slice4 := arr[:]      // [1, 2, 3, 4, 5] (all elements)

// Slice from slice
original := []int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}
sub1 := original[2:7]   // [3, 4, 5, 6, 7]
sub2 := sub1[1:3]       // [4, 5] (relative to sub1)
```

### Slice Properties

```go
slice := []int{1, 2, 3, 4, 5}

// Length: number of elements
fmt.Println(len(slice))    // 5

// Capacity: number of elements from start to end of underlying array
fmt.Println(cap(slice))    // 5

// Slices from arrays show capacity
arr := [10]int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}
slice = arr[2:5]           // [3, 4, 5]
fmt.Println(len(slice))    // 3
fmt.Println(cap(slice))    // 8 (from index 2 to end of array)

// Nil slice
var nilSlice []int
fmt.Println(nilSlice == nil)        // true
fmt.Println(len(nilSlice))          // 0
fmt.Println(cap(nilSlice))          // 0

// Empty slice (not nil)
emptySlice := []int{}
fmt.Println(emptySlice == nil)      // false
fmt.Println(len(emptySlice))        // 0
fmt.Println(cap(emptySlice))        // 0
```

## Arrays vs Slices

Understanding the fundamental differences helps choose the right data structure.

### Type System Differences

```go
// Arrays: size is part of type
var arr1 [3]int
var arr2 [5]int
// arr1 = arr2  // Error: different types

// Slices: size is not part of type
var slice1 []int
var slice2 []int
slice1 = slice2  // OK: same type

// Function parameters
func processArray(arr [5]int) {
    // Can only accept [5]int arrays
}

func processSlice(slice []int) {
    // Can accept any []int slice
}

// Usage
arr := [5]int{1, 2, 3, 4, 5}
slice := []int{1, 2, 3, 4, 5}

processArray(arr)    // OK
// processArray(slice)  // Error: cannot use []int as [5]int
processSlice(slice)  // OK
processSlice(arr[:]) // OK: convert array to slice
```

### Memory and Performance Differences

```go
import "unsafe"

// Arrays are value types
arr1 := [1000]int{}
arr2 := arr1  // Copies entire array (8000 bytes on 64-bit)

// Slices are reference types
slice1 := make([]int, 1000)
slice2 := slice1  // Copies only slice header (24 bytes)

// Size comparison
fmt.Printf("Array size: %d bytes\n", unsafe.Sizeof(arr1))    // 8000 bytes
fmt.Printf("Slice size: %d bytes\n", unsafe.Sizeof(slice1))  // 24 bytes

// Function call overhead
func processArrayValue(arr [1000]int) {
    // 8000 bytes copied to stack
}

func processSliceValue(slice []int) {
    // Only 24 bytes copied to stack
}
```

### Mutability Differences

```go
// Arrays: modifications affect only the copy
func modifyArray(arr [3]int) {
    arr[0] = 999
}

// Slices: modifications affect the underlying array
func modifySlice(slice []int) {
    slice[0] = 999
}

func main() {
    arr := [3]int{1, 2, 3}
    slice := []int{1, 2, 3}

    modifyArray(arr)
    fmt.Println(arr)    // [1, 2, 3] (unchanged)

    modifySlice(slice)
    fmt.Println(slice)  // [999, 2, 3] (modified)
}
```

## Slice Internals

Understanding slice internals is crucial for effective Go programming.

### Slice Header Structure

```go
// Conceptual slice structure
type SliceHeader struct {
    Data uintptr  // Pointer to underlying array
    Len  int      // Current length
    Cap  int      // Capacity
}

// Slice header is 24 bytes on 64-bit systems
import "unsafe"
slice := []int{1, 2, 3}
fmt.Printf("Slice header size: %d bytes\n", unsafe.Sizeof(slice))  // 24
```

### Underlying Array Sharing

```go
// Multiple slices can share the same underlying array
original := []int{1, 2, 3, 4, 5, 6, 7, 8}

slice1 := original[1:4]  // [2, 3, 4]
slice2 := original[3:6]  // [4, 5, 6]

// Modifying one slice affects others if they share elements
slice1[2] = 999          // Changes original[3]
fmt.Println(slice2[0])   // 999 (slice2[0] is original[3])
fmt.Println(original)    // [1, 2, 3, 999, 5, 6, 7, 8]

// Visualizing shared memory
fmt.Printf("original: %p\n", &original[0])
fmt.Printf("slice1:   %p\n", &slice1[0])    // Points to original[1]
fmt.Printf("slice2:   %p\n", &slice2[0])    // Points to original[3]
```

### Slice Growth and Reallocation

```go
// Slice growth triggers reallocation
slice := make([]int, 0, 2)  // Length 0, capacity 2
fmt.Printf("Initial: len=%d, cap=%d, ptr=%p\n",
    len(slice), cap(slice), &slice[0:1][0])

slice = append(slice, 1)    // [1]
fmt.Printf("After 1st append: len=%d, cap=%d, ptr=%p\n",
    len(slice), cap(slice), &slice[0])

slice = append(slice, 2)    // [1, 2]
fmt.Printf("After 2nd append: len=%d, cap=%d, ptr=%p\n",
    len(slice), cap(slice), &slice[0])

slice = append(slice, 3)    // [1, 2, 3] - triggers reallocation
fmt.Printf("After 3rd append: len=%d, cap=%d, ptr=%p\n",
    len(slice), cap(slice), &slice[0])  // Different pointer!
```

### Capacity Growth Strategy

```go
// Go's capacity growth strategy (approximate)
func demonstrateGrowth() {
    slice := make([]int, 0)

    for i := 0; i < 20; i++ {
        oldCap := cap(slice)
        slice = append(slice, i)
        newCap := cap(slice)

        if newCap != oldCap {
            fmt.Printf("Grew from %d to %d (factor: %.2f)\n",
                oldCap, newCap, float64(newCap)/float64(oldCap))
        }
    }
}

// Typical output:
// Grew from 0 to 1 (factor: +Inf)
// Grew from 1 to 2 (factor: 2.00)
// Grew from 2 to 4 (factor: 2.00)
// Grew from 4 to 8 (factor: 2.00)
// Grew from 8 to 16 (factor: 2.00)
// Grew from 16 to 32 (factor: 2.00)
```

## Slice Operations

Comprehensive coverage of slice operations and their behaviors.

### Append Operation

```go
// Basic append
slice := []int{1, 2, 3}
slice = append(slice, 4)           // [1, 2, 3, 4]
slice = append(slice, 5, 6, 7)     // [1, 2, 3, 4, 5, 6, 7]

// Append another slice
slice1 := []int{1, 2, 3}
slice2 := []int{4, 5, 6}
slice1 = append(slice1, slice2...)  // [1, 2, 3, 4, 5, 6]

// Append to nil slice
var nilSlice []int
nilSlice = append(nilSlice, 1, 2, 3)  // [1, 2, 3]

// Append with capacity consideration
slice = make([]int, 3, 5)  // [0, 0, 0] with capacity 5
slice = append(slice, 4)   // [0, 0, 0, 4] - no reallocation
slice = append(slice, 5)   // [0, 0, 0, 4, 5] - no reallocation
slice = append(slice, 6)   // [0, 0, 0, 4, 5, 6] - reallocation occurs
```

### Copy Operation

```go
// Basic copy
source := []int{1, 2, 3, 4, 5}
dest := make([]int, len(source))
n := copy(dest, source)
fmt.Printf("Copied %d elements: %v\n", n, dest)  // Copied 5 elements: [1 2 3 4 5]

// Copy with different sizes
source = []int{1, 2, 3, 4, 5}
dest = make([]int, 3)
n = copy(dest, source)
fmt.Printf("Copied %d elements: %v\n", n, dest)  // Copied 3 elements: [1 2 3]

dest = make([]int, 7)
n = copy(dest, source)
fmt.Printf("Copied %d elements: %v\n", n, dest)  // Copied 5 elements: [1 2 3 4 5 0 0]

// Copy overlapping slices
slice := []int{1, 2, 3, 4, 5}
copy(slice[2:], slice[:3])  // Copy [1, 2, 3] to positions 2, 3, 4
fmt.Println(slice)          // [1, 2, 1, 2, 3]
```

### Slicing Operations

```go
// Basic slicing
slice := []int{0, 1, 2, 3, 4, 5, 6, 7, 8, 9}

// slice[start:end] - elements from start to end-1
sub1 := slice[2:5]    // [2, 3, 4]
sub2 := slice[:4]     // [0, 1, 2, 3]
sub3 := slice[6:]     // [6, 7, 8, 9]
sub4 := slice[:]      // [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]

// Three-index slicing: slice[start:end:capacity]
original := []int{0, 1, 2, 3, 4, 5, 6, 7, 8, 9}
sub := original[2:5:7]  // Elements 2,3,4 with capacity limited to 7-2=5
fmt.Printf("len=%d, cap=%d, slice=%v\n", len(sub), cap(sub), sub)
// len=3, cap=5, slice=[2 3 4]

// Without capacity limit
sub2 := original[2:5]   // Capacity extends to end of original
fmt.Printf("len=%d, cap=%d\n", len(sub2), cap(sub2))
// len=3, cap=8
```

### Deletion Operations

```go
// Remove element at index
func removeAtIndex(slice []int, index int) []int {
    return append(slice[:index], slice[index+1:]...)
}

// Remove first occurrence of value
func removeValue(slice []int, value int) []int {
    for i, v := range slice {
        if v == value {
            return append(slice[:i], slice[i+1:]...)
        }
    }
    return slice
}

// Remove multiple elements
func removeRange(slice []int, start, end int) []int {
    return append(slice[:start], slice[end:]...)
}

// Usage examples
slice := []int{1, 2, 3, 4, 5, 6, 7}
slice = removeAtIndex(slice, 2)     // [1, 2, 4, 5, 6, 7]
slice = removeValue(slice, 5)       // [1, 2, 4, 6, 7]
slice = removeRange(slice, 1, 3)    // [1, 6, 7]
```

### Insertion Operations

```go
// Insert element at index
func insertAt(slice []int, index int, value int) []int {
    // Grow slice by one element
    slice = append(slice, 0)
    // Shift elements to the right
    copy(slice[index+1:], slice[index:])
    // Insert new value
    slice[index] = value
    return slice
}

// Insert multiple elements at index
func insertSliceAt(slice []int, index int, values []int) []int {
    // Make room for new elements
    slice = append(slice, make([]int, len(values))...)
    // Shift existing elements
    copy(slice[index+len(values):], slice[index:])
    // Insert new values
    copy(slice[index:], values)
    return slice
}

// Usage examples
slice := []int{1, 2, 4, 5}
slice = insertAt(slice, 2, 3)           // [1, 2, 3, 4, 5]
slice = insertSliceAt(slice, 0, []int{-1, 0})  // [-1, 0, 1, 2, 3, 4, 5]
```

## Multi-dimensional Arrays and Slices

Working with multi-dimensional data structures in Go.

### Multi-dimensional Arrays

```go
// 2D array declaration
var matrix2D [3][4]int                    // 3 rows, 4 columns

// 2D array initialization
var matrix = [3][4]int{
    {1, 2, 3, 4},
    {5, 6, 7, 8},
    {9, 10, 11, 12},
}

// 3D array
var cube [2][3][4]int

// Accessing elements
fmt.Println(matrix[1][2])                 // 7
matrix[0][0] = 100

// Iterating over 2D array
for i := 0; i < len(matrix); i++ {
    for j := 0; j < len(matrix[i]); j++ {
        fmt.Printf("%d ", matrix[i][j])
    }
    fmt.Println()
}

// Range iteration
for i, row := range matrix {
    for j, value := range row {
        fmt.Printf("matrix[%d][%d] = %d\n", i, j, value)
    }
}
```

### Multi-dimensional Slices

```go
// 2D slice - slice of slices
var matrix2D [][]int

// Initialize with make
matrix2D = make([][]int, 3)               // 3 rows
for i := range matrix2D {
    matrix2D[i] = make([]int, 4)          // 4 columns each
}

// Initialize with literal
matrix2D = [][]int{
    {1, 2, 3, 4},
    {5, 6, 7, 8},
    {9, 10, 11, 12},
}

// Jagged arrays (different row lengths)
jagged := [][]int{
    {1, 2},
    {3, 4, 5, 6},
    {7},
    {8, 9, 10},
}

// 3D slice
var cube3D [][][]int
cube3D = make([][][]int, 2)
for i := range cube3D {
    cube3D[i] = make([][]int, 3)
    for j := range cube3D[i] {
        cube3D[i][j] = make([]int, 4)
    }
}
```

### Efficient Multi-dimensional Slice Creation

```go
// Efficient 2D slice creation (single allocation)
func make2D(rows, cols int) [][]int {
    // Allocate single backing array
    data := make([]int, rows*cols)

    // Create slice of slices pointing to backing array
    matrix := make([][]int, rows)
    for i := range matrix {
        matrix[i] = data[i*cols : (i+1)*cols]
    }

    return matrix
}

// Usage
matrix := make2D(3, 4)
matrix[1][2] = 42
fmt.Println(matrix[1][2])  // 42

// Efficient 3D slice creation
func make3D(x, y, z int) [][][]int {
    data := make([]int, x*y*z)
    cube := make([][][]int, x)

    for i := range cube {
        cube[i] = make([][]int, y)
        for j := range cube[i] {
            start := (i*y+j)*z
            cube[i][j] = data[start : start+z]
        }
    }

    return cube
}
```

## Memory Management

Understanding memory allocation and management for arrays and slices.

### Stack vs Heap Allocation

```go
// Arrays are typically stack allocated
func stackArrays() {
    var arr [100]int  // Stack allocated (if function doesn't escape)
    arr[0] = 42
    // arr is automatically cleaned up when function returns
}

// Large arrays may be heap allocated
func heapArrays() *[10000]int {
    var arr [10000]int  // Heap allocated (escapes via return)
    return &arr
}

// Slices are typically heap allocated
func sliceAllocation() {
    slice := make([]int, 100)  // Underlying array is heap allocated
    slice[0] = 42
    // Slice header on stack, underlying array on heap
}
```

### Memory Leaks and Prevention

```go
// Memory leak: keeping reference to large slice
func memoryLeak() []int {
    largeSlice := make([]int, 1000000)
    // ... populate largeSlice

    // This keeps the entire large slice in memory
    return largeSlice[0:10]  // Only need first 10 elements
}

// Fixed: copy needed elements
func memoryLeakFixed() []int {
    largeSlice := make([]int, 1000000)
    // ... populate largeSlice

    // Copy only needed elements
    result := make([]int, 10)
    copy(result, largeSlice[0:10])
    return result  // largeSlice can be garbage collected
}

// Memory leak: slice of pointers
func pointerSliceLeak() {
    slice := make([]*LargeStruct, 1000)
    // ... populate slice

    // Remove elements but keep pointers
    slice = slice[:10]  // Last 990 elements still referenced!
}

// Fixed: nil out removed pointers
func pointerSliceFixed() {
    slice := make([]*LargeStruct, 1000)
    // ... populate slice

    // Nil out pointers before shrinking
    for i := 10; i < len(slice); i++ {
        slice[i] = nil
    }
    slice = slice[:10]
}
```

### Slice Capacity Management

```go
// Pre-allocate when size is known
func efficientSliceBuilding(n int) []int {
    // Good: pre-allocate capacity
    result := make([]int, 0, n)
    for i := 0; i < n; i++ {
        result = append(result, i)  // No reallocations
    }
    return result
}

// Inefficient: multiple reallocations
func inefficientSliceBuilding(n int) []int {
    var result []int
    for i := 0; i < n; i++ {
        result = append(result, i)  // Multiple reallocations
    }
    return result
}

// Reset slice for reuse
func reuseSlice() {
    slice := make([]int, 0, 100)

    for iteration := 0; iteration < 10; iteration++ {
        // Reset length but keep capacity
        slice = slice[:0]

        // Reuse the slice
        for i := 0; i < 50; i++ {
            slice = append(slice, i)
        }

        processSlice(slice)
    }
}
```

## Performance Considerations

Optimizing array and slice operations for better performance.

### Benchmarking Array vs Slice Operations

```go
import "testing"

// Benchmark array access
func BenchmarkArrayAccess(b *testing.B) {
    arr := [1000]int{}
    for i := range arr {
        arr[i] = i
    }

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        sum := 0
        for j := 0; j < len(arr); j++ {
            sum += arr[j]
        }
    }
}

// Benchmark slice access
func BenchmarkSliceAccess(b *testing.B) {
    slice := make([]int, 1000)
    for i := range slice {
        slice[i] = i
    }

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        sum := 0
        for j := 0; j < len(slice); j++ {
            sum += slice[j]
        }
    }
}

// Benchmark range vs index iteration
func BenchmarkRangeIteration(b *testing.B) {
    slice := make([]int, 1000)
    for i := range slice {
        slice[i] = i
    }

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        sum := 0
        for _, v := range slice {
            sum += v
        }
    }
}
```

### Optimization Techniques

```go
// 1. Avoid bounds checking when safe
func unsafeFastSum(slice []int) int {
    sum := 0
    for i := 0; i < len(slice); i++ {
        sum += slice[i]  // Bounds check on every access
    }
    return sum
}

func safeFastSum(slice []int) int {
    sum := 0
    for _, v := range slice {  // No bounds checking
        sum += v
    }
    return sum
}

// 2. Pre-allocate slices
func slowAppend() []int {
    var result []int
    for i := 0; i < 10000; i++ {
        result = append(result, i)  // Multiple reallocations
    }
    return result
}

func fastAppend() []int {
    result := make([]int, 0, 10000)  // Pre-allocate
    for i := 0; i < 10000; i++ {
        result = append(result, i)  // No reallocations
    }
    return result
}

// 3. Use copy for bulk operations
func slowCopy(src, dst []int) {
    for i := 0; i < len(src) && i < len(dst); i++ {
        dst[i] = src[i]  // Element by element
    }
}

func fastCopy(src, dst []int) {
    copy(dst, src)  // Optimized bulk copy
}

// 4. Minimize slice header copying
func processSlicesBad(slices [][]int) {
    for _, slice := range slices {  // Copies slice header
        processSlice(slice)
    }
}

func processSlicesGood(slices [][]int) {
    for i := range slices {  // No copying
        processSlice(slices[i])
    }
}
```

## Best Practices

Guidelines for effective use of arrays and slices.

### When to Use Arrays vs Slices

```go
// Use arrays for:
// 1. Fixed-size data known at compile time
type RGB [3]uint8  // Always 3 components
type Matrix4x4 [4][4]float64  // Always 4x4

// 2. Performance-critical code with small, fixed collections
func processCoordinates(points [3][2]float64) {
    // Stack allocated, no indirection
}

// 3. As underlying storage for slices
type Buffer struct {
    data [4096]byte
    slice []byte
}

func (b *Buffer) Slice() []byte {
    return b.data[:]
}

// Use slices for:
// 1. Dynamic collections
func collectResults() []Result {
    var results []Result
    // Add results dynamically
    return results
}

// 2. Function parameters
func processItems(items []Item) {
    // Can accept any size slice
}

// 3. Most general-purpose cases
func main() {
    data := []int{1, 2, 3, 4, 5}  // Flexible and convenient
}
```

### Slice Best Practices

```go
// 1. Pre-allocate when size is known
func goodPreallocation() []int {
    const size = 1000
    result := make([]int, 0, size)  // Pre-allocate capacity

    for i := 0; i < size; i++ {
        result = append(result, i)
    }
    return result
}

// 2. Use three-index slicing to limit capacity
func limitCapacity(data []int) []int {
    // Prevent accidental modification of original data
    return data[10:20:20]  // len=10, cap=10
}

// 3. Check for nil before using
func safeSliceOperation(slice []int) {
    if slice == nil {
        return
    }

    for _, v := range slice {
        process(v)
    }
}

// 4. Copy slices when you need independence
func independentCopy(original []int) []int {
    copy := make([]int, len(original))
    copy(copy, original)
    return copy
}

// 5. Use append correctly
func correctAppend() {
    slice := []int{1, 2, 3}

    // Always assign result of append
    slice = append(slice, 4)

    // When appending to different slice
    newSlice := append(slice, 5, 6, 7)

    // When appending another slice
    other := []int{8, 9, 10}
    slice = append(slice, other...)
}
```

## Common Patterns

Useful patterns for working with arrays and slices.

### Filtering

```go
// Filter slice in-place
func filterInPlace(slice []int, predicate func(int) bool) []int {
    n := 0
    for _, v := range slice {
        if predicate(v) {
            slice[n] = v
            n++
        }
    }
    return slice[:n]
}

// Filter to new slice
func filter(slice []int, predicate func(int) bool) []int {
    var result []int
    for _, v := range slice {
        if predicate(v) {
            result = append(result, v)
        }
    }
    return result
}

// Usage
numbers := []int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}
evens := filter(numbers, func(n int) bool { return n%2 == 0 })
```

### Mapping

```go
// Map slice to new slice
func mapSlice[T, U any](slice []T, mapper func(T) U) []U {
    result := make([]U, len(slice))
    for i, v := range slice {
        result[i] = mapper(v)
    }
    return result
}

// Usage
numbers := []int{1, 2, 3, 4, 5}
squares := mapSlice(numbers, func(n int) int { return n * n })
strings := mapSlice(numbers, func(n int) string { return fmt.Sprintf("%d", n) })
```

### Reducing

```go
// Reduce slice to single value
func reduce[T, U any](slice []T, initial U, reducer func(U, T) U) U {
    result := initial
    for _, v := range slice {
        result = reducer(result, v)
    }
    return result
}

// Usage
numbers := []int{1, 2, 3, 4, 5}
sum := reduce(numbers, 0, func(acc, n int) int { return acc + n })
product := reduce(numbers, 1, func(acc, n int) int { return acc * n })
```

## Common Mistakes

Avoiding common pitfalls when working with arrays and slices.

### Mistake 1: Slice Modification During Iteration

```go
// Wrong: modifying slice during iteration
func removeBadElements(slice []int) []int {
    for i, v := range slice {
        if v < 0 {
            slice = append(slice[:i], slice[i+1:]...)  // Dangerous!
        }
    }
    return slice
}

// Correct: iterate backwards or collect indices
func removeElementsCorrect(slice []int) []int {
    for i := len(slice) - 1; i >= 0; i-- {
        if slice[i] < 0 {
            slice = append(slice[:i], slice[i+1:]...)
        }
    }
    return slice
}
```

### Mistake 2: Assuming Slice Independence

```go
// Wrong: assuming slices are independent
func createSlices() ([]int, []int) {
    original := []int{1, 2, 3, 4, 5}
    slice1 := original[0:3]  // [1, 2, 3]
    slice2 := original[2:5]  // [3, 4, 5]

    slice1[2] = 999  // Affects slice2[0]!
    return slice1, slice2
}

// Correct: create independent copies
func createIndependentSlices() ([]int, []int) {
    original := []int{1, 2, 3, 4, 5}

    slice1 := make([]int, 3)
    copy(slice1, original[0:3])

    slice2 := make([]int, 3)
    copy(slice2, original[2:5])

    return slice1, slice2
}
```

### Mistake 3: Not Handling Nil Slices

```go
// Wrong: not checking for nil
func processSlice(slice []int) int {
    return slice[0]  // Panic if slice is nil or empty
}

// Correct: check for nil and length
func processSliceSafe(slice []int) (int, error) {
    if slice == nil || len(slice) == 0 {
        return 0, errors.New("slice is nil or empty")
    }
    return slice[0], nil
}
```

This comprehensive guide covers all aspects of arrays and slices in Go, providing the knowledge needed to use these fundamental data structures effectively and efficiently.
