# Testing in Go

This comprehensive guide covers testing in Go, including unit tests, benchmarks, table-driven tests, mocking, test coverage, and advanced testing patterns for building reliable Go applications.

## Table of Contents

1. [Overview](#overview)
2. [Basic Testing](#basic-testing)
3. [Test Organization](#test-organization)
4. [Table-Driven Tests](#table-driven-tests)
5. [Subtests](#subtests)
6. [Test Helpers](#test-helpers)
7. [Benchmarking](#benchmarking)
8. [Example Tests](#example-tests)
9. [Test Coverage](#test-coverage)
10. [Mocking](#mocking)
11. [Integration Testing](#integration-testing)
12. [HTTP Testing](#http-testing)
13. [Database Testing](#database-testing)
14. [Test Utilities](#test-utilities)
15. [Best Practices](#best-practices)
16. [Advanced Patterns](#advanced-patterns)

## Overview

Go's testing package provides a robust framework for writing tests, benchmarks, and examples.

### Key Concepts

```go
// Test function signature
func TestFunctionName(t *testing.T) {
    // Test implementation
}

// Benchmark function signature
func BenchmarkFunctionName(b *testing.B) {
    // Benchmark implementation
}

// Example function signature
func ExampleFunctionName() {
    // Example implementation
    // Output: expected output
}
```

### Testing Philosophy

| Principle | Description | Example |
|-----------|-------------|---------|
| **Fast** | Tests should run quickly | Unit tests < 100ms |
| **Independent** | Tests don't depend on each other | No shared state |
| **Repeatable** | Same results every time | No random data |
| **Self-validating** | Clear pass/fail | Explicit assertions |
| **Timely** | Written with or before code | TDD approach |

## Basic Testing

Understanding the fundamentals of Go testing.

### Simple Test Example

```go
// math.go
package math

func Add(a, b int) int {
    return a + b
}

func Divide(a, b float64) (float64, error) {
    if b == 0 {
        return 0, errors.New("division by zero")
    }
    return a / b, nil
}

func IsEven(n int) bool {
    return n%2 == 0
}
```

```go
// math_test.go
package math

import (
    "testing"
)

func TestAdd(t *testing.T) {
    result := Add(2, 3)
    expected := 5

    if result != expected {
        t.Errorf("Add(2, 3) = %d; want %d", result, expected)
    }
}

func TestDivide(t *testing.T) {
    // Test normal division
    result, err := Divide(10, 2)
    if err != nil {
        t.Errorf("Divide(10, 2) returned error: %v", err)
    }

    expected := 5.0
    if result != expected {
        t.Errorf("Divide(10, 2) = %f; want %f", result, expected)
    }

    // Test division by zero
    _, err = Divide(10, 0)
    if err == nil {
        t.Error("Divide(10, 0) should return error")
    }
}

func TestIsEven(t *testing.T) {
    if !IsEven(2) {
        t.Error("IsEven(2) should return true")
    }

    if IsEven(3) {
        t.Error("IsEven(3) should return false")
    }
}
```

### Test Functions and Methods

```go
// Testing methods
type Calculator struct {
    memory float64
}

func (c *Calculator) Add(value float64) {
    c.memory += value
}

func (c *Calculator) GetMemory() float64 {
    return c.memory
}

func (c *Calculator) Clear() {
    c.memory = 0
}

// calculator_test.go
func TestCalculator(t *testing.T) {
    calc := &Calculator{}

    // Test initial state
    if calc.GetMemory() != 0 {
        t.Errorf("Initial memory should be 0, got %f", calc.GetMemory())
    }

    // Test addition
    calc.Add(5)
    if calc.GetMemory() != 5 {
        t.Errorf("After adding 5, memory should be 5, got %f", calc.GetMemory())
    }

    // Test multiple additions
    calc.Add(3)
    if calc.GetMemory() != 8 {
        t.Errorf("After adding 3, memory should be 8, got %f", calc.GetMemory())
    }

    // Test clear
    calc.Clear()
    if calc.GetMemory() != 0 {
        t.Errorf("After clear, memory should be 0, got %f", calc.GetMemory())
    }
}
```

### Test Assertions

```go
// Common assertion patterns
func TestAssertions(t *testing.T) {
    // Equality
    got := Add(2, 3)
    want := 5
    if got != want {
        t.Errorf("Add(2, 3) = %d; want %d", got, want)
    }

    // Error checking
    _, err := Divide(10, 0)
    if err == nil {
        t.Error("Expected error for division by zero")
    }

    // Boolean assertions
    if !IsEven(4) {
        t.Error("4 should be even")
    }

    // Slice comparison
    gotSlice := []int{1, 2, 3}
    wantSlice := []int{1, 2, 3}
    if !slicesEqual(gotSlice, wantSlice) {
        t.Errorf("Slices not equal: got %v, want %v", gotSlice, wantSlice)
    }

    // String contains
    message := "Hello, World!"
    if !strings.Contains(message, "World") {
        t.Errorf("Message should contain 'World': %s", message)
    }
}

// Helper function for slice comparison
func slicesEqual(a, b []int) bool {
    if len(a) != len(b) {
        return false
    }
    for i := range a {
        if a[i] != b[i] {
            return false
        }
    }
    return true
}
```

## Test Organization

Structuring tests for maintainability and clarity.

### File Organization

```
mypackage/
├── calculator.go
├── calculator_test.go      // Unit tests
├── integration_test.go     // Integration tests
├── benchmark_test.go       // Benchmarks
├── example_test.go         // Examples
└── testdata/              // Test data files
    ├── input.json
    └── expected.json
```

### Test Naming Conventions

```go
// Good test names
func TestAdd(t *testing.T)                    // Tests Add function
func TestCalculator_Add(t *testing.T)         // Tests Calculator.Add method
func TestUserService_CreateUser(t *testing.T) // Tests UserService.CreateUser
func TestParseJSON_InvalidInput(t *testing.T) // Tests specific scenario

// Test scenarios
func TestAdd_PositiveNumbers(t *testing.T)    // Specific case
func TestAdd_NegativeNumbers(t *testing.T)    // Another case
func TestAdd_ZeroValues(t *testing.T)         // Edge case
func TestAdd_LargeNumbers(t *testing.T)       // Boundary case
```

### Setup and Teardown

```go
func TestMain(m *testing.M) {
    // Setup before all tests
    setup()

    // Run tests
    code := m.Run()

    // Teardown after all tests
    teardown()

    // Exit with test result code
    os.Exit(code)
}

func setup() {
    fmt.Println("Setting up test environment")
    // Initialize test database, create temp files, etc.
}

func teardown() {
    fmt.Println("Cleaning up test environment")
    // Clean up resources, remove temp files, etc.
}

// Per-test setup/teardown
func TestWithSetup(t *testing.T) {
    // Setup for this specific test
    tempDir := createTempDir(t)
    defer removeTempDir(tempDir) // Cleanup

    // Test implementation
    // ...
}

func createTempDir(t *testing.T) string {
    dir, err := os.MkdirTemp("", "test")
    if err != nil {
        t.Fatalf("Failed to create temp dir: %v", err)
    }
    return dir
}

func removeTempDir(dir string) {
    os.RemoveAll(dir)
}
```

## Table-Driven Tests

Efficient testing with multiple test cases using table-driven approach.

### Basic Table-Driven Tests

```go
func TestAdd_TableDriven(t *testing.T) {
    tests := []struct {
        name     string
        a, b     int
        expected int
    }{
        {"positive numbers", 2, 3, 5},
        {"negative numbers", -2, -3, -5},
        {"mixed signs", -2, 3, 1},
        {"zero values", 0, 0, 0},
        {"large numbers", 1000000, 2000000, 3000000},
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            result := Add(tt.a, tt.b)
            if result != tt.expected {
                t.Errorf("Add(%d, %d) = %d; want %d", tt.a, tt.b, result, tt.expected)
            }
        })
    }
}
```

### Complex Table-Driven Tests

```go
func TestDivide_TableDriven(t *testing.T) {
    tests := []struct {
        name        string
        a, b        float64
        expected    float64
        expectError bool
        errorMsg    string
    }{
        {
            name:        "normal division",
            a:           10,
            b:           2,
            expected:    5,
            expectError: false,
        },
        {
            name:        "division by zero",
            a:           10,
            b:           0,
            expected:    0,
            expectError: true,
            errorMsg:    "division by zero",
        },
        {
            name:        "negative numbers",
            a:           -10,
            b:           2,
            expected:    -5,
            expectError: false,
        },
        {
            name:        "decimal result",
            a:           7,
            b:           3,
            expected:    2.3333333333333335,
            expectError: false,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            result, err := Divide(tt.a, tt.b)

            if tt.expectError {
                if err == nil {
                    t.Errorf("Divide(%f, %f) expected error but got none", tt.a, tt.b)
                    return
                }
                if tt.errorMsg != "" && !strings.Contains(err.Error(), tt.errorMsg) {
                    t.Errorf("Divide(%f, %f) error = %v; want error containing %q",
                        tt.a, tt.b, err, tt.errorMsg)
                }
                return
            }

            if err != nil {
                t.Errorf("Divide(%f, %f) unexpected error: %v", tt.a, tt.b, err)
                return
            }

            if result != tt.expected {
                t.Errorf("Divide(%f, %f) = %f; want %f", tt.a, tt.b, result, tt.expected)
            }
        })
    }
}
```

### Table Tests with Setup

```go
func TestUserService_CreateUser(t *testing.T) {
    tests := []struct {
        name        string
        user        User
        setupFunc   func(*testing.T) *UserService
        expectError bool
        errorType   error
    }{
        {
            name: "valid user",
            user: User{Name: "John", Email: "<EMAIL>"},
            setupFunc: func(t *testing.T) *UserService {
                return NewUserService(NewMockDB())
            },
            expectError: false,
        },
        {
            name: "duplicate email",
            user: User{Name: "Jane", Email: "<EMAIL>"},
            setupFunc: func(t *testing.T) *UserService {
                db := NewMockDB()
                db.AddUser(User{Name: "Existing", Email: "<EMAIL>"})
                return NewUserService(db)
            },
            expectError: true,
            errorType:   ErrDuplicateEmail,
        },
        {
            name: "invalid email",
            user: User{Name: "Bob", Email: "invalid-email"},
            setupFunc: func(t *testing.T) *UserService {
                return NewUserService(NewMockDB())
            },
            expectError: true,
            errorType:   ErrInvalidEmail,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            service := tt.setupFunc(t)

            err := service.CreateUser(tt.user)

            if tt.expectError {
                if err == nil {
                    t.Errorf("CreateUser() expected error but got none")
                    return
                }
                if tt.errorType != nil && !errors.Is(err, tt.errorType) {
                    t.Errorf("CreateUser() error = %v; want %v", err, tt.errorType)
                }
                return
            }

            if err != nil {
                t.Errorf("CreateUser() unexpected error: %v", err)
            }
        })
    }
}
```

## Subtests

Organizing related tests using subtests for better structure and reporting.

### Basic Subtests

```go
func TestMathOperations(t *testing.T) {
    t.Run("Addition", func(t *testing.T) {
        result := Add(2, 3)
        if result != 5 {
            t.Errorf("Add(2, 3) = %d; want 5", result)
        }
    })

    t.Run("Division", func(t *testing.T) {
        t.Run("Normal", func(t *testing.T) {
            result, err := Divide(10, 2)
            if err != nil {
                t.Errorf("Divide(10, 2) error: %v", err)
            }
            if result != 5 {
                t.Errorf("Divide(10, 2) = %f; want 5", result)
            }
        })

        t.Run("ByZero", func(t *testing.T) {
            _, err := Divide(10, 0)
            if err == nil {
                t.Error("Divide(10, 0) should return error")
            }
        })
    })
}
```

### Parallel Subtests

```go
func TestConcurrentOperations(t *testing.T) {
    t.Run("group", func(t *testing.T) {
        t.Run("A", func(t *testing.T) {
            t.Parallel()
            // Test A implementation
            time.Sleep(100 * time.Millisecond)
            // ...
        })

        t.Run("B", func(t *testing.T) {
            t.Parallel()
            // Test B implementation
            time.Sleep(100 * time.Millisecond)
            // ...
        })

        t.Run("C", func(t *testing.T) {
            t.Parallel()
            // Test C implementation
            time.Sleep(100 * time.Millisecond)
            // ...
        })
    })
}
```

## Benchmarking

Performance testing with Go's benchmarking framework.

### Basic Benchmarks

```go
func BenchmarkAdd(b *testing.B) {
    for i := 0; i < b.N; i++ {
        Add(2, 3)
    }
}

func BenchmarkDivide(b *testing.B) {
    for i := 0; i < b.N; i++ {
        Divide(10, 2)
    }
}

// Run benchmarks:
// go test -bench=.
// go test -bench=BenchmarkAdd
// go test -bench=. -benchmem  // Include memory stats
```

### Advanced Benchmarks

```go
func BenchmarkStringConcatenation(b *testing.B) {
    tests := []struct {
        name string
        fn   func([]string) string
    }{
        {"Plus", concatenateWithPlus},
        {"Builder", concatenateWithBuilder},
        {"Join", concatenateWithJoin},
    }

    strings := []string{"hello", "world", "this", "is", "a", "test"}

    for _, tt := range tests {
        b.Run(tt.name, func(b *testing.B) {
            for i := 0; i < b.N; i++ {
                tt.fn(strings)
            }
        })
    }
}

func concatenateWithPlus(strings []string) string {
    result := ""
    for _, s := range strings {
        result += s
    }
    return result
}

func concatenateWithBuilder(strings []string) string {
    var builder strings.Builder
    for _, s := range strings {
        builder.WriteString(s)
    }
    return builder.String()
}

func concatenateWithJoin(strings []string) string {
    return strings.Join(strings, "")
}
```

### Benchmark with Setup

```go
func BenchmarkMapOperations(b *testing.B) {
    sizes := []int{100, 1000, 10000, 100000}

    for _, size := range sizes {
        b.Run(fmt.Sprintf("Size%d", size), func(b *testing.B) {
            // Setup
            data := make(map[int]string)
            for i := 0; i < size; i++ {
                data[i] = fmt.Sprintf("value%d", i)
            }

            b.ResetTimer() // Reset timer after setup

            // Benchmark
            for i := 0; i < b.N; i++ {
                // Access random key
                key := i % size
                _ = data[key]
            }
        })
    }
}

func BenchmarkMemoryAllocation(b *testing.B) {
    b.Run("SliceAppend", func(b *testing.B) {
        for i := 0; i < b.N; i++ {
            var slice []int
            for j := 0; j < 1000; j++ {
                slice = append(slice, j)
            }
        }
    })

    b.Run("SlicePrealloc", func(b *testing.B) {
        for i := 0; i < b.N; i++ {
            slice := make([]int, 0, 1000)
            for j := 0; j < 1000; j++ {
                slice = append(slice, j)
            }
        }
    })
}
```

## Example Tests

Executable documentation using example tests.

### Basic Examples

```go
// example_test.go
package math_test

import (
    "fmt"
    "mypackage/math"
)

func ExampleAdd() {
    result := math.Add(2, 3)
    fmt.Println(result)
    // Output: 5
}

func ExampleDivide() {
    result, err := math.Divide(10, 2)
    if err != nil {
        fmt.Printf("Error: %v", err)
        return
    }
    fmt.Printf("%.1f", result)
    // Output: 5.0
}

func ExampleDivide_zero() {
    _, err := math.Divide(10, 0)
    if err != nil {
        fmt.Println("Error:", err)
    }
    // Output: Error: division by zero
}
```

### Complex Examples

```go
func ExampleCalculator() {
    calc := math.NewCalculator()
    calc.Add(5)
    calc.Add(3)
    fmt.Printf("Memory: %.1f", calc.GetMemory())
    // Output: Memory: 8.0
}

func ExampleCalculator_operations() {
    calc := math.NewCalculator()

    // Add some values
    calc.Add(10)
    calc.Add(5)
    fmt.Printf("After additions: %.0f\n", calc.GetMemory())

    // Clear and start over
    calc.Clear()
    calc.Add(3)
    fmt.Printf("After clear and add 3: %.0f", calc.GetMemory())

    // Output:
    // After additions: 15
    // After clear and add 3: 3
}

// Example with unordered output
func ExampleProcessMap() {
    data := map[string]int{
        "apple":  5,
        "banana": 3,
        "cherry": 8,
    }

    for key, value := range data {
        fmt.Printf("%s: %d\n", key, value)
    }

    // Unordered output:
    // apple: 5
    // banana: 3
    // cherry: 8
}
```

## Test Coverage

Measuring and improving test coverage.

### Running Coverage

```bash
# Run tests with coverage
go test -cover

# Generate coverage profile
go test -coverprofile=coverage.out

# View coverage in browser
go tool cover -html=coverage.out

# Show coverage by function
go tool cover -func=coverage.out

# Coverage for specific packages
go test -cover ./...

# Coverage with specific mode
go test -covermode=atomic -coverprofile=coverage.out
```

### Coverage Analysis

```go
// math.go - Example with different coverage scenarios
package math

func Add(a, b int) int {
    return a + b  // This line will be covered
}

func ComplexFunction(x int) string {
    if x > 0 {
        if x > 100 {
            return "large"     // May not be covered
        }
        return "positive"      // Covered
    } else if x < 0 {
        return "negative"      // May not be covered
    }
    return "zero"              // Covered
}

func UntestedFunction() {
    // This function is not tested - 0% coverage
    fmt.Println("This is not tested")
}
```

```go
// Comprehensive test for better coverage
func TestComplexFunction_AllBranches(t *testing.T) {
    tests := []struct {
        input    int
        expected string
    }{
        {150, "large"},      // Covers x > 100 branch
        {50, "positive"},    // Covers 0 < x <= 100 branch
        {-5, "negative"},    // Covers x < 0 branch
        {0, "zero"},         // Covers x == 0 branch
    }

    for _, tt := range tests {
        t.Run(fmt.Sprintf("input_%d", tt.input), func(t *testing.T) {
            result := ComplexFunction(tt.input)
            if result != tt.expected {
                t.Errorf("ComplexFunction(%d) = %s; want %s",
                    tt.input, result, tt.expected)
            }
        })
    }
}
```

## Mocking

Creating and using mocks for testing dependencies.

### Interface-Based Mocking

```go
// Define interfaces for dependencies
type UserRepository interface {
    Create(user User) error
    GetByID(id int) (User, error)
    GetByEmail(email string) (User, error)
    Update(user User) error
    Delete(id int) error
}

type EmailService interface {
    SendWelcomeEmail(email, name string) error
    SendPasswordReset(email, token string) error
}

// Service that depends on interfaces
type UserService struct {
    repo  UserRepository
    email EmailService
}

func NewUserService(repo UserRepository, email EmailService) *UserService {
    return &UserService{
        repo:  repo,
        email: email,
    }
}

func (s *UserService) RegisterUser(name, email string) error {
    // Check if user exists
    _, err := s.repo.GetByEmail(email)
    if err == nil {
        return errors.New("user already exists")
    }

    // Create user
    user := User{Name: name, Email: email}
    if err := s.repo.Create(user); err != nil {
        return err
    }

    // Send welcome email
    return s.email.SendWelcomeEmail(email, name)
}
```

### Manual Mocks

```go
// Mock implementations
type MockUserRepository struct {
    users  map[int]User
    emails map[string]User
    nextID int
}

func NewMockUserRepository() *MockUserRepository {
    return &MockUserRepository{
        users:  make(map[int]User),
        emails: make(map[string]User),
        nextID: 1,
    }
}

func (m *MockUserRepository) Create(user User) error {
    // Check for duplicate email
    if _, exists := m.emails[user.Email]; exists {
        return errors.New("email already exists")
    }

    user.ID = m.nextID
    m.nextID++
    m.users[user.ID] = user
    m.emails[user.Email] = user
    return nil
}

func (m *MockUserRepository) GetByID(id int) (User, error) {
    user, exists := m.users[id]
    if !exists {
        return User{}, errors.New("user not found")
    }
    return user, nil
}

func (m *MockUserRepository) GetByEmail(email string) (User, error) {
    user, exists := m.emails[email]
    if !exists {
        return User{}, errors.New("user not found")
    }
    return user, nil
}

func (m *MockUserRepository) Update(user User) error {
    if _, exists := m.users[user.ID]; !exists {
        return errors.New("user not found")
    }
    m.users[user.ID] = user
    return nil
}

func (m *MockUserRepository) Delete(id int) error {
    user, exists := m.users[id]
    if !exists {
        return errors.New("user not found")
    }
    delete(m.users, id)
    delete(m.emails, user.Email)
    return nil
}

type MockEmailService struct {
    SentEmails []EmailRecord
    ShouldFail bool
}

type EmailRecord struct {
    Type      string
    Recipient string
    Subject   string
    Body      string
}

func NewMockEmailService() *MockEmailService {
    return &MockEmailService{
        SentEmails: make([]EmailRecord, 0),
    }
}

func (m *MockEmailService) SendWelcomeEmail(email, name string) error {
    if m.ShouldFail {
        return errors.New("email service unavailable")
    }

    m.SentEmails = append(m.SentEmails, EmailRecord{
        Type:      "welcome",
        Recipient: email,
        Subject:   "Welcome!",
        Body:      fmt.Sprintf("Welcome, %s!", name),
    })
    return nil
}

func (m *MockEmailService) SendPasswordReset(email, token string) error {
    if m.ShouldFail {
        return errors.New("email service unavailable")
    }

    m.SentEmails = append(m.SentEmails, EmailRecord{
        Type:      "password_reset",
        Recipient: email,
        Subject:   "Password Reset",
        Body:      fmt.Sprintf("Reset token: %s", token),
    })
    return nil
}
```

### Testing with Mocks

```go
func TestUserService_RegisterUser(t *testing.T) {
    tests := []struct {
        name          string
        userName      string
        userEmail     string
        setupRepo     func(*MockUserRepository)
        setupEmail    func(*MockEmailService)
        expectError   bool
        expectedError string
    }{
        {
            name:      "successful registration",
            userName:  "John Doe",
            userEmail: "<EMAIL>",
            setupRepo: func(repo *MockUserRepository) {
                // No existing users
            },
            setupEmail: func(email *MockEmailService) {
                // Email service working
            },
            expectError: false,
        },
        {
            name:      "duplicate email",
            userName:  "Jane Doe",
            userEmail: "<EMAIL>",
            setupRepo: func(repo *MockUserRepository) {
                // Add existing user
                repo.Create(User{Name: "Existing", Email: "<EMAIL>"})
            },
            setupEmail: func(email *MockEmailService) {
                // Email service working
            },
            expectError:   true,
            expectedError: "user already exists",
        },
        {
            name:      "email service failure",
            userName:  "Bob Smith",
            userEmail: "<EMAIL>",
            setupRepo: func(repo *MockUserRepository) {
                // No existing users
            },
            setupEmail: func(email *MockEmailService) {
                email.ShouldFail = true
            },
            expectError:   true,
            expectedError: "email service unavailable",
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Setup mocks
            repo := NewMockUserRepository()
            email := NewMockEmailService()

            tt.setupRepo(repo)
            tt.setupEmail(email)

            // Create service
            service := NewUserService(repo, email)

            // Execute
            err := service.RegisterUser(tt.userName, tt.userEmail)

            // Verify
            if tt.expectError {
                if err == nil {
                    t.Errorf("Expected error but got none")
                    return
                }
                if tt.expectedError != "" && !strings.Contains(err.Error(), tt.expectedError) {
                    t.Errorf("Expected error containing %q, got %q", tt.expectedError, err.Error())
                }
                return
            }

            if err != nil {
                t.Errorf("Unexpected error: %v", err)
                return
            }

            // Verify user was created
            user, err := repo.GetByEmail(tt.userEmail)
            if err != nil {
                t.Errorf("User should be created: %v", err)
                return
            }

            if user.Name != tt.userName {
                t.Errorf("User name = %s; want %s", user.Name, tt.userName)
            }

            // Verify welcome email was sent
            if len(email.SentEmails) != 1 {
                t.Errorf("Expected 1 email, got %d", len(email.SentEmails))
                return
            }

            sentEmail := email.SentEmails[0]
            if sentEmail.Type != "welcome" {
                t.Errorf("Email type = %s; want welcome", sentEmail.Type)
            }
            if sentEmail.Recipient != tt.userEmail {
                t.Errorf("Email recipient = %s; want %s", sentEmail.Recipient, tt.userEmail)
            }
        })
    }
}
```

## HTTP Testing

Testing HTTP handlers and clients using Go's testing utilities.

### Testing HTTP Handlers

```go
// handlers.go
package handlers

import (
    "encoding/json"
    "net/http"
    "strconv"

    "github.com/gorilla/mux"
)

type User struct {
    ID    int    `json:"id"`
    Name  string `json:"name"`
    Email string `json:"email"`
}

type UserHandler struct {
    users map[int]User
    nextID int
}

func NewUserHandler() *UserHandler {
    return &UserHandler{
        users:  make(map[int]User),
        nextID: 1,
    }
}

func (h *UserHandler) CreateUser(w http.ResponseWriter, r *http.Request) {
    var user User
    if err := json.NewDecoder(r.Body).Decode(&user); err != nil {
        http.Error(w, "Invalid JSON", http.StatusBadRequest)
        return
    }

    if user.Name == "" || user.Email == "" {
        http.Error(w, "Name and email are required", http.StatusBadRequest)
        return
    }

    user.ID = h.nextID
    h.nextID++
    h.users[user.ID] = user

    w.Header().Set("Content-Type", "application/json")
    w.WriteHeader(http.StatusCreated)
    json.NewEncoder(w).Encode(user)
}

func (h *UserHandler) GetUser(w http.ResponseWriter, r *http.Request) {
    vars := mux.Vars(r)
    id, err := strconv.Atoi(vars["id"])
    if err != nil {
        http.Error(w, "Invalid user ID", http.StatusBadRequest)
        return
    }

    user, exists := h.users[id]
    if !exists {
        http.Error(w, "User not found", http.StatusNotFound)
        return
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(user)
}

func (h *UserHandler) ListUsers(w http.ResponseWriter, r *http.Request) {
    users := make([]User, 0, len(h.users))
    for _, user := range h.users {
        users = append(users, user)
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(users)
}
```

### HTTP Handler Tests

```go
// handlers_test.go
package handlers

import (
    "bytes"
    "encoding/json"
    "net/http"
    "net/http/httptest"
    "testing"

    "github.com/gorilla/mux"
)

func TestUserHandler_CreateUser(t *testing.T) {
    tests := []struct {
        name           string
        requestBody    interface{}
        expectedStatus int
        expectedUser   *User
        expectError    bool
    }{
        {
            name: "valid user",
            requestBody: User{
                Name:  "John Doe",
                Email: "<EMAIL>",
            },
            expectedStatus: http.StatusCreated,
            expectedUser: &User{
                ID:    1,
                Name:  "John Doe",
                Email: "<EMAIL>",
            },
        },
        {
            name: "missing name",
            requestBody: User{
                Email: "<EMAIL>",
            },
            expectedStatus: http.StatusBadRequest,
            expectError:    true,
        },
        {
            name: "missing email",
            requestBody: User{
                Name: "John Doe",
            },
            expectedStatus: http.StatusBadRequest,
            expectError:    true,
        },
        {
            name:           "invalid JSON",
            requestBody:    "invalid json",
            expectedStatus: http.StatusBadRequest,
            expectError:    true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            handler := NewUserHandler()

            // Prepare request body
            var body bytes.Buffer
            if err := json.NewEncoder(&body).Encode(tt.requestBody); err != nil {
                t.Fatalf("Failed to encode request body: %v", err)
            }

            // Create request
            req := httptest.NewRequest(http.MethodPost, "/users", &body)
            req.Header.Set("Content-Type", "application/json")

            // Create response recorder
            rr := httptest.NewRecorder()

            // Execute handler
            handler.CreateUser(rr, req)

            // Check status code
            if rr.Code != tt.expectedStatus {
                t.Errorf("Expected status %d, got %d", tt.expectedStatus, rr.Code)
            }

            if tt.expectError {
                return // Don't check response body for error cases
            }

            // Check response body
            var responseUser User
            if err := json.NewDecoder(rr.Body).Decode(&responseUser); err != nil {
                t.Fatalf("Failed to decode response: %v", err)
            }

            if responseUser.ID != tt.expectedUser.ID ||
               responseUser.Name != tt.expectedUser.Name ||
               responseUser.Email != tt.expectedUser.Email {
                t.Errorf("Expected user %+v, got %+v", tt.expectedUser, responseUser)
            }
        })
    }
}

func TestUserHandler_GetUser(t *testing.T) {
    handler := NewUserHandler()

    // Add test user
    testUser := User{ID: 1, Name: "John Doe", Email: "<EMAIL>"}
    handler.users[1] = testUser
    handler.nextID = 2

    tests := []struct {
        name           string
        userID         string
        expectedStatus int
        expectedUser   *User
    }{
        {
            name:           "existing user",
            userID:         "1",
            expectedStatus: http.StatusOK,
            expectedUser:   &testUser,
        },
        {
            name:           "non-existing user",
            userID:         "999",
            expectedStatus: http.StatusNotFound,
        },
        {
            name:           "invalid user ID",
            userID:         "invalid",
            expectedStatus: http.StatusBadRequest,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Create request with mux vars
            req := httptest.NewRequest(http.MethodGet, "/users/"+tt.userID, nil)
            rr := httptest.NewRecorder()

            // Set up mux vars
            req = mux.SetURLVars(req, map[string]string{"id": tt.userID})

            // Execute handler
            handler.GetUser(rr, req)

            // Check status code
            if rr.Code != tt.expectedStatus {
                t.Errorf("Expected status %d, got %d", tt.expectedStatus, rr.Code)
            }

            if tt.expectedUser == nil {
                return // Don't check response body for error cases
            }

            // Check response body
            var responseUser User
            if err := json.NewDecoder(rr.Body).Decode(&responseUser); err != nil {
                t.Fatalf("Failed to decode response: %v", err)
            }

            if responseUser != *tt.expectedUser {
                t.Errorf("Expected user %+v, got %+v", tt.expectedUser, responseUser)
            }
        })
    }
}
```

### Testing HTTP Clients

```go
// client.go
package client

import (
    "bytes"
    "encoding/json"
    "fmt"
    "net/http"
)

type APIClient struct {
    baseURL    string
    httpClient *http.Client
}

func NewAPIClient(baseURL string) *APIClient {
    return &APIClient{
        baseURL:    baseURL,
        httpClient: &http.Client{},
    }
}

func (c *APIClient) CreateUser(user User) (*User, error) {
    body, err := json.Marshal(user)
    if err != nil {
        return nil, err
    }

    resp, err := c.httpClient.Post(
        c.baseURL+"/users",
        "application/json",
        bytes.NewBuffer(body),
    )
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()

    if resp.StatusCode != http.StatusCreated {
        return nil, fmt.Errorf("unexpected status: %d", resp.StatusCode)
    }

    var createdUser User
    if err := json.NewDecoder(resp.Body).Decode(&createdUser); err != nil {
        return nil, err
    }

    return &createdUser, nil
}

func (c *APIClient) GetUser(id int) (*User, error) {
    resp, err := c.httpClient.Get(fmt.Sprintf("%s/users/%d", c.baseURL, id))
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()

    if resp.StatusCode == http.StatusNotFound {
        return nil, fmt.Errorf("user not found")
    }

    if resp.StatusCode != http.StatusOK {
        return nil, fmt.Errorf("unexpected status: %d", resp.StatusCode)
    }

    var user User
    if err := json.NewDecoder(resp.Body).Decode(&user); err != nil {
        return nil, err
    }

    return &user, nil
}
```

### HTTP Client Tests

```go
// client_test.go
package client

import (
    "encoding/json"
    "net/http"
    "net/http/httptest"
    "testing"
)

func TestAPIClient_CreateUser(t *testing.T) {
    tests := []struct {
        name           string
        user           User
        serverResponse func(w http.ResponseWriter, r *http.Request)
        expectError    bool
        expectedUser   *User
    }{
        {
            name: "successful creation",
            user: User{Name: "John Doe", Email: "<EMAIL>"},
            serverResponse: func(w http.ResponseWriter, r *http.Request) {
                // Verify request
                if r.Method != http.MethodPost {
                    t.Errorf("Expected POST, got %s", r.Method)
                }
                if r.Header.Get("Content-Type") != "application/json" {
                    t.Errorf("Expected application/json content type")
                }

                // Send response
                user := User{ID: 1, Name: "John Doe", Email: "<EMAIL>"}
                w.Header().Set("Content-Type", "application/json")
                w.WriteHeader(http.StatusCreated)
                json.NewEncoder(w).Encode(user)
            },
            expectedUser: &User{ID: 1, Name: "John Doe", Email: "<EMAIL>"},
        },
        {
            name: "server error",
            user: User{Name: "John Doe", Email: "<EMAIL>"},
            serverResponse: func(w http.ResponseWriter, r *http.Request) {
                w.WriteHeader(http.StatusInternalServerError)
            },
            expectError: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Create test server
            server := httptest.NewServer(http.HandlerFunc(tt.serverResponse))
            defer server.Close()

            // Create client
            client := NewAPIClient(server.URL)

            // Execute
            user, err := client.CreateUser(tt.user)

            // Verify
            if tt.expectError {
                if err == nil {
                    t.Error("Expected error but got none")
                }
                return
            }

            if err != nil {
                t.Errorf("Unexpected error: %v", err)
                return
            }

            if user.ID != tt.expectedUser.ID ||
               user.Name != tt.expectedUser.Name ||
               user.Email != tt.expectedUser.Email {
                t.Errorf("Expected user %+v, got %+v", tt.expectedUser, user)
            }
        })
    }
}
```

## Database Testing

Testing database interactions with proper setup and teardown.

### Database Test Setup

```go
// database_test.go
package database

import (
    "database/sql"
    "testing"

    _ "github.com/lib/pq"
    "github.com/golang-migrate/migrate/v4"
    "github.com/golang-migrate/migrate/v4/database/postgres"
    _ "github.com/golang-migrate/migrate/v4/source/file"
)

func setupTestDB(t *testing.T) *sql.DB {
    // Connect to test database
    db, err := sql.Open("postgres", "postgres://user:password@localhost/testdb?sslmode=disable")
    if err != nil {
        t.Fatalf("Failed to connect to test database: %v", err)
    }

    // Run migrations
    driver, err := postgres.WithInstance(db, &postgres.Config{})
    if err != nil {
        t.Fatalf("Failed to create migration driver: %v", err)
    }

    m, err := migrate.NewWithDatabaseInstance("file://migrations", "postgres", driver)
    if err != nil {
        t.Fatalf("Failed to create migration instance: %v", err)
    }

    if err := m.Up(); err != nil && err != migrate.ErrNoChange {
        t.Fatalf("Failed to run migrations: %v", err)
    }

    return db
}

func teardownTestDB(t *testing.T, db *sql.DB) {
    // Clean up test data
    tables := []string{"users", "orders", "products"}
    for _, table := range tables {
        _, err := db.Exec("DELETE FROM " + table)
        if err != nil {
            t.Logf("Failed to clean table %s: %v", table, err)
        }
    }

    db.Close()
}

func TestUserRepository_Create(t *testing.T) {
    db := setupTestDB(t)
    defer teardownTestDB(t, db)

    repo := NewUserRepository(db)

    user := User{
        Name:  "John Doe",
        Email: "<EMAIL>",
    }

    err := repo.Create(&user)
    if err != nil {
        t.Fatalf("Failed to create user: %v", err)
    }

    if user.ID == 0 {
        t.Error("Expected user ID to be set")
    }

    // Verify user was created
    var count int
    err = db.QueryRow("SELECT COUNT(*) FROM users WHERE email = $1", user.Email).Scan(&count)
    if err != nil {
        t.Fatalf("Failed to count users: %v", err)
    }

    if count != 1 {
        t.Errorf("Expected 1 user, got %d", count)
    }
}
```

### Transaction Testing

```go
func TestUserRepository_CreateWithTransaction(t *testing.T) {
    db := setupTestDB(t)
    defer teardownTestDB(t, db)

    repo := NewUserRepository(db)

    tests := []struct {
        name        string
        users       []User
        expectError bool
        expectedCount int
    }{
        {
            name: "successful transaction",
            users: []User{
                {Name: "User 1", Email: "<EMAIL>"},
                {Name: "User 2", Email: "<EMAIL>"},
            },
            expectedCount: 2,
        },
        {
            name: "failed transaction - duplicate email",
            users: []User{
                {Name: "User 1", Email: "<EMAIL>"},
                {Name: "User 2", Email: "<EMAIL>"}, // Duplicate
            },
            expectError:   true,
            expectedCount: 0, // Transaction should be rolled back
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Clean up before each test
            db.Exec("DELETE FROM users")

            tx, err := db.Begin()
            if err != nil {
                t.Fatalf("Failed to begin transaction: %v", err)
            }

            var txErr error
            for _, user := range tt.users {
                if err := repo.CreateWithTx(tx, &user); err != nil {
                    txErr = err
                    break
                }
            }

            if txErr != nil {
                tx.Rollback()
                if !tt.expectError {
                    t.Errorf("Unexpected error: %v", txErr)
                }
            } else {
                if err := tx.Commit(); err != nil {
                    t.Fatalf("Failed to commit transaction: %v", err)
                }
                if tt.expectError {
                    t.Error("Expected error but transaction succeeded")
                }
            }

            // Verify final state
            var count int
            db.QueryRow("SELECT COUNT(*) FROM users").Scan(&count)
            if count != tt.expectedCount {
                t.Errorf("Expected %d users, got %d", tt.expectedCount, count)
            }
        })
    }
}
```

## Best Practices

Guidelines for writing effective tests in Go.

### Test Organization

```go
// Good: Clear test structure
func TestUserService_CreateUser(t *testing.T) {
    // Arrange
    service := NewUserService(NewMockRepository())
    user := User{Name: "John", Email: "<EMAIL>"}

    // Act
    err := service.CreateUser(user)

    // Assert
    if err != nil {
        t.Errorf("Expected no error, got %v", err)
    }
}

// Good: Descriptive test names
func TestUserService_CreateUser_WithDuplicateEmail_ReturnsError(t *testing.T) {
    // Test implementation
}

// Good: One assertion per test
func TestAdd_PositiveNumbers(t *testing.T) {
    result := Add(2, 3)
    if result != 5 {
        t.Errorf("Add(2, 3) = %d; want 5", result)
    }
}

func TestAdd_NegativeNumbers(t *testing.T) {
    result := Add(-2, -3)
    if result != -5 {
        t.Errorf("Add(-2, -3) = %d; want -5", result)
    }
}
```

### Test Data Management

```go
// Good: Use test fixtures
func getTestUser() User {
    return User{
        ID:    1,
        Name:  "John Doe",
        Email: "<EMAIL>",
    }
}

func getTestUsers() []User {
    return []User{
        {ID: 1, Name: "John Doe", Email: "<EMAIL>"},
        {ID: 2, Name: "Jane Smith", Email: "<EMAIL>"},
        {ID: 3, Name: "Bob Johnson", Email: "<EMAIL>"},
    }
}

// Good: Use testdata directory
func loadTestData(t *testing.T, filename string) []byte {
    data, err := os.ReadFile(filepath.Join("testdata", filename))
    if err != nil {
        t.Fatalf("Failed to load test data %s: %v", filename, err)
    }
    return data
}

func TestParseJSON(t *testing.T) {
    data := loadTestData(t, "user.json")

    var user User
    err := json.Unmarshal(data, &user)
    if err != nil {
        t.Fatalf("Failed to parse JSON: %v", err)
    }

    // Test assertions...
}
```

### Error Testing

```go
// Good: Test both success and error cases
func TestDivide(t *testing.T) {
    tests := []struct {
        name        string
        a, b        float64
        expected    float64
        expectError bool
        errorMsg    string
    }{
        {
            name:     "normal division",
            a:        10,
            b:        2,
            expected: 5,
        },
        {
            name:        "division by zero",
            a:           10,
            b:           0,
            expectError: true,
            errorMsg:    "division by zero",
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            result, err := Divide(tt.a, tt.b)

            if tt.expectError {
                if err == nil {
                    t.Error("Expected error but got none")
                    return
                }
                if !strings.Contains(err.Error(), tt.errorMsg) {
                    t.Errorf("Expected error containing %q, got %q", tt.errorMsg, err.Error())
                }
                return
            }

            if err != nil {
                t.Errorf("Unexpected error: %v", err)
                return
            }

            if result != tt.expected {
                t.Errorf("Expected %f, got %f", tt.expected, result)
            }
        })
    }
}
```

### Test Helpers

```go
// Good: Create reusable test helpers
func assertNoError(t *testing.T, err error) {
    t.Helper()
    if err != nil {
        t.Fatalf("Expected no error, got %v", err)
    }
}

func assertError(t *testing.T, err error, expectedMsg string) {
    t.Helper()
    if err == nil {
        t.Fatal("Expected error but got none")
    }
    if !strings.Contains(err.Error(), expectedMsg) {
        t.Errorf("Expected error containing %q, got %q", expectedMsg, err.Error())
    }
}

func assertEqual[T comparable](t *testing.T, got, want T) {
    t.Helper()
    if got != want {
        t.Errorf("Got %v, want %v", got, want)
    }
}

// Usage
func TestUserService_CreateUser(t *testing.T) {
    service := NewUserService(NewMockRepository())
    user := getTestUser()

    err := service.CreateUser(user)
    assertNoError(t, err)

    createdUser, err := service.GetUser(user.ID)
    assertNoError(t, err)
    assertEqual(t, createdUser.Name, user.Name)
    assertEqual(t, createdUser.Email, user.Email)
}
```

### Performance Testing

```go
// Good: Benchmark critical paths
func BenchmarkUserService_CreateUser(b *testing.B) {
    service := NewUserService(NewMockRepository())
    user := getTestUser()

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        user.ID = i // Avoid duplicates
        service.CreateUser(user)
    }
}

// Good: Memory benchmarks
func BenchmarkStringBuilder(b *testing.B) {
    b.Run("StringBuilder", func(b *testing.B) {
        for i := 0; i < b.N; i++ {
            var builder strings.Builder
            for j := 0; j < 100; j++ {
                builder.WriteString("test")
            }
            _ = builder.String()
        }
    })

    b.Run("StringConcatenation", func(b *testing.B) {
        for i := 0; i < b.N; i++ {
            result := ""
            for j := 0; j < 100; j++ {
                result += "test"
            }
        }
    })
}
```

## Advanced Patterns

Advanced testing techniques and patterns.

### Property-Based Testing

```go
// Property-based testing example
func TestAdd_Properties(t *testing.T) {
    // Property: Addition is commutative
    t.Run("Commutative", func(t *testing.T) {
        for i := 0; i < 100; i++ {
            a := rand.Intn(1000) - 500
            b := rand.Intn(1000) - 500

            result1 := Add(a, b)
            result2 := Add(b, a)

            if result1 != result2 {
                t.Errorf("Addition not commutative: Add(%d, %d) = %d, Add(%d, %d) = %d",
                    a, b, result1, b, a, result2)
            }
        }
    })

    // Property: Addition is associative
    t.Run("Associative", func(t *testing.T) {
        for i := 0; i < 100; i++ {
            a := rand.Intn(100)
            b := rand.Intn(100)
            c := rand.Intn(100)

            result1 := Add(Add(a, b), c)
            result2 := Add(a, Add(b, c))

            if result1 != result2 {
                t.Errorf("Addition not associative: Add(Add(%d, %d), %d) = %d, Add(%d, Add(%d, %d)) = %d",
                    a, b, c, result1, a, b, c, result2)
            }
        }
    })

    // Property: Identity element
    t.Run("Identity", func(t *testing.T) {
        for i := 0; i < 100; i++ {
            a := rand.Intn(1000) - 500

            result := Add(a, 0)

            if result != a {
                t.Errorf("Zero not identity: Add(%d, 0) = %d, expected %d", a, result, a)
            }
        }
    })
}
```

### Fuzzing

```go
// Fuzz testing (Go 1.18+)
func FuzzAdd(f *testing.F) {
    // Seed corpus
    f.Add(1, 2)
    f.Add(-1, -2)
    f.Add(0, 0)
    f.Add(math.MaxInt32, 1)

    f.Fuzz(func(t *testing.T, a, b int) {
        result := Add(a, b)

        // Property: result should be sum of inputs
        expected := int64(a) + int64(b)
        if int64(result) != expected {
            t.Errorf("Add(%d, %d) = %d, expected %d", a, b, result, expected)
        }

        // Property: commutative
        if Add(a, b) != Add(b, a) {
            t.Errorf("Addition not commutative: Add(%d, %d) != Add(%d, %d)", a, b, b, a)
        }
    })
}

func FuzzParseJSON(f *testing.F) {
    // Seed with valid JSON
    f.Add(`{"name": "John", "age": 30}`)
    f.Add(`{"name": "", "age": 0}`)

    f.Fuzz(func(t *testing.T, data string) {
        var result map[string]interface{}
        err := json.Unmarshal([]byte(data), &result)

        // Should not panic
        if err == nil {
            // If parsing succeeds, re-marshaling should work
            _, err := json.Marshal(result)
            if err != nil {
                t.Errorf("Re-marshaling failed: %v", err)
            }
        }
    })
}
```

### Test Doubles and Dependency Injection

```go
// Advanced mocking with behavior verification
type MockEmailService struct {
    calls []EmailCall
}

type EmailCall struct {
    Method string
    Args   []interface{}
}

func (m *MockEmailService) SendEmail(to, subject, body string) error {
    m.calls = append(m.calls, EmailCall{
        Method: "SendEmail",
        Args:   []interface{}{to, subject, body},
    })
    return nil
}

func (m *MockEmailService) VerifyEmailSent(t *testing.T, to, subject string) {
    t.Helper()
    for _, call := range m.calls {
        if call.Method == "SendEmail" &&
           call.Args[0] == to &&
           call.Args[1] == subject {
            return
        }
    }
    t.Errorf("Expected email to %s with subject %s, but not found", to, subject)
}

func (m *MockEmailService) VerifyNoEmailsSent(t *testing.T) {
    t.Helper()
    if len(m.calls) > 0 {
        t.Errorf("Expected no emails sent, but %d were sent", len(m.calls))
    }
}

// Test with behavior verification
func TestUserService_RegisterUser_SendsWelcomeEmail(t *testing.T) {
    mockEmail := &MockEmailService{}
    service := NewUserService(NewMockRepository(), mockEmail)

    user := User{Name: "John", Email: "<EMAIL>"}
    err := service.RegisterUser(user)

    assertNoError(t, err)
    mockEmail.VerifyEmailSent(t, "<EMAIL>", "Welcome")
}
```

### Integration Test Patterns

```go
// Integration test with external dependencies
func TestUserAPI_Integration(t *testing.T) {
    if testing.Short() {
        t.Skip("Skipping integration test in short mode")
    }

    // Setup test database
    db := setupTestDB(t)
    defer teardownTestDB(t, db)

    // Setup test server
    handler := NewUserHandler(NewUserRepository(db))
    server := httptest.NewServer(handler)
    defer server.Close()

    // Create API client
    client := NewAPIClient(server.URL)

    // Test full flow
    user := User{Name: "John Doe", Email: "<EMAIL>"}

    // Create user
    createdUser, err := client.CreateUser(user)
    assertNoError(t, err)
    assertEqual(t, createdUser.Name, user.Name)
    assertEqual(t, createdUser.Email, user.Email)

    // Get user
    retrievedUser, err := client.GetUser(createdUser.ID)
    assertNoError(t, err)
    assertEqual(t, retrievedUser.ID, createdUser.ID)
    assertEqual(t, retrievedUser.Name, createdUser.Name)
    assertEqual(t, retrievedUser.Email, createdUser.Email)
}

// Contract testing
func TestUserRepository_Contract(t *testing.T) {
    implementations := []struct {
        name string
        repo UserRepository
    }{
        {"Memory", NewMemoryUserRepository()},
        {"Database", NewDatabaseUserRepository(setupTestDB(t))},
    }

    for _, impl := range implementations {
        t.Run(impl.name, func(t *testing.T) {
            testUserRepositoryContract(t, impl.repo)
        })
    }
}

func testUserRepositoryContract(t *testing.T, repo UserRepository) {
    // Test contract: Create should set ID
    user := User{Name: "John", Email: "<EMAIL>"}
    err := repo.Create(&user)
    assertNoError(t, err)

    if user.ID == 0 {
        t.Error("Create should set user ID")
    }

    // Test contract: Get should return created user
    retrieved, err := repo.GetByID(user.ID)
    assertNoError(t, err)
    assertEqual(t, retrieved.Name, user.Name)
    assertEqual(t, retrieved.Email, user.Email)

    // Test contract: Get non-existent should return error
    _, err = repo.GetByID(99999)
    if err == nil {
        t.Error("GetByID for non-existent user should return error")
    }
}
```

This comprehensive guide covers all aspects of testing in Go, from basic unit tests to advanced patterns like property-based testing and fuzzing. Understanding these concepts will help you build robust, well-tested Go applications with confidence in their correctness and reliability.