# Naming Conventions in Go

This comprehensive guide covers all naming conventions in Go, from basic variable names to complex package structures, following Go community standards and best practices.

## Table of Contents

1. [General Principles](#general-principles)
2. [Visibility Rules](#visibility-rules)
3. [Variable Names](#variable-names)
4. [Constant Names](#constant-names)
5. [Function Names](#function-names)
6. [Type Names](#type-names)
7. [Interface Names](#interface-names)
8. [Package Names](#package-names)
9. [File Names](#file-names)
10. [Method Names](#method-names)
11. [Receiver Names](#receiver-names)
12. [Error Names](#error-names)
13. [Test Names](#test-names)
14. [Common Patterns](#common-patterns)
15. [Anti-patterns](#anti-patterns)
16. [Industry Standards](#industry-standards)

## General Principles

Go naming conventions prioritize clarity, simplicity, and consistency.

### Core Principles

```go
// 1. Use camelCase for multi-word names
var userName string          // Good
var user_name string         // Avoid (snake_case)
var UserName string          // Good (exported)

// 2. Prefer shorter names for shorter scopes
func process() {
    for i, v := range items {    // Good: short scope, short names
        // process v
    }
}

var globalConfiguration Config   // Good: longer scope, descriptive name

// 3. Use descriptive names for longer scopes
func processUserData() {
    var authenticatedUsers []User     // Good: clear purpose
    var totalProcessedCount int       // Good: descriptive
    
    // Long-lived variables need descriptive names
}

// 4. Avoid abbreviations unless well-known
var url string               // Good: URL is well-known
var db *sql.DB              // Good: DB is well-known
var usr string              // Avoid: unclear abbreviation
var cfg Config              // Avoid: unclear abbreviation
```

### Consistency Rules

```go
// Be consistent within your codebase
type User struct {
    ID       int64     // Consistent: use ID everywhere
    UserName string    // Consistent: use UserName everywhere
    Email    string
}

// Don't mix naming styles
type BadUser struct {
    id       int64     // Inconsistent: mixing ID and id
    userName string    // Inconsistent: mixing UserName and userName
    Email    string
}
```

## Visibility Rules

Go uses capitalization to determine visibility (exported vs unexported).

### Exported (Public) Names

```go
// Exported variables (visible to other packages)
var PublicVariable int = 42
var ExportedConfig Config

// Exported constants
const PublicConstant = "visible to other packages"
const MaxRetries = 3

// Exported types
type PublicStruct struct {
    PublicField    string  // Exported field
    privateField   int     // Unexported field
}

// Exported functions
func PublicFunction() {
    // Visible to other packages
}

// Exported methods
func (p *PublicStruct) PublicMethod() {
    // Visible to other packages
}
```

### Unexported (Private) Names

```go
// Unexported variables (only visible within package)
var privateVariable int = 42
var internalConfig config

// Unexported constants
const privateConstant = "only visible in this package"
const maxInternalRetries = 5

// Unexported types
type privateStruct struct {
    field string
}

// Unexported functions
func privateFunction() {
    // Only visible within this package
}

// Unexported methods
func (p *privateStruct) privateMethod() {
    // Only visible within this package
}
```

### Mixed Visibility

```go
type User struct {
    // Exported fields
    ID       int64     `json:"id"`
    Name     string    `json:"name"`
    Email    string    `json:"email"`
    
    // Unexported fields (internal state)
    password string
    salt     string
    created  time.Time
}

// Exported methods
func (u *User) GetID() int64 {
    return u.ID
}

func (u *User) SetPassword(password string) error {
    u.password = hashPassword(password, u.salt)
    return nil
}

// Unexported methods (internal helpers)
func (u *User) hashPassword(password, salt string) string {
    // Internal implementation
    return ""
}
```

## Variable Names

Variable naming depends on scope and purpose.

### Local Variables

```go
func processData() {
    // Short names for short scopes
    for i, v := range items {
        // i and v are fine for loop variables
        process(v)
    }
    
    // Descriptive names for longer-lived variables
    var authenticatedUsers []User
    var processingErrors []error
    var totalProcessedCount int
    
    // Context-specific names
    var httpClient *http.Client
    var dbConnection *sql.DB
    var configFile *os.File
}
```

### Package-Level Variables

```go
// Descriptive names for package-level variables
var (
    DefaultTimeout     = 30 * time.Second
    MaxRetryAttempts   = 3
    GlobalUserCache    = make(map[int64]*User)
    DatabaseConnection *sql.DB
)

// Avoid generic names at package level
var (
    // Avoid these
    timeout = 30        // Too generic
    max     = 3         // Too generic
    cache   = make(map[int64]*User)  // Too generic
)
```

### Boolean Variables

```go
// Use "is", "has", "can", "should" prefixes
var isAuthenticated bool
var hasPermission bool
var canEdit bool
var shouldRetry bool
var isValid bool
var hasError bool

// Specific boolean names
var userLoggedIn bool
var dataLoaded bool
var connectionEstablished bool
var requestCompleted bool

// Avoid negative booleans when possible
var isEnabled bool          // Good
var isDisabled bool         // Avoid (prefer !isEnabled)
```

### Collection Variables

```go
// Use plural nouns for collections
var users []User
var connections []*Connection
var errorMessages []string
var activeConnections map[string]*Connection

// Specific collection names
var authenticatedUsers []User
var failedRequests []Request
var pendingOrders []Order
var usersByID map[int64]*User
```

## Constant Names

Constants follow specific naming patterns based on their purpose.

### Basic Constants

```go
// Descriptive constant names
const (
    DefaultPort        = 8080
    MaxRetryAttempts   = 3
    DatabaseDriverName = "postgres"
    APIVersion         = "v1"
    DefaultTimeout     = 30 * time.Second
)

// Avoid abbreviations
const (
    // Good
    DefaultHTTPTimeout = 30 * time.Second
    MaxDatabaseConnections = 100
    
    // Avoid
    DefHTTPTO = 30 * time.Second
    MaxDBConns = 100
)
```

### Enum-style Constants

```go
// Status constants with meaningful prefixes
const (
    StatusPending   = "pending"
    StatusRunning   = "running"
    StatusCompleted = "completed"
    StatusFailed    = "failed"
)

// HTTP status constants
const (
    StatusOK                  = 200
    StatusCreated             = 201
    StatusBadRequest          = 400
    StatusUnauthorized        = 401
    StatusInternalServerError = 500
)

// Permission constants
const (
    PermissionRead    = "read"
    PermissionWrite   = "write"
    PermissionExecute = "execute"
    PermissionAdmin   = "admin"
)
```

### Error Constants

```go
// Error message constants
const (
    ErrMsgUserNotFound     = "user not found"
    ErrMsgInvalidPassword  = "invalid password"
    ErrMsgDatabaseError    = "database connection failed"
    ErrMsgUnauthorized     = "unauthorized access"
    ErrMsgInvalidInput     = "invalid input provided"
)

// Error code constants
const (
    ErrCodeUserNotFound     = 1001
    ErrCodeInvalidPassword  = 1002
    ErrCodeDatabaseError    = 2001
    ErrCodeUnauthorized     = 3001
)
```

## Function Names

Function names should clearly describe what the function does.

### Action-Based Names

```go
// Use verbs for functions that perform actions
func CreateUser(name, email string) (*User, error) { }
func UpdateUserEmail(userID int64, email string) error { }
func DeleteUser(userID int64) error { }
func ValidateEmail(email string) bool { }
func ProcessPayment(amount float64) error { }

// Getter functions (omit "Get" prefix when clear)
func (u *User) Name() string { return u.name }
func (u *User) Email() string { return u.email }
func (u *User) ID() int64 { return u.id }

// Setter functions
func (u *User) SetName(name string) { u.name = name }
func (u *User) SetEmail(email string) { u.email = email }
```

### Boolean Functions

```go
// Boolean functions should read like questions
func IsValidEmail(email string) bool { }
func HasPermission(user *User, permission string) bool { }
func CanEdit(user *User, resource *Resource) bool { }
func ShouldRetry(err error) bool { }
func IsAuthenticated(user *User) bool { }

// Specific boolean functions
func UserExists(userID int64) bool { }
func EmailTaken(email string) bool { }
func ConnectionActive(conn *Connection) bool { }
```

### Constructor Functions

```go
// Use "New" prefix for constructors
func NewUser(name, email string) *User { }
func NewHTTPClient(timeout time.Duration) *http.Client { }
func NewDatabaseConnection(dsn string) (*sql.DB, error) { }

// Factory functions
func CreateDefaultConfig() *Config { }
func BuildUserFromJSON(data []byte) (*User, error) { }
func ParseConfigFile(filename string) (*Config, error) { }
```

## Type Names

Type names should be clear and follow Go conventions.

### Struct Names

```go
// Use nouns for struct names
type User struct { }
type Order struct { }
type PaymentProcessor struct { }
type DatabaseConnection struct { }
type HTTPClient struct { }

// Avoid generic names
type Data struct { }     // Too generic
type Info struct { }     // Too generic
type Item struct { }     // Too generic (unless truly generic)

// Use descriptive names
type UserAccount struct { }
type OrderItem struct { }
type PaymentMethod struct { }
type DatabaseConfig struct { }
```

### Custom Types

```go
// Domain-specific types
type UserID int64
type ProductID int64
type OrderID int64
type EmailAddress string
type PhoneNumber string
type Currency string

// Measurement types
type Distance float64
type Weight float64
type Temperature float64
type Duration time.Duration

// Status types
type OrderStatus string
type PaymentStatus string
type UserRole string
```
