# Concurrency in Go

This comprehensive guide covers concurrency in Go, including goroutines, channels, synchronization primitives, and advanced concurrency patterns for building efficient concurrent applications.

## Table of Contents

1. [Overview](#overview)
2. [Goroutines](#goroutines)
3. [Channels](#channels)
4. [Channel Patterns](#channel-patterns)
5. [Select Statement](#select-statement)
6. [Synchronization Primitives](#synchronization-primitives)
7. [Context Package](#context-package)
8. [Concurrency Patterns](#concurrency-patterns)
9. [Error Handling in Concurrent Code](#error-handling-in-concurrent-code)
10. [Performance and Best Practices](#performance-and-best-practices)
11. [Common Pitfalls](#common-pitfalls)
12. [Testing Concurrent Code](#testing-concurrent-code)

## Overview

Go's concurrency model is based on CSP (Communicating Sequential Processes) and provides powerful primitives for building concurrent applications.

### Key Concepts

```go
// Goroutines: Lightweight threads managed by Go runtime
// Channels: Communication mechanism between goroutines
// Select: Multiplexing channel operations
// Sync package: Traditional synchronization primitives
// Context: Cancellation and timeout management
```

### Concurrency vs Parallelism

| Aspect | Concurrency | Parallelism |
|--------|-------------|-------------|
| **Definition** | Dealing with multiple things at once | Doing multiple things at once |
| **Focus** | Structure and composition | Execution |
| **Hardware** | Can work on single core | Requires multiple cores |
| **Go Support** | Goroutines, channels | GOMAXPROCS, runtime scheduling |

### Go's Concurrency Philosophy

```go
// "Don't communicate by sharing memory; share memory by communicating"
// - Rob Pike

// Traditional approach (sharing memory)
var counter int
var mutex sync.Mutex

func increment() {
    mutex.Lock()
    counter++
    mutex.Unlock()
}

// Go approach (communicating)
func incrementChannel(ch chan int) {
    ch <- 1 // Send increment signal
}
```

## Goroutines

Lightweight threads managed by the Go runtime.

### Basic Goroutines

```go
import (
    "fmt"
    "time"
)

// Simple goroutine
func sayHello() {
    fmt.Println("Hello from goroutine!")
}

func demonstrateBasicGoroutines() {
    // Start a goroutine
    go sayHello()

    // Start anonymous goroutine
    go func() {
        fmt.Println("Hello from anonymous goroutine!")
    }()

    // Start goroutine with parameters
    go func(name string) {
        fmt.Printf("Hello %s from parameterized goroutine!\n", name)
    }("Alice")

    // Wait for goroutines to complete
    time.Sleep(100 * time.Millisecond)
}

// Goroutine with loop
func printNumbers(prefix string) {
    for i := 1; i <= 5; i++ {
        fmt.Printf("%s: %d\n", prefix, i)
        time.Sleep(100 * time.Millisecond)
    }
}

func demonstrateMultipleGoroutines() {
    go printNumbers("Goroutine 1")
    go printNumbers("Goroutine 2")
    go printNumbers("Goroutine 3")

    // Wait for all goroutines to complete
    time.Sleep(600 * time.Millisecond)
}
```

### Goroutine Lifecycle

```go
import "runtime"

// Goroutine states and lifecycle
func demonstrateGoroutineLifecycle() {
    fmt.Printf("Initial goroutines: %d\n", runtime.NumGoroutine())

    // Create multiple goroutines
    for i := 0; i < 10; i++ {
        go func(id int) {
            fmt.Printf("Goroutine %d starting\n", id)
            time.Sleep(time.Duration(id*100) * time.Millisecond)
            fmt.Printf("Goroutine %d finishing\n", id)
        }(i)
    }

    fmt.Printf("After creating goroutines: %d\n", runtime.NumGoroutine())

    // Wait for goroutines to complete
    time.Sleep(1 * time.Second)
    fmt.Printf("Final goroutines: %d\n", runtime.NumGoroutine())
}

// Goroutine with cleanup
func workerWithCleanup(id int, done chan bool) {
    defer func() {
        fmt.Printf("Worker %d cleaning up\n", id)
        done <- true
    }()

    fmt.Printf("Worker %d starting\n", id)

    // Simulate work
    for i := 0; i < 3; i++ {
        fmt.Printf("Worker %d: task %d\n", id, i+1)
        time.Sleep(200 * time.Millisecond)
    }

    fmt.Printf("Worker %d finished\n", id)
}

func demonstrateGoroutineCleanup() {
    done := make(chan bool)

    // Start workers
    for i := 1; i <= 3; i++ {
        go workerWithCleanup(i, done)
    }

    // Wait for all workers to complete
    for i := 1; i <= 3; i++ {
        <-done
    }

    fmt.Println("All workers completed")
}
```

### Goroutine Scheduling

```go
import "runtime"

// Understanding goroutine scheduling
func demonstrateScheduling() {
    // Get number of CPUs
    numCPU := runtime.NumCPU()
    fmt.Printf("Number of CPUs: %d\n", numCPU)

    // Get current GOMAXPROCS
    maxProcs := runtime.GOMAXPROCS(0)
    fmt.Printf("GOMAXPROCS: %d\n", maxProcs)

    // CPU-intensive goroutines
    for i := 0; i < numCPU*2; i++ {
        go func(id int) {
            for j := 0; j < 1000000; j++ {
                // CPU-intensive work
                _ = j * j
            }
            fmt.Printf("CPU worker %d completed\n", id)
        }(i)
    }

    // I/O-intensive goroutines
    for i := 0; i < 10; i++ {
        go func(id int) {
            time.Sleep(100 * time.Millisecond) // Simulate I/O
            fmt.Printf("I/O worker %d completed\n", id)
        }(i)
    }

    time.Sleep(2 * time.Second)
}

// Cooperative scheduling with runtime.Gosched()
func demonstrateCooperativeScheduling() {
    go func() {
        for i := 0; i < 5; i++ {
            fmt.Printf("Goroutine 1: %d\n", i)
            runtime.Gosched() // Yield to other goroutines
        }
    }()

    go func() {
        for i := 0; i < 5; i++ {
            fmt.Printf("Goroutine 2: %d\n", i)
            runtime.Gosched() // Yield to other goroutines
        }
    }()

    time.Sleep(100 * time.Millisecond)
}
```

## Channels

Go's primary mechanism for communication between goroutines.

### Basic Channel Operations

```go
// Channel creation and basic operations
func demonstrateBasicChannels() {
    // Create a channel
    ch := make(chan int)

    // Send and receive in separate goroutines
    go func() {
        ch <- 42 // Send value to channel
        fmt.Println("Sent 42 to channel")
    }()

    go func() {
        value := <-ch // Receive value from channel
        fmt.Printf("Received %d from channel\n", value)
    }()

    time.Sleep(100 * time.Millisecond)
}

// Buffered channels
func demonstrateBufferedChannels() {
    // Create buffered channel
    ch := make(chan string, 3)

    // Send values (won't block until buffer is full)
    ch <- "first"
    ch <- "second"
    ch <- "third"

    fmt.Printf("Channel length: %d, capacity: %d\n", len(ch), cap(ch))

    // Receive values
    fmt.Println("Received:", <-ch)
    fmt.Println("Received:", <-ch)
    fmt.Println("Received:", <-ch)
}

// Channel directions
func sender(ch chan<- int) { // Send-only channel
    for i := 1; i <= 5; i++ {
        ch <- i
        fmt.Printf("Sent: %d\n", i)
    }
    close(ch)
}

func receiver(ch <-chan int) { // Receive-only channel
    for value := range ch {
        fmt.Printf("Received: %d\n", value)
    }
    fmt.Println("Channel closed")
}

func demonstrateChannelDirections() {
    ch := make(chan int)

    go sender(ch)
    go receiver(ch)

    time.Sleep(100 * time.Millisecond)
}
```

### Channel Closing and Range

```go
// Proper channel closing
func producer(ch chan<- int) {
    defer close(ch) // Always close channels when done sending

    for i := 1; i <= 10; i++ {
        ch <- i
        time.Sleep(50 * time.Millisecond)
    }
}

func consumer(ch <-chan int) {
    // Range over channel until it's closed
    for value := range ch {
        fmt.Printf("Processing: %d\n", value)
    }
    fmt.Println("Producer finished, channel closed")
}

func demonstrateChannelClosing() {
    ch := make(chan int)

    go producer(ch)
    consumer(ch) // Run in main goroutine
}

// Check if channel is closed
func demonstrateChannelStatus() {
    ch := make(chan int, 2)

    ch <- 1
    ch <- 2
    close(ch)

    // Receive with ok idiom
    for {
        value, ok := <-ch
        if !ok {
            fmt.Println("Channel is closed")
            break
        }
        fmt.Printf("Received: %d\n", value)
    }
}
```

## Channel Patterns

Common patterns for using channels effectively.

### Worker Pool Pattern

```go
// Worker pool for concurrent processing
type Job struct {
    ID   int
    Data string
}

type Result struct {
    JobID int
    Value string
    Error error
}

func worker(id int, jobs <-chan Job, results chan<- Result) {
    for job := range jobs {
        fmt.Printf("Worker %d processing job %d\n", id, job.ID)

        // Simulate work
        time.Sleep(time.Duration(rand.Intn(100)) * time.Millisecond)

        // Process job
        result := Result{
            JobID: job.ID,
            Value: fmt.Sprintf("Processed: %s", job.Data),
        }

        results <- result
    }
}

func demonstrateWorkerPool() {
    const numWorkers = 3
    const numJobs = 10

    jobs := make(chan Job, numJobs)
    results := make(chan Result, numJobs)

    // Start workers
    for w := 1; w <= numWorkers; w++ {
        go worker(w, jobs, results)
    }

    // Send jobs
    for j := 1; j <= numJobs; j++ {
        jobs <- Job{
            ID:   j,
            Data: fmt.Sprintf("job-%d", j),
        }
    }
    close(jobs)

    // Collect results
    for r := 1; r <= numJobs; r++ {
        result := <-results
        fmt.Printf("Result: Job %d -> %s\n", result.JobID, result.Value)
    }
}
```

### Pipeline Pattern

```go
// Pipeline stages
func generateNumbers(out chan<- int) {
    defer close(out)
    for i := 1; i <= 10; i++ {
        out <- i
    }
}

func squareNumbers(in <-chan int, out chan<- int) {
    defer close(out)
    for num := range in {
        out <- num * num
    }
}

func printNumbers(in <-chan int) {
    for num := range in {
        fmt.Printf("Final result: %d\n", num)
    }
}

func demonstratePipeline() {
    // Create pipeline stages
    numbers := make(chan int)
    squares := make(chan int)

    // Start pipeline
    go generateNumbers(numbers)
    go squareNumbers(numbers, squares)
    printNumbers(squares) // Run in main goroutine
}

// Fan-out/Fan-in pattern
func fanOut(in <-chan int, out1, out2 chan<- int) {
    defer close(out1)
    defer close(out2)

    for num := range in {
        // Distribute work to multiple channels
        select {
        case out1 <- num:
        case out2 <- num:
        }
    }
}

func fanIn(in1, in2 <-chan int, out chan<- int) {
    defer close(out)

    for {
        select {
        case val, ok := <-in1:
            if !ok {
                in1 = nil
            } else {
                out <- val * 2 // Process from first channel
            }
        case val, ok := <-in2:
            if !ok {
                in2 = nil
            } else {
                out <- val * 3 // Process from second channel
            }
        }

        if in1 == nil && in2 == nil {
            break
        }
    }
}

func demonstrateFanOutFanIn() {
    input := make(chan int)
    out1 := make(chan int)
    out2 := make(chan int)
    result := make(chan int)

    // Start fan-out
    go fanOut(input, out1, out2)

    // Start fan-in
    go fanIn(out1, out2, result)

    // Send input
    go func() {
        defer close(input)
        for i := 1; i <= 10; i++ {
            input <- i
        }
    }()

    // Collect results
    for val := range result {
        fmt.Printf("Fan result: %d\n", val)
    }
}
```

### Producer-Consumer Pattern

```go
// Producer-consumer with buffered channel
func producer(name string, ch chan<- string, count int) {
    defer close(ch)

    for i := 1; i <= count; i++ {
        item := fmt.Sprintf("%s-item-%d", name, i)
        ch <- item
        fmt.Printf("Produced: %s\n", item)
        time.Sleep(100 * time.Millisecond)
    }
}

func consumer(name string, ch <-chan string) {
    for item := range ch {
        fmt.Printf("Consumer %s processing: %s\n", name, item)
        time.Sleep(150 * time.Millisecond) // Slower than producer
    }
}

func demonstrateProducerConsumer() {
    ch := make(chan string, 5) // Buffered channel

    go producer("Producer-1", ch, 10)
    consumer("Consumer-1", ch)
}

// Multiple producers, single consumer
func demonstrateMultipleProducers() {
    ch := make(chan string, 10)

    // Start multiple producers
    go producer("P1", ch, 5)
    go producer("P2", ch, 5)
    go producer("P3", ch, 5)

    // Single consumer
    itemCount := 0
    for item := range ch {
        fmt.Printf("Consumed: %s\n", item)
        itemCount++
        if itemCount >= 15 { // Total items from all producers
            break
        }
    }
}
```

## Select Statement

Multiplexing channel operations.

### Basic Select

```go
// Basic select statement
func demonstrateBasicSelect() {
    ch1 := make(chan string)
    ch2 := make(chan string)

    go func() {
        time.Sleep(100 * time.Millisecond)
        ch1 <- "message from ch1"
    }()

    go func() {
        time.Sleep(200 * time.Millisecond)
        ch2 <- "message from ch2"
    }()

    // Select waits for one of the channels to be ready
    select {
    case msg1 := <-ch1:
        fmt.Println("Received:", msg1)
    case msg2 := <-ch2:
        fmt.Println("Received:", msg2)
    }
}

// Select with default case
func demonstrateSelectWithDefault() {
    ch := make(chan string)

    select {
    case msg := <-ch:
        fmt.Println("Received:", msg)
    default:
        fmt.Println("No message received (non-blocking)")
    }

    // Send a message
    go func() {
        ch <- "hello"
    }()

    time.Sleep(10 * time.Millisecond)

    select {
    case msg := <-ch:
        fmt.Println("Received:", msg)
    default:
        fmt.Println("No message received")
    }
}

// Select with timeout
func demonstrateSelectWithTimeout() {
    ch := make(chan string)

    go func() {
        time.Sleep(200 * time.Millisecond)
        ch <- "delayed message"
    }()

    select {
    case msg := <-ch:
        fmt.Println("Received:", msg)
    case <-time.After(100 * time.Millisecond):
        fmt.Println("Timeout: no message received")
    }
}
```

### Advanced Select Patterns

```go
// Select in loop for multiple operations
func demonstrateSelectLoop() {
    ch1 := make(chan int)
    ch2 := make(chan int)
    quit := make(chan bool)

    // Producer for ch1
    go func() {
        for i := 1; i <= 5; i++ {
            ch1 <- i
            time.Sleep(100 * time.Millisecond)
        }
        close(ch1)
    }()

    // Producer for ch2
    go func() {
        for i := 10; i <= 15; i++ {
            ch2 <- i
            time.Sleep(150 * time.Millisecond)
        }
        close(ch2)
    }()

    // Quit signal after some time
    go func() {
        time.Sleep(800 * time.Millisecond)
        quit <- true
    }()

    // Select loop
    for {
        select {
        case val1, ok := <-ch1:
            if !ok {
                ch1 = nil // Disable this case
            } else {
                fmt.Printf("From ch1: %d\n", val1)
            }
        case val2, ok := <-ch2:
            if !ok {
                ch2 = nil // Disable this case
            } else {
                fmt.Printf("From ch2: %d\n", val2)
            }
        case <-quit:
            fmt.Println("Quit signal received")
            return
        }

        // Exit when both channels are closed
        if ch1 == nil && ch2 == nil {
            fmt.Println("Both channels closed")
            return
        }
    }
}

// Select for load balancing
func loadBalancer(requests <-chan string, workers ...chan<- string) {
    for request := range requests {
        select {
        case workers[0] <- request:
            fmt.Printf("Sent %s to worker 1\n", request)
        case workers[1] <- request:
            fmt.Printf("Sent %s to worker 2\n", request)
        case workers[2] <- request:
            fmt.Printf("Sent %s to worker 3\n", request)
        }
    }

    // Close all worker channels
    for _, worker := range workers {
        close(worker)
    }
}

func demonstrateLoadBalancer() {
    requests := make(chan string)
    worker1 := make(chan string)
    worker2 := make(chan string)
    worker3 := make(chan string)

    // Start workers
    go func() {
        for req := range worker1 {
            fmt.Printf("Worker 1 processing: %s\n", req)
            time.Sleep(100 * time.Millisecond)
        }
    }()

    go func() {
        for req := range worker2 {
            fmt.Printf("Worker 2 processing: %s\n", req)
            time.Sleep(100 * time.Millisecond)
        }
    }()

    go func() {
        for req := range worker3 {
            fmt.Printf("Worker 3 processing: %s\n", req)
            time.Sleep(100 * time.Millisecond)
        }
    }()

    // Start load balancer
    go loadBalancer(requests, worker1, worker2, worker3)

    // Send requests
    for i := 1; i <= 10; i++ {
        requests <- fmt.Sprintf("request-%d", i)
    }
    close(requests)

    time.Sleep(500 * time.Millisecond)
}
```

## Synchronization Primitives

Traditional synchronization mechanisms from the sync package.

### Mutex and RWMutex

```go
import "sync"

// Mutex for exclusive access
type Counter struct {
    mu    sync.Mutex
    value int
}

func (c *Counter) Increment() {
    c.mu.Lock()
    defer c.mu.Unlock()
    c.value++
}

func (c *Counter) Value() int {
    c.mu.Lock()
    defer c.mu.Unlock()
    return c.value
}

func demonstrateMutex() {
    counter := &Counter{}

    // Start multiple goroutines
    var wg sync.WaitGroup
    for i := 0; i < 10; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            for j := 0; j < 100; j++ {
                counter.Increment()
            }
        }()
    }

    wg.Wait()
    fmt.Printf("Final counter value: %d\n", counter.Value())
}

// RWMutex for read-write access
type Cache struct {
    mu   sync.RWMutex
    data map[string]string
}

func NewCache() *Cache {
    return &Cache{
        data: make(map[string]string),
    }
}

func (c *Cache) Get(key string) (string, bool) {
    c.mu.RLock()
    defer c.mu.RUnlock()
    value, exists := c.data[key]
    return value, exists
}

func (c *Cache) Set(key, value string) {
    c.mu.Lock()
    defer c.mu.Unlock()
    c.data[key] = value
}

func demonstrateRWMutex() {
    cache := NewCache()

    var wg sync.WaitGroup

    // Writers
    for i := 0; i < 5; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            for j := 0; j < 10; j++ {
                key := fmt.Sprintf("key-%d-%d", id, j)
                value := fmt.Sprintf("value-%d-%d", id, j)
                cache.Set(key, value)
            }
        }(i)
    }

    // Readers
    for i := 0; i < 10; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            for j := 0; j < 20; j++ {
                key := fmt.Sprintf("key-%d-%d", id%5, j%10)
                if value, exists := cache.Get(key); exists {
                    fmt.Printf("Reader %d found: %s = %s\n", id, key, value)
                }
            }
        }(i)
    }

    wg.Wait()
}
```

### WaitGroup

```go
// WaitGroup for waiting on multiple goroutines
func demonstrateWaitGroup() {
    var wg sync.WaitGroup

    tasks := []string{"task1", "task2", "task3", "task4", "task5"}

    for _, task := range tasks {
        wg.Add(1) // Increment counter
        go func(taskName string) {
            defer wg.Done() // Decrement counter when done

            fmt.Printf("Starting %s\n", taskName)
            time.Sleep(time.Duration(rand.Intn(200)) * time.Millisecond)
            fmt.Printf("Completed %s\n", taskName)
        }(task)
    }

    wg.Wait() // Wait for all goroutines to complete
    fmt.Println("All tasks completed")
}

// WaitGroup with error handling
func processItems(items []string) error {
    var wg sync.WaitGroup
    var mu sync.Mutex
    var errors []error

    for _, item := range items {
        wg.Add(1)
        go func(item string) {
            defer wg.Done()

            if err := processItem(item); err != nil {
                mu.Lock()
                errors = append(errors, fmt.Errorf("failed to process %s: %w", item, err))
                mu.Unlock()
            }
        }(item)
    }

    wg.Wait()

    if len(errors) > 0 {
        return fmt.Errorf("processing failed: %v", errors)
    }

    return nil
}

func processItem(item string) error {
    // Simulate processing
    time.Sleep(100 * time.Millisecond)
    if rand.Float32() < 0.2 { // 20% chance of error
        return fmt.Errorf("processing error for %s", item)
    }
    return nil
}
```

### Once

```go
// sync.Once for one-time initialization
type Singleton struct {
    data string
}

var (
    instance *Singleton
    once     sync.Once
)

func GetSingleton() *Singleton {
    once.Do(func() {
        fmt.Println("Creating singleton instance")
        instance = &Singleton{
            data: "singleton data",
        }
    })
    return instance
}

func demonstrateOnce() {
    var wg sync.WaitGroup

    // Multiple goroutines trying to get singleton
    for i := 0; i < 10; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            singleton := GetSingleton()
            fmt.Printf("Goroutine %d got singleton: %s\n", id, singleton.data)
        }(i)
    }

    wg.Wait()
}

// Once for expensive initialization
var (
    config *Config
    configOnce sync.Once
)

type Config struct {
    DatabaseURL string
    APIKey      string
}

func loadConfig() *Config {
    configOnce.Do(func() {
        fmt.Println("Loading configuration...")
        time.Sleep(100 * time.Millisecond) // Simulate expensive operation
        config = &Config{
            DatabaseURL: "postgres://localhost/mydb",
            APIKey:      "secret-api-key",
        }
    })
    return config
}
```

### Atomic Operations

```go
import "sync/atomic"

// Atomic operations for simple values
type AtomicCounter struct {
    value int64
}

func (c *AtomicCounter) Increment() {
    atomic.AddInt64(&c.value, 1)
}

func (c *AtomicCounter) Decrement() {
    atomic.AddInt64(&c.value, -1)
}

func (c *AtomicCounter) Value() int64 {
    return atomic.LoadInt64(&c.value)
}

func (c *AtomicCounter) Reset() {
    atomic.StoreInt64(&c.value, 0)
}

func demonstrateAtomic() {
    counter := &AtomicCounter{}

    var wg sync.WaitGroup

    // Incrementers
    for i := 0; i < 10; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            for j := 0; j < 1000; j++ {
                counter.Increment()
            }
        }()
    }

    // Decrementers
    for i := 0; i < 5; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            for j := 0; j < 500; j++ {
                counter.Decrement()
            }
        }()
    }

    wg.Wait()
    fmt.Printf("Final atomic counter value: %d\n", counter.Value())
}

// Atomic compare-and-swap
func demonstrateCompareAndSwap() {
    var value int64 = 10

    var wg sync.WaitGroup

    // Multiple goroutines trying to update value
    for i := 0; i < 5; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()

            for {
                old := atomic.LoadInt64(&value)
                new := old + int64(id)

                if atomic.CompareAndSwapInt64(&value, old, new) {
                    fmt.Printf("Goroutine %d successfully updated value from %d to %d\n", id, old, new)
                    break
                }
                // If CAS failed, retry
            }
        }(i)
    }

    wg.Wait()
    fmt.Printf("Final value: %d\n", atomic.LoadInt64(&value))
}
```

## Context Package

Managing cancellation, timeouts, and request-scoped values.

### Basic Context Usage

```go
import "context"

// Context with cancellation
func demonstrateContextCancellation() {
    ctx, cancel := context.WithCancel(context.Background())
    defer cancel()

    go func() {
        time.Sleep(200 * time.Millisecond)
        fmt.Println("Cancelling context")
        cancel()
    }()

    select {
    case <-time.After(500 * time.Millisecond):
        fmt.Println("Operation completed")
    case <-ctx.Done():
        fmt.Printf("Operation cancelled: %v\n", ctx.Err())
    }
}

// Context with timeout
func demonstrateContextTimeout() {
    ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
    defer cancel()

    select {
    case <-time.After(200 * time.Millisecond):
        fmt.Println("Operation completed")
    case <-ctx.Done():
        fmt.Printf("Operation timed out: %v\n", ctx.Err())
    }
}

// Context with deadline
func demonstrateContextDeadline() {
    deadline := time.Now().Add(150 * time.Millisecond)
    ctx, cancel := context.WithDeadline(context.Background(), deadline)
    defer cancel()

    select {
    case <-time.After(200 * time.Millisecond):
        fmt.Println("Operation completed")
    case <-ctx.Done():
        fmt.Printf("Operation deadline exceeded: %v\n", ctx.Err())
    }
}
```

### Context with Values

```go
// Context with values
type contextKey string

const (
    userIDKey    contextKey = "userID"
    requestIDKey contextKey = "requestID"
)

func demonstrateContextValues() {
    ctx := context.Background()

    // Add values to context
    ctx = context.WithValue(ctx, userIDKey, 12345)
    ctx = context.WithValue(ctx, requestIDKey, "req-abc-123")

    processRequest(ctx)
}

func processRequest(ctx context.Context) {
    userID := ctx.Value(userIDKey)
    requestID := ctx.Value(requestIDKey)

    fmt.Printf("Processing request %v for user %v\n", requestID, userID)

    // Pass context to other functions
    authenticateUser(ctx)
    fetchUserData(ctx)
}

func authenticateUser(ctx context.Context) {
    userID := ctx.Value(userIDKey)
    fmt.Printf("Authenticating user %v\n", userID)
}

func fetchUserData(ctx context.Context) {
    userID := ctx.Value(userIDKey)
    requestID := ctx.Value(requestIDKey)
    fmt.Printf("Fetching data for user %v (request %v)\n", userID, requestID)
}
```

### Context in Concurrent Operations

```go
// Context-aware worker
func contextAwareWorker(ctx context.Context, id int, jobs <-chan string, results chan<- string) {
    for {
        select {
        case job, ok := <-jobs:
            if !ok {
                return // Jobs channel closed
            }

            // Process job with context
            result, err := processJobWithContext(ctx, job)
            if err != nil {
                fmt.Printf("Worker %d: job %s failed: %v\n", id, job, err)
                continue
            }

            select {
            case results <- result:
                fmt.Printf("Worker %d completed job: %s\n", id, job)
            case <-ctx.Done():
                fmt.Printf("Worker %d cancelled while sending result\n", id)
                return
            }

        case <-ctx.Done():
            fmt.Printf("Worker %d cancelled: %v\n", id, ctx.Err())
            return
        }
    }
}

func processJobWithContext(ctx context.Context, job string) (string, error) {
    // Simulate work that respects context cancellation
    select {
    case <-time.After(100 * time.Millisecond):
        return fmt.Sprintf("processed-%s", job), nil
    case <-ctx.Done():
        return "", ctx.Err()
    }
}

func demonstrateContextAwareWorkers() {
    ctx, cancel := context.WithTimeout(context.Background(), 500*time.Millisecond)
    defer cancel()

    jobs := make(chan string, 10)
    results := make(chan string, 10)

    // Start workers
    for i := 1; i <= 3; i++ {
        go contextAwareWorker(ctx, i, jobs, results)
    }

    // Send jobs
    go func() {
        defer close(jobs)
        for i := 1; i <= 10; i++ {
            jobs <- fmt.Sprintf("job-%d", i)
        }
    }()

    // Collect results until context is cancelled
    for {
        select {
        case result, ok := <-results:
            if !ok {
                return
            }
            fmt.Printf("Result: %s\n", result)
        case <-ctx.Done():
            fmt.Printf("Main: context cancelled: %v\n", ctx.Err())
            return
        }
    }
}
```

## Concurrency Patterns

Advanced patterns for building robust concurrent applications.

### Rate Limiting

```go
import "golang.org/x/time/rate"

// Token bucket rate limiter
func demonstrateRateLimiting() {
    limiter := rate.NewLimiter(rate.Limit(2), 5) // 2 requests per second, burst of 5

    var wg sync.WaitGroup

    for i := 1; i <= 10; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()

            if limiter.Allow() {
                fmt.Printf("Request %d: Allowed\n", id)
            } else {
                fmt.Printf("Request %d: Rate limited\n", id)
            }
        }(i)
    }

    wg.Wait()
}

// Channel-based rate limiter
func channelRateLimiter(requests <-chan string, limit int, interval time.Duration) <-chan string {
    output := make(chan string)
    ticker := time.NewTicker(interval)

    go func() {
        defer close(output)
        defer ticker.Stop()

        tokens := make(chan struct{}, limit)

        // Fill initial tokens
        for i := 0; i < limit; i++ {
            tokens <- struct{}{}
        }

        // Refill tokens
        go func() {
            for range ticker.C {
                select {
                case tokens <- struct{}{}:
                default: // Bucket is full
                }
            }
        }()

        for request := range requests {
            <-tokens // Wait for token
            output <- request
        }
    }()

    return output
}

func demonstrateChannelRateLimiter() {
    requests := make(chan string)
    limited := channelRateLimiter(requests, 3, 500*time.Millisecond)

    // Send requests
    go func() {
        defer close(requests)
        for i := 1; i <= 10; i++ {
            requests <- fmt.Sprintf("request-%d", i)
        }
    }()

    // Process rate-limited requests
    for request := range limited {
        fmt.Printf("Processing: %s at %s\n", request, time.Now().Format("15:04:05.000"))
    }
}
```

### Circuit Breaker

```go
// Circuit breaker implementation
type CircuitBreaker struct {
    mu           sync.RWMutex
    state        string
    failures     int
    maxFailures  int
    timeout      time.Duration
    lastFailTime time.Time
}

func NewCircuitBreaker(maxFailures int, timeout time.Duration) *CircuitBreaker {
    return &CircuitBreaker{
        state:       "closed",
        maxFailures: maxFailures,
        timeout:     timeout,
    }
}

func (cb *CircuitBreaker) Call(fn func() error) error {
    cb.mu.RLock()
    state := cb.state
    failures := cb.failures
    lastFailTime := cb.lastFailTime
    cb.mu.RUnlock()

    // Check if we should transition from open to half-open
    if state == "open" && time.Since(lastFailTime) > cb.timeout {
        cb.mu.Lock()
        cb.state = "half-open"
        cb.mu.Unlock()
        state = "half-open"
    }

    // Reject calls if circuit is open
    if state == "open" {
        return fmt.Errorf("circuit breaker is open")
    }

    // Execute function
    err := fn()

    cb.mu.Lock()
    defer cb.mu.Unlock()

    if err != nil {
        cb.failures++
        cb.lastFailTime = time.Now()

        if cb.failures >= cb.maxFailures {
            cb.state = "open"
        }
        return err
    }

    // Success - reset circuit breaker
    cb.failures = 0
    cb.state = "closed"
    return nil
}

func (cb *CircuitBreaker) State() string {
    cb.mu.RLock()
    defer cb.mu.RUnlock()
    return cb.state
}

func demonstrateCircuitBreaker() {
    cb := NewCircuitBreaker(3, 2*time.Second)

    // Simulate failing service
    failingService := func() error {
        if rand.Float32() < 0.7 { // 70% failure rate
            return fmt.Errorf("service error")
        }
        return nil
    }

    for i := 1; i <= 10; i++ {
        err := cb.Call(failingService)
        fmt.Printf("Call %d: State=%s, Error=%v\n", i, cb.State(), err)
        time.Sleep(300 * time.Millisecond)
    }
}
```

### Semaphore Pattern

```go
// Semaphore for limiting concurrent access
type Semaphore struct {
    ch chan struct{}
}

func NewSemaphore(capacity int) *Semaphore {
    return &Semaphore{
        ch: make(chan struct{}, capacity),
    }
}

func (s *Semaphore) Acquire() {
    s.ch <- struct{}{}
}

func (s *Semaphore) Release() {
    <-s.ch
}

func (s *Semaphore) TryAcquire() bool {
    select {
    case s.ch <- struct{}{}:
        return true
    default:
        return false
    }
}

func demonstrateSemaphore() {
    sem := NewSemaphore(3) // Allow max 3 concurrent operations

    var wg sync.WaitGroup

    for i := 1; i <= 10; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()

            fmt.Printf("Worker %d waiting for semaphore\n", id)
            sem.Acquire()
            defer sem.Release()

            fmt.Printf("Worker %d acquired semaphore\n", id)
            time.Sleep(time.Duration(rand.Intn(500)) * time.Millisecond)
            fmt.Printf("Worker %d releasing semaphore\n", id)
        }(i)
    }

    wg.Wait()
}
```

### Publish-Subscribe Pattern

```go
// Event bus for publish-subscribe pattern
type EventBus struct {
    mu          sync.RWMutex
    subscribers map[string][]chan interface{}
}

func NewEventBus() *EventBus {
    return &EventBus{
        subscribers: make(map[string][]chan interface{}),
    }
}

func (eb *EventBus) Subscribe(topic string, ch chan interface{}) {
    eb.mu.Lock()
    defer eb.mu.Unlock()
    eb.subscribers[topic] = append(eb.subscribers[topic], ch)
}

func (eb *EventBus) Unsubscribe(topic string, ch chan interface{}) {
    eb.mu.Lock()
    defer eb.mu.Unlock()

    subscribers := eb.subscribers[topic]
    for i, subscriber := range subscribers {
        if subscriber == ch {
            eb.subscribers[topic] = append(subscribers[:i], subscribers[i+1:]...)
            break
        }
    }
}

func (eb *EventBus) Publish(topic string, data interface{}) {
    eb.mu.RLock()
    defer eb.mu.RUnlock()

    for _, ch := range eb.subscribers[topic] {
        select {
        case ch <- data:
        default: // Non-blocking send
            fmt.Printf("Subscriber channel full for topic: %s\n", topic)
        }
    }
}

func demonstrateEventBus() {
    bus := NewEventBus()

    // Create subscribers
    userEvents := make(chan interface{}, 10)
    orderEvents := make(chan interface{}, 10)
    allEvents := make(chan interface{}, 10)

    bus.Subscribe("user", userEvents)
    bus.Subscribe("order", orderEvents)
    bus.Subscribe("user", allEvents)
    bus.Subscribe("order", allEvents)

    // Start event processors
    go func() {
        for event := range userEvents {
            fmt.Printf("User processor: %v\n", event)
        }
    }()

    go func() {
        for event := range orderEvents {
            fmt.Printf("Order processor: %v\n", event)
        }
    }()

    go func() {
        for event := range allEvents {
            fmt.Printf("All events processor: %v\n", event)
        }
    }()

    // Publish events
    bus.Publish("user", "User created: Alice")
    bus.Publish("order", "Order placed: #12345")
    bus.Publish("user", "User updated: Bob")
    bus.Publish("order", "Order shipped: #12345")

    time.Sleep(100 * time.Millisecond)
}
```

## Performance and Best Practices

Guidelines for writing efficient concurrent code.

### Goroutine Best Practices

```go
// Good: Bounded goroutines with worker pool
func goodConcurrentProcessing(items []string) {
    const numWorkers = 10
    jobs := make(chan string, len(items))
    results := make(chan string, len(items))

    // Start workers
    var wg sync.WaitGroup
    for i := 0; i < numWorkers; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            for job := range jobs {
                result := processItem(job)
                results <- result
            }
        }()
    }

    // Send jobs
    for _, item := range items {
        jobs <- item
    }
    close(jobs)

    // Wait for completion
    go func() {
        wg.Wait()
        close(results)
    }()

    // Collect results
    for result := range results {
        fmt.Println(result)
    }
}

// Bad: Unbounded goroutines
func badConcurrentProcessing(items []string) {
    var wg sync.WaitGroup

    for _, item := range items {
        wg.Add(1)
        go func(item string) { // Creates one goroutine per item
            defer wg.Done()
            result := processItem(item)
            fmt.Println(result)
        }(item)
    }

    wg.Wait()
}

func processItem(item string) string {
    time.Sleep(10 * time.Millisecond)
    return "processed: " + item
}
```

### Channel Best Practices

```go
// Good: Proper channel closing
func goodChannelUsage() {
    ch := make(chan int)

    // Producer closes channel
    go func() {
        defer close(ch)
        for i := 1; i <= 5; i++ {
            ch <- i
        }
    }()

    // Consumer uses range
    for value := range ch {
        fmt.Println(value)
    }
}

// Good: Buffered channels for known capacity
func goodBufferedChannel() {
    const batchSize = 100
    ch := make(chan string, batchSize)

    // Producer
    go func() {
        defer close(ch)
        for i := 1; i <= batchSize; i++ {
            ch <- fmt.Sprintf("item-%d", i)
        }
    }()

    // Consumer
    for item := range ch {
        fmt.Println(item)
    }
}

// Bad: Unbuffered channel causing blocking
func badUnbufferedChannel() {
    ch := make(chan string) // Unbuffered

    // This will block if consumer is slow
    for i := 1; i <= 100; i++ {
        ch <- fmt.Sprintf("item-%d", i) // Blocks on each send
    }
}
```

### Memory Management

```go
// Good: Reuse goroutines and channels
type WorkerPool struct {
    workers int
    jobs    chan func()
    quit    chan struct{}
}

func NewWorkerPool(workers int) *WorkerPool {
    wp := &WorkerPool{
        workers: workers,
        jobs:    make(chan func(), workers*2),
        quit:    make(chan struct{}),
    }

    for i := 0; i < workers; i++ {
        go wp.worker()
    }

    return wp
}

func (wp *WorkerPool) worker() {
    for {
        select {
        case job := <-wp.jobs:
            job()
        case <-wp.quit:
            return
        }
    }
}

func (wp *WorkerPool) Submit(job func()) {
    select {
    case wp.jobs <- job:
    case <-wp.quit:
        return
    }
}

func (wp *WorkerPool) Stop() {
    close(wp.quit)
}

// Good: Object pooling for frequent allocations
var bufferPool = sync.Pool{
    New: func() interface{} {
        return make([]byte, 1024)
    },
}

func processWithPool(data []byte) []byte {
    buf := bufferPool.Get().([]byte)
    defer bufferPool.Put(buf)

    // Use buf for processing
    copy(buf, data)
    // ... process buf

    // Return copy since we're returning buf to pool
    result := make([]byte, len(data))
    copy(result, buf[:len(data)])
    return result
}
```

## Common Pitfalls

Avoiding common mistakes in concurrent programming.

### Race Conditions

```go
// Bad: Race condition
var counter int

func badIncrement() {
    counter++ // Not atomic
}

// Good: Use mutex or atomic
var (
    safeCounter int
    counterMu   sync.Mutex
)

func goodIncrement() {
    counterMu.Lock()
    defer counterMu.Unlock()
    safeCounter++
}

// Better: Use atomic operations
var atomicCounter int64

func atomicIncrement() {
    atomic.AddInt64(&atomicCounter, 1)
}
```

### Deadlocks

```go
// Bad: Potential deadlock
func badDeadlock() {
    var mu1, mu2 sync.Mutex

    go func() {
        mu1.Lock()
        time.Sleep(100 * time.Millisecond)
        mu2.Lock() // Potential deadlock
        mu2.Unlock()
        mu1.Unlock()
    }()

    go func() {
        mu2.Lock()
        time.Sleep(100 * time.Millisecond)
        mu1.Lock() // Potential deadlock
        mu1.Unlock()
        mu2.Unlock()
    }()
}

// Good: Consistent lock ordering
func goodLockOrdering() {
    var mu1, mu2 sync.Mutex

    lockInOrder := func() {
        mu1.Lock()
        defer mu1.Unlock()
        mu2.Lock()
        defer mu2.Unlock()
        // Do work
    }

    go lockInOrder()
    go lockInOrder()
}
```

### Goroutine Leaks

```go
// Bad: Goroutine leak
func badGoroutineLeak() {
    ch := make(chan string)

    go func() {
        for {
            select {
            case msg := <-ch:
                fmt.Println(msg)
            // No way to exit this goroutine
            }
        }
    }()

    // Goroutine will run forever
}

// Good: Proper cleanup
func goodGoroutineCleanup() {
    ch := make(chan string)
    quit := make(chan struct{})

    go func() {
        for {
            select {
            case msg := <-ch:
                fmt.Println(msg)
            case <-quit:
                return // Proper exit
            }
        }
    }()

    // Later...
    close(quit) // Signal goroutine to exit
}

// Better: Use context
func betterWithContext() {
    ctx, cancel := context.WithCancel(context.Background())
    defer cancel()

    ch := make(chan string)

    go func() {
        for {
            select {
            case msg := <-ch:
                fmt.Println(msg)
            case <-ctx.Done():
                return
            }
        }
    }()

    // Context will be cancelled when function returns
}
```

## Testing Concurrent Code

Strategies for testing concurrent applications.

### Testing with Race Detector

```go
// Run tests with: go test -race

func TestConcurrentCounter(t *testing.T) {
    counter := &Counter{}

    var wg sync.WaitGroup
    const numGoroutines = 100
    const incrementsPerGoroutine = 100

    for i := 0; i < numGoroutines; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            for j := 0; j < incrementsPerGoroutine; j++ {
                counter.Increment()
            }
        }()
    }

    wg.Wait()

    expected := numGoroutines * incrementsPerGoroutine
    if counter.Value() != expected {
        t.Errorf("Expected %d, got %d", expected, counter.Value())
    }
}
```

### Testing Channels

```go
func TestChannelCommunication(t *testing.T) {
    ch := make(chan int, 1)

    // Test send
    ch <- 42

    // Test receive
    select {
    case value := <-ch:
        if value != 42 {
            t.Errorf("Expected 42, got %d", value)
        }
    case <-time.After(100 * time.Millisecond):
        t.Error("Channel receive timed out")
    }
}

func TestChannelClosing(t *testing.T) {
    ch := make(chan int)

    go func() {
        ch <- 1
        ch <- 2
        close(ch)
    }()

    var values []int
    for value := range ch {
        values = append(values, value)
    }

    expected := []int{1, 2}
    if !reflect.DeepEqual(values, expected) {
        t.Errorf("Expected %v, got %v", expected, values)
    }
}
```

### Testing with Timeouts

```go
func TestWithTimeout(t *testing.T) {
    done := make(chan bool)

    go func() {
        // Simulate work
        time.Sleep(50 * time.Millisecond)
        done <- true
    }()

    select {
    case <-done:
        // Test passed
    case <-time.After(100 * time.Millisecond):
        t.Error("Operation timed out")
    }
}

func TestContextCancellation(t *testing.T) {
    ctx, cancel := context.WithTimeout(context.Background(), 50*time.Millisecond)
    defer cancel()

    select {
    case <-time.After(100 * time.Millisecond):
        t.Error("Context should have been cancelled")
    case <-ctx.Done():
        if ctx.Err() != context.DeadlineExceeded {
            t.Errorf("Expected DeadlineExceeded, got %v", ctx.Err())
        }
    }
}
```

This comprehensive guide covers all aspects of concurrency in Go, from basic goroutines and channels to advanced patterns and best practices. Understanding these concepts will help you build efficient, scalable concurrent applications.