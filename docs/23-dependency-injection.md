# Dependency Injection in Go

This comprehensive guide covers dependency injection patterns in Go, from basic manual injection to advanced DI containers, with real-world examples and best practices for building maintainable, testable applications.

## Table of Contents

1. [Overview](#overview)
2. [Manual Dependency Injection](#manual-dependency-injection)
3. [Constructor Injection](#constructor-injection)
4. [Interface-Based Injection](#interface-based-injection)
5. [DI Container Patterns](#di-container-patterns)
6. [Popular DI Libraries](#popular-di-libraries)
7. [Service Locator Pattern](#service-locator-pattern)
8. [Lifecycle Management](#lifecycle-management)
9. [Configuration and Environment](#configuration-and-environment)
10. [Testing with DI](#testing-with-di)
11. [Real-World Examples](#real-world-examples)
12. [Best Practices](#best-practices)
13. [Common Pitfalls](#common-pitfalls)
14. [Performance Considerations](#performance-considerations)

## Overview

Dependency Injection (DI) is a design pattern that implements Inversion of Control (IoC) for resolving dependencies. Instead of objects creating their dependencies, they receive them from external sources.

### Why Dependency Injection?

```go
// Without DI - Tight coupling
type UserService struct {
    db *sql.DB // Hard dependency
}

func NewUserService() *UserService {
    db, _ := sql.Open("postgres", "connection-string") // Hard-coded
    return &UserService{db: db}
}

// With DI - Loose coupling
type UserService struct {
    repo UserRepository // Interface dependency
}

func NewUserService(repo UserRepository) *UserService {
    return &UserService{repo: repo}
}
```

### Benefits of DI

| Benefit | Description | Example |
|---------|-------------|---------|
| **Testability** | Easy to mock dependencies | Inject mock repository for tests |
| **Flexibility** | Swap implementations easily | Different DB for dev/prod |
| **Maintainability** | Loose coupling between components | Change DB without changing service |
| **Configuration** | External configuration of dependencies | Environment-based setup |

## Manual Dependency Injection

The simplest form of DI - manually passing dependencies to constructors.

### Basic Manual Injection

```go
package main

import (
    "database/sql"
    "fmt"
    "log"

    _ "github.com/lib/pq"
)

// Domain models
type User struct {
    ID    int
    Name  string
    Email string
}

// Repository interface
type UserRepository interface {
    Create(user *User) error
    GetByID(id int) (*User, error)
    GetByEmail(email string) (*User, error)
    Update(user *User) error
    Delete(id int) error
}

// Service interface
type UserService interface {
    RegisterUser(name, email string) (*User, error)
    GetUser(id int) (*User, error)
    UpdateUser(user *User) error
}

// Email service interface
type EmailService interface {
    SendWelcomeEmail(email, name string) error
    SendPasswordReset(email string) error
}

// Concrete implementations
type PostgresUserRepository struct {
    db *sql.DB
}

func NewPostgresUserRepository(db *sql.DB) *PostgresUserRepository {
    return &PostgresUserRepository{db: db}
}

func (r *PostgresUserRepository) Create(user *User) error {
    query := `INSERT INTO users (name, email) VALUES ($1, $2) RETURNING id`
    err := r.db.QueryRow(query, user.Name, user.Email).Scan(&user.ID)
    return err
}

func (r *PostgresUserRepository) GetByID(id int) (*User, error) {
    user := &User{}
    query := `SELECT id, name, email FROM users WHERE id = $1`
    err := r.db.QueryRow(query, id).Scan(&user.ID, &user.Name, &user.Email)
    if err != nil {
        return nil, err
    }
    return user, nil
}

func (r *PostgresUserRepository) GetByEmail(email string) (*User, error) {
    user := &User{}
    query := `SELECT id, name, email FROM users WHERE email = $1`
    err := r.db.QueryRow(query, email).Scan(&user.ID, &user.Name, &user.Email)
    if err != nil {
        return nil, err
    }
    return user, nil
}

func (r *PostgresUserRepository) Update(user *User) error {
    query := `UPDATE users SET name = $1, email = $2 WHERE id = $3`
    _, err := r.db.Exec(query, user.Name, user.Email, user.ID)
    return err
}

func (r *PostgresUserRepository) Delete(id int) error {
    query := `DELETE FROM users WHERE id = $1`
    _, err := r.db.Exec(query, id)
    return err
}

// User service implementation
type userService struct {
    userRepo     UserRepository
    emailService EmailService
}

func NewUserService(userRepo UserRepository, emailService EmailService) UserService {
    return &userService{
        userRepo:     userRepo,
        emailService: emailService,
    }
}

func (s *userService) RegisterUser(name, email string) (*User, error) {
    // Check if user already exists
    existingUser, err := s.userRepo.GetByEmail(email)
    if err == nil && existingUser != nil {
        return nil, fmt.Errorf("user with email %s already exists", email)
    }

    // Create new user
    user := &User{
        Name:  name,
        Email: email,
    }

    if err := s.userRepo.Create(user); err != nil {
        return nil, fmt.Errorf("failed to create user: %w", err)
    }

    // Send welcome email
    if err := s.emailService.SendWelcomeEmail(email, name); err != nil {
        log.Printf("Failed to send welcome email: %v", err)
        // Don't fail the registration for email issues
    }

    return user, nil
}

func (s *userService) GetUser(id int) (*User, error) {
    return s.userRepo.GetByID(id)
}

func (s *userService) UpdateUser(user *User) error {
    return s.userRepo.Update(user)
}

// Email service implementation
type SMTPEmailService struct {
    host     string
    port     int
    username string
    password string
}

func NewSMTPEmailService(host string, port int, username, password string) *SMTPEmailService {
    return &SMTPEmailService{
        host:     host,
        port:     port,
        username: username,
        password: password,
    }
}

func (e *SMTPEmailService) SendWelcomeEmail(email, name string) error {
    // Implementation would use SMTP to send email
    fmt.Printf("Sending welcome email to %s (%s)\n", name, email)
    return nil
}

func (e *SMTPEmailService) SendPasswordReset(email string) error {
    // Implementation would use SMTP to send email
    fmt.Printf("Sending password reset email to %s\n", email)
    return nil
}

// Manual wiring in main function
func main() {
    // Database connection
    db, err := sql.Open("postgres", "postgres://user:password@localhost/mydb?sslmode=disable")
    if err != nil {
        log.Fatal("Failed to connect to database:", err)
    }
    defer db.Close()

    // Create dependencies
    userRepo := NewPostgresUserRepository(db)
    emailService := NewSMTPEmailService("smtp.gmail.com", 587, "<EMAIL>", "password")

    // Inject dependencies into service
    userService := NewUserService(userRepo, emailService)

    // Use the service
    user, err := userService.RegisterUser("John Doe", "<EMAIL>")
    if err != nil {
        log.Printf("Registration failed: %v", err)
        return
    }

    fmt.Printf("User registered: %+v\n", user)

    // Get user
    retrievedUser, err := userService.GetUser(user.ID)
    if err != nil {
        log.Printf("Failed to get user: %v", err)
        return
    }

    fmt.Printf("Retrieved user: %+v\n", retrievedUser)
}
```

### Configuration-Based Injection

```go
// Configuration structure
type Config struct {
    Database DatabaseConfig `json:"database"`
    Email    EmailConfig    `json:"email"`
    Server   ServerConfig   `json:"server"`
}

type DatabaseConfig struct {
    Host     string `json:"host"`
    Port     int    `json:"port"`
    Username string `json:"username"`
    Password string `json:"password"`
    Database string `json:"database"`
    SSLMode  string `json:"ssl_mode"`
}

type EmailConfig struct {
    Host     string `json:"host"`
    Port     int    `json:"port"`
    Username string `json:"username"`
    Password string `json:"password"`
}

type ServerConfig struct {
    Host string `json:"host"`
    Port int    `json:"port"`
}

// Configuration loader
func LoadConfig(path string) (*Config, error) {
    file, err := os.Open(path)
    if err != nil {
        return nil, err
    }
    defer file.Close()

    var config Config
    decoder := json.NewDecoder(file)
    if err := decoder.Decode(&config); err != nil {
        return nil, err
    }

    return &config, nil
}

// Dependency factory
type Dependencies struct {
    DB           *sql.DB
    UserRepo     UserRepository
    EmailService EmailService
    UserService  UserService
}

func NewDependencies(config *Config) (*Dependencies, error) {
    // Create database connection
    dsn := fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=%s",
        config.Database.Username,
        config.Database.Password,
        config.Database.Host,
        config.Database.Port,
        config.Database.Database,
        config.Database.SSLMode,
    )

    db, err := sql.Open("postgres", dsn)
    if err != nil {
        return nil, fmt.Errorf("failed to connect to database: %w", err)
    }

    // Test connection
    if err := db.Ping(); err != nil {
        return nil, fmt.Errorf("failed to ping database: %w", err)
    }

    // Create repository
    userRepo := NewPostgresUserRepository(db)

    // Create email service
    emailService := NewSMTPEmailService(
        config.Email.Host,
        config.Email.Port,
        config.Email.Username,
        config.Email.Password,
    )

    // Create user service
    userService := NewUserService(userRepo, emailService)

    return &Dependencies{
        DB:           db,
        UserRepo:     userRepo,
        EmailService: emailService,
        UserService:  userService,
    }, nil
}

func (d *Dependencies) Close() error {
    if d.DB != nil {
        return d.DB.Close()
    }
    return nil
}

// Usage with configuration
func mainWithConfig() {
    config, err := LoadConfig("config.json")
    if err != nil {
        log.Fatal("Failed to load config:", err)
    }

    deps, err := NewDependencies(config)
    if err != nil {
        log.Fatal("Failed to create dependencies:", err)
    }
    defer deps.Close()

    // Use dependencies
    user, err := deps.UserService.RegisterUser("Jane Doe", "<EMAIL>")
    if err != nil {
        log.Printf("Registration failed: %v", err)
        return
    }

    fmt.Printf("User registered: %+v\n", user)
}
```

## Constructor Injection

Using constructors to inject dependencies - the most common pattern in Go.

### Functional Options Pattern

```go
// Service with optional dependencies
type OrderService struct {
    orderRepo    OrderRepository
    paymentSvc   PaymentService
    emailSvc     EmailService
    logger       Logger
    eventBus     EventBus
    config       *OrderConfig
}

type OrderConfig struct {
    MaxRetries      int
    TimeoutDuration time.Duration
    EnableNotifications bool
}

// Option function type
type OrderServiceOption func(*OrderService)

// Constructor with options
func NewOrderService(orderRepo OrderRepository, opts ...OrderServiceOption) *OrderService {
    service := &OrderService{
        orderRepo: orderRepo,
        config: &OrderConfig{
            MaxRetries:      3,
            TimeoutDuration: 30 * time.Second,
            EnableNotifications: true,
        },
    }

    // Apply options
    for _, opt := range opts {
        opt(service)
    }

    return service
}

// Option functions
func WithPaymentService(paymentSvc PaymentService) OrderServiceOption {
    return func(s *OrderService) {
        s.paymentSvc = paymentSvc
    }
}

func WithEmailService(emailSvc EmailService) OrderServiceOption {
    return func(s *OrderService) {
        s.emailSvc = emailSvc
    }
}

func WithLogger(logger Logger) OrderServiceOption {
    return func(s *OrderService) {
        s.logger = logger
    }
}

func WithEventBus(eventBus EventBus) OrderServiceOption {
    return func(s *OrderService) {
        s.eventBus = eventBus
    }
}

func WithConfig(config *OrderConfig) OrderServiceOption {
    return func(s *OrderService) {
        s.config = config
    }
}

// Usage
func demonstrateOptionsPattern() {
    orderRepo := &PostgresOrderRepository{}
    paymentSvc := &StripePaymentService{}
    emailSvc := &SMTPEmailService{}
    logger := &ZapLogger{}

    // Create service with selected dependencies
    orderService := NewOrderService(
        orderRepo,
        WithPaymentService(paymentSvc),
        WithEmailService(emailSvc),
        WithLogger(logger),
        WithConfig(&OrderConfig{
            MaxRetries:      5,
            TimeoutDuration: 60 * time.Second,
            EnableNotifications: false,
        }),
    )

    // Use service
    _ = orderService
}
```

### Builder Pattern for Complex Dependencies

```go
// Service builder
type UserServiceBuilder struct {
    userRepo     UserRepository
    emailSvc     EmailService
    cacheSvc     CacheService
    logger       Logger
    validator    Validator
    eventBus     EventBus
    config       *UserServiceConfig
}

type UserServiceConfig struct {
    CacheEnabled     bool
    ValidationEnabled bool
    EventsEnabled    bool
    MaxLoginAttempts int
}

func NewUserServiceBuilder() *UserServiceBuilder {
    return &UserServiceBuilder{
        config: &UserServiceConfig{
            CacheEnabled:     true,
            ValidationEnabled: true,
            EventsEnabled:    true,
            MaxLoginAttempts: 3,
        },
    }
}

func (b *UserServiceBuilder) WithUserRepository(repo UserRepository) *UserServiceBuilder {
    b.userRepo = repo
    return b
}

func (b *UserServiceBuilder) WithEmailService(svc EmailService) *UserServiceBuilder {
    b.emailSvc = svc
    return b
}

func (b *UserServiceBuilder) WithCacheService(svc CacheService) *UserServiceBuilder {
    b.cacheSvc = svc
    return b
}

func (b *UserServiceBuilder) WithLogger(logger Logger) *UserServiceBuilder {
    b.logger = logger
    return b
}

func (b *UserServiceBuilder) WithValidator(validator Validator) *UserServiceBuilder {
    b.validator = validator
    return b
}

func (b *UserServiceBuilder) WithEventBus(eventBus EventBus) *UserServiceBuilder {
    b.eventBus = eventBus
    return b
}

func (b *UserServiceBuilder) WithConfig(config *UserServiceConfig) *UserServiceBuilder {
    b.config = config
    return b
}

func (b *UserServiceBuilder) Build() (*EnhancedUserService, error) {
    // Validate required dependencies
    if b.userRepo == nil {
        return nil, fmt.Errorf("user repository is required")
    }

    // Create service with all dependencies
    service := &EnhancedUserService{
        userRepo:  b.userRepo,
        emailSvc:  b.emailSvc,
        cacheSvc:  b.cacheSvc,
        logger:    b.logger,
        validator: b.validator,
        eventBus:  b.eventBus,
        config:    b.config,
    }

    return service, nil
}

// Enhanced user service
type EnhancedUserService struct {
    userRepo  UserRepository
    emailSvc  EmailService
    cacheSvc  CacheService
    logger    Logger
    validator Validator
    eventBus  EventBus
    config    *UserServiceConfig
}

func (s *EnhancedUserService) CreateUser(user *User) error {
    // Validation
    if s.config.ValidationEnabled && s.validator != nil {
        if err := s.validator.ValidateUser(user); err != nil {
            return fmt.Errorf("validation failed: %w", err)
        }
    }

    // Create user
    if err := s.userRepo.Create(user); err != nil {
        if s.logger != nil {
            s.logger.Error("Failed to create user", "error", err, "user", user.Email)
        }
        return err
    }

    // Cache user
    if s.config.CacheEnabled && s.cacheSvc != nil {
        cacheKey := fmt.Sprintf("user:%d", user.ID)
        s.cacheSvc.Set(cacheKey, user, 1*time.Hour)
    }

    // Send welcome email
    if s.emailSvc != nil {
        if err := s.emailSvc.SendWelcomeEmail(user.Email, user.Name); err != nil {
            if s.logger != nil {
                s.logger.Warn("Failed to send welcome email", "error", err, "user", user.Email)
            }
        }
    }

    // Publish event
    if s.config.EventsEnabled && s.eventBus != nil {
        event := &UserCreatedEvent{
            UserID: user.ID,
            Email:  user.Email,
            Name:   user.Name,
            Time:   time.Now(),
        }
        s.eventBus.Publish("user.created", event)
    }

    if s.logger != nil {
        s.logger.Info("User created successfully", "user_id", user.ID, "email", user.Email)
    }

    return nil
}

// Usage
func demonstrateBuilderPattern() {
    userRepo := &PostgresUserRepository{}
    emailSvc := &SMTPEmailService{}
    cacheSvc := &RedisCache{}
    logger := &ZapLogger{}
    validator := &UserValidator{}
    eventBus := &InMemoryEventBus{}

    userService, err := NewUserServiceBuilder().
        WithUserRepository(userRepo).
        WithEmailService(emailSvc).
        WithCacheService(cacheSvc).
        WithLogger(logger).
        WithValidator(validator).
        WithEventBus(eventBus).
        WithConfig(&UserServiceConfig{
            CacheEnabled:     true,
            ValidationEnabled: true,
            EventsEnabled:    true,
            MaxLoginAttempts: 5,
        }).
        Build()

    if err != nil {
        log.Fatal("Failed to build user service:", err)
    }

    // Use service
    user := &User{Name: "John Doe", Email: "<EMAIL>"}
    if err := userService.CreateUser(user); err != nil {
        log.Printf("Failed to create user: %v", err)
    }
}
```

## Interface-Based Injection

Using interfaces to define contracts and enable flexible implementations.

### Repository Pattern with Interfaces

```go
// Domain interfaces
type UserRepository interface {
    Create(ctx context.Context, user *User) error
    GetByID(ctx context.Context, id int) (*User, error)
    GetByEmail(ctx context.Context, email string) (*User, error)
    Update(ctx context.Context, user *User) error
    Delete(ctx context.Context, id int) error
    List(ctx context.Context, limit, offset int) ([]*User, error)
}

type CacheService interface {
    Get(ctx context.Context, key string, dest interface{}) error
    Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error
    Delete(ctx context.Context, key string) error
    Exists(ctx context.Context, key string) (bool, error)
}

type Logger interface {
    Debug(msg string, fields ...interface{})
    Info(msg string, fields ...interface{})
    Warn(msg string, fields ...interface{})
    Error(msg string, fields ...interface{})
}

// Multiple implementations
type PostgresUserRepository struct {
    db *sql.DB
}

type MongoUserRepository struct {
    collection *mongo.Collection
}

type InMemoryUserRepository struct {
    users map[int]*User
    mu    sync.RWMutex
    nextID int
}

// Cache implementations
type RedisCache struct {
    client *redis.Client
}

type InMemoryCache struct {
    data map[string]cacheItem
    mu   sync.RWMutex
}

type cacheItem struct {
    value  interface{}
    expiry time.Time
}

// Logger implementations
type ZapLogger struct {
    logger *zap.Logger
}

type StandardLogger struct {
    logger *log.Logger
}

// Service using interfaces
type UserServiceV2 struct {
    repo   UserRepository
    cache  CacheService
    logger Logger
}

func NewUserServiceV2(repo UserRepository, cache CacheService, logger Logger) *UserServiceV2 {
    return &UserServiceV2{
        repo:   repo,
        cache:  cache,
        logger: logger,
    }
}

func (s *UserServiceV2) GetUser(ctx context.Context, id int) (*User, error) {
    // Try cache first
    cacheKey := fmt.Sprintf("user:%d", id)
    var user User

    if err := s.cache.Get(ctx, cacheKey, &user); err == nil {
        s.logger.Debug("User found in cache", "user_id", id)
        return &user, nil
    }

    // Get from repository
    userPtr, err := s.repo.GetByID(ctx, id)
    if err != nil {
        s.logger.Error("Failed to get user from repository", "user_id", id, "error", err)
        return nil, err
    }

    // Cache the result
    if err := s.cache.Set(ctx, cacheKey, userPtr, 1*time.Hour); err != nil {
        s.logger.Warn("Failed to cache user", "user_id", id, "error", err)
    }

    s.logger.Info("User retrieved from repository", "user_id", id)
    return userPtr, nil
}

func (s *UserServiceV2) CreateUser(ctx context.Context, user *User) error {
    if err := s.repo.Create(ctx, user); err != nil {
        s.logger.Error("Failed to create user", "email", user.Email, "error", err)
        return err
    }

    // Cache the new user
    cacheKey := fmt.Sprintf("user:%d", user.ID)
    if err := s.cache.Set(ctx, cacheKey, user, 1*time.Hour); err != nil {
        s.logger.Warn("Failed to cache new user", "user_id", user.ID, "error", err)
    }

    s.logger.Info("User created successfully", "user_id", user.ID, "email", user.Email)
    return nil
}

// Implementation examples
func (r *InMemoryUserRepository) Create(ctx context.Context, user *User) error {
    r.mu.Lock()
    defer r.mu.Unlock()

    if r.users == nil {
        r.users = make(map[int]*User)
        r.nextID = 1
    }

    user.ID = r.nextID
    r.nextID++
    r.users[user.ID] = user

    return nil
}

func (r *InMemoryUserRepository) GetByID(ctx context.Context, id int) (*User, error) {
    r.mu.RLock()
    defer r.mu.RUnlock()

    user, exists := r.users[id]
    if !exists {
        return nil, fmt.Errorf("user with id %d not found", id)
    }

    // Return a copy to avoid external modifications
    userCopy := *user
    return &userCopy, nil
}

func (c *InMemoryCache) Get(ctx context.Context, key string, dest interface{}) error {
    c.mu.RLock()
    defer c.mu.RUnlock()

    item, exists := c.data[key]
    if !exists {
        return fmt.Errorf("key %s not found", key)
    }

    if time.Now().After(item.expiry) {
        delete(c.data, key)
        return fmt.Errorf("key %s expired", key)
    }

    // Use reflection to copy value to dest
    destValue := reflect.ValueOf(dest)
    if destValue.Kind() != reflect.Ptr {
        return fmt.Errorf("dest must be a pointer")
    }

    srcValue := reflect.ValueOf(item.value)
    destValue.Elem().Set(srcValue)

    return nil
}

func (c *InMemoryCache) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
    c.mu.Lock()
    defer c.mu.Unlock()

    if c.data == nil {
        c.data = make(map[string]cacheItem)
    }

    c.data[key] = cacheItem{
        value:  value,
        expiry: time.Now().Add(ttl),
    }

    return nil
}

func (l *StandardLogger) Info(msg string, fields ...interface{}) {
    l.logger.Printf("[INFO] %s %v", msg, fields)
}

func (l *StandardLogger) Error(msg string, fields ...interface{}) {
    l.logger.Printf("[ERROR] %s %v", msg, fields)
}

func (l *StandardLogger) Debug(msg string, fields ...interface{}) {
    l.logger.Printf("[DEBUG] %s %v", msg, fields)
}

func (l *StandardLogger) Warn(msg string, fields ...interface{}) {
    l.logger.Printf("[WARN] %s %v", msg, fields)
}

// Usage with different implementations
func demonstrateInterfaceInjection() {
    // Development setup
    devRepo := &InMemoryUserRepository{}
    devCache := &InMemoryCache{}
    devLogger := &StandardLogger{logger: log.New(os.Stdout, "", log.LstdFlags)}

    devService := NewUserServiceV2(devRepo, devCache, devLogger)

    // Production setup (would use real implementations)
    // prodRepo := &PostgresUserRepository{db: prodDB}
    // prodCache := &RedisCache{client: redisClient}
    // prodLogger := &ZapLogger{logger: zapLogger}
    // prodService := NewUserServiceV2(prodRepo, prodCache, prodLogger)

    // Same interface, different implementations
    ctx := context.Background()
    user := &User{Name: "Alice", Email: "<EMAIL>"}

    if err := devService.CreateUser(ctx, user); err != nil {
        log.Printf("Failed to create user: %v", err)
        return
    }

    retrievedUser, err := devService.GetUser(ctx, user.ID)
    if err != nil {
        log.Printf("Failed to get user: %v", err)
        return
    }

    fmt.Printf("Retrieved user: %+v\n", retrievedUser)
}
```

## DI Container Patterns

Building custom dependency injection containers for complex applications.

### Simple DI Container

```go
// Simple DI container implementation
type Container struct {
    services map[string]interface{}
    factories map[string]func() interface{}
    singletons map[string]interface{}
    mu sync.RWMutex
}

func NewContainer() *Container {
    return &Container{
        services:   make(map[string]interface{}),
        factories:  make(map[string]func() interface{}),
        singletons: make(map[string]interface{}),
    }
}

// Register a singleton instance
func (c *Container) RegisterSingleton(name string, instance interface{}) {
    c.mu.Lock()
    defer c.mu.Unlock()
    c.singletons[name] = instance
}

// Register a factory function
func (c *Container) RegisterFactory(name string, factory func() interface{}) {
    c.mu.Lock()
    defer c.mu.Unlock()
    c.factories[name] = factory
}

// Register a transient service
func (c *Container) RegisterTransient(name string, instance interface{}) {
    c.mu.Lock()
    defer c.mu.Unlock()
    c.services[name] = instance
}

// Resolve a service by name
func (c *Container) Resolve(name string) (interface{}, error) {
    c.mu.RLock()
    defer c.mu.RUnlock()

    // Check singletons first
    if instance, exists := c.singletons[name]; exists {
        return instance, nil
    }

    // Check factories
    if factory, exists := c.factories[name]; exists {
        return factory(), nil
    }

    // Check transient services
    if instance, exists := c.services[name]; exists {
        return instance, nil
    }

    return nil, fmt.Errorf("service %s not found", name)
}

// Type-safe resolve methods
func (c *Container) ResolveUserRepository() (UserRepository, error) {
    service, err := c.Resolve("UserRepository")
    if err != nil {
        return nil, err
    }

    repo, ok := service.(UserRepository)
    if !ok {
        return nil, fmt.Errorf("service UserRepository is not of correct type")
    }

    return repo, nil
}

func (c *Container) ResolveLogger() (Logger, error) {
    service, err := c.Resolve("Logger")
    if err != nil {
        return nil, err
    }

    logger, ok := service.(Logger)
    if !ok {
        return nil, fmt.Errorf("service Logger is not of correct type")
    }

    return logger, nil
}

// Usage example
func demonstrateSimpleContainer() {
    container := NewContainer()

    // Register services
    container.RegisterSingleton("Logger", &StandardLogger{
        logger: log.New(os.Stdout, "", log.LstdFlags),
    })

    container.RegisterFactory("UserRepository", func() interface{} {
        return &InMemoryUserRepository{}
    })

    container.RegisterSingleton("CacheService", &InMemoryCache{})

    // Resolve and use services
    logger, err := container.ResolveLogger()
    if err != nil {
        log.Fatal("Failed to resolve logger:", err)
    }

    userRepo, err := container.ResolveUserRepository()
    if err != nil {
        log.Fatal("Failed to resolve user repository:", err)
    }

    cacheService, err := container.Resolve("CacheService")
    if err != nil {
        log.Fatal("Failed to resolve cache service:", err)
    }

    cache := cacheService.(CacheService)

    // Create service with resolved dependencies
    userService := NewUserServiceV2(userRepo, cache, logger)

    // Use the service
    ctx := context.Background()
    user := &User{Name: "Container User", Email: "<EMAIL>"}

    if err := userService.CreateUser(ctx, user); err != nil {
        log.Printf("Failed to create user: %v", err)
    } else {
        logger.Info("User created via container", "user_id", user.ID)
    }
}
```

### Advanced DI Container with Auto-wiring

```go
// Advanced container with reflection-based auto-wiring
type AdvancedContainer struct {
    services map[reflect.Type]interface{}
    factories map[reflect.Type]func() interface{}
    singletons map[reflect.Type]interface{}
    mu sync.RWMutex
}

func NewAdvancedContainer() *AdvancedContainer {
    return &AdvancedContainer{
        services:   make(map[reflect.Type]interface{}),
        factories:  make(map[reflect.Type]func() interface{}),
        singletons: make(map[reflect.Type]interface{}),
    }
}

// Register by type
func (c *AdvancedContainer) RegisterSingleton(instance interface{}) {
    c.mu.Lock()
    defer c.mu.Unlock()

    t := reflect.TypeOf(instance)
    c.singletons[t] = instance

    // Also register by interface types
    c.registerInterfaces(instance)
}

func (c *AdvancedContainer) RegisterFactory(factory interface{}) error {
    c.mu.Lock()
    defer c.mu.Unlock()

    factoryType := reflect.TypeOf(factory)
    if factoryType.Kind() != reflect.Func {
        return fmt.Errorf("factory must be a function")
    }

    if factoryType.NumOut() != 1 {
        return fmt.Errorf("factory must return exactly one value")
    }

    returnType := factoryType.Out(0)
    factoryValue := reflect.ValueOf(factory)

    c.factories[returnType] = func() interface{} {
        results := factoryValue.Call(nil)
        return results[0].Interface()
    }

    return nil
}

func (c *AdvancedContainer) registerInterfaces(instance interface{}) {
    instanceType := reflect.TypeOf(instance)
    instanceValue := reflect.ValueOf(instance)

    // Register all interfaces this type implements
    for i := 0; i < instanceType.NumMethod(); i++ {
        method := instanceType.Method(i)
        _ = method // Use method to find interfaces
    }

    // Simple interface registration for common patterns
    if userRepo, ok := instance.(UserRepository); ok {
        userRepoType := reflect.TypeOf((*UserRepository)(nil)).Elem()
        c.singletons[userRepoType] = userRepo
    }

    if logger, ok := instance.(Logger); ok {
        loggerType := reflect.TypeOf((*Logger)(nil)).Elem()
        c.singletons[loggerType] = logger
    }

    if cache, ok := instance.(CacheService); ok {
        cacheType := reflect.TypeOf((*CacheService)(nil)).Elem()
        c.singletons[cacheType] = cache
    }

    _ = instanceValue // Avoid unused variable
}

// Resolve by type
func (c *AdvancedContainer) Resolve(serviceType interface{}) (interface{}, error) {
    c.mu.RLock()
    defer c.mu.RUnlock()

    t := reflect.TypeOf(serviceType)
    if t.Kind() == reflect.Ptr {
        t = t.Elem()
    }

    // Check singletons
    if instance, exists := c.singletons[t]; exists {
        return instance, nil
    }

    // Check factories
    if factory, exists := c.factories[t]; exists {
        return factory(), nil
    }

    return nil, fmt.Errorf("service of type %s not found", t)
}

// Auto-wire a struct
func (c *AdvancedContainer) AutoWire(target interface{}) error {
    targetValue := reflect.ValueOf(target)
    if targetValue.Kind() != reflect.Ptr || targetValue.Elem().Kind() != reflect.Struct {
        return fmt.Errorf("target must be a pointer to struct")
    }

    targetValue = targetValue.Elem()
    targetType := targetValue.Type()

    for i := 0; i < targetValue.NumField(); i++ {
        field := targetValue.Field(i)
        fieldType := targetType.Field(i)

        // Skip unexported fields
        if !field.CanSet() {
            continue
        }

        // Check for inject tag
        if fieldType.Tag.Get("inject") == "" {
            continue
        }

        // Resolve dependency
        service, err := c.Resolve(reflect.New(field.Type()).Interface())
        if err != nil {
            return fmt.Errorf("failed to resolve field %s: %w", fieldType.Name, err)
        }

        field.Set(reflect.ValueOf(service))
    }

    return nil
}

// Service with injection tags
type AutoWiredUserService struct {
    UserRepo UserRepository `inject:""`
    Cache    CacheService   `inject:""`
    Logger   Logger         `inject:""`
}

func (s *AutoWiredUserService) CreateUser(ctx context.Context, user *User) error {
    if err := s.UserRepo.Create(ctx, user); err != nil {
        s.Logger.Error("Failed to create user", "error", err)
        return err
    }

    cacheKey := fmt.Sprintf("user:%d", user.ID)
    if err := s.Cache.Set(ctx, cacheKey, user, 1*time.Hour); err != nil {
        s.Logger.Warn("Failed to cache user", "error", err)
    }

    s.Logger.Info("User created", "user_id", user.ID)
    return nil
}

func demonstrateAdvancedContainer() {
    container := NewAdvancedContainer()

    // Register services
    container.RegisterSingleton(&InMemoryUserRepository{})
    container.RegisterSingleton(&InMemoryCache{})
    container.RegisterSingleton(&StandardLogger{
        logger: log.New(os.Stdout, "", log.LstdFlags),
    })

    // Auto-wire service
    userService := &AutoWiredUserService{}
    if err := container.AutoWire(userService); err != nil {
        log.Fatal("Failed to auto-wire service:", err)
    }

    // Use the service
    ctx := context.Background()
    user := &User{Name: "Auto-wired User", Email: "<EMAIL>"}

    if err := userService.CreateUser(ctx, user); err != nil {
        log.Printf("Failed to create user: %v", err)
    }
}
```

## Popular DI Libraries

Overview of popular Go dependency injection libraries.

### Wire (Google)

```go
// Wire is a compile-time dependency injection tool
// go install github.com/google/wire/cmd/wire

//go:build wireinject
// +build wireinject

package main

import (
    "database/sql"
    "github.com/google/wire"
)

// Provider functions
func provideDatabase() (*sql.DB, error) {
    return sql.Open("postgres", "connection-string")
}

func provideUserRepository(db *sql.DB) UserRepository {
    return NewPostgresUserRepository(db)
}

func provideEmailService() EmailService {
    return NewSMTPEmailService("smtp.gmail.com", 587, "user", "pass")
}

func provideUserService(repo UserRepository, email EmailService) UserService {
    return NewUserService(repo, email)
}

// Wire set
var UserServiceSet = wire.NewSet(
    provideDatabase,
    provideUserRepository,
    provideEmailService,
    provideUserService,
)

// Injector function
func InitializeUserService() (UserService, error) {
    wire.Build(UserServiceSet)
    return nil, nil // Wire generates the implementation
}

// Generated code (by wire) would look like:
// func InitializeUserService() (UserService, error) {
//     db, err := provideDatabase()
//     if err != nil {
//         return nil, err
//     }
//     userRepository := provideUserRepository(db)
//     emailService := provideEmailService()
//     userService := provideUserService(userRepository, emailService)
//     return userService, nil
// }
```

### Dig (Uber)

```go
// go get go.uber.org/dig

import "go.uber.org/dig"

func demonstrateDig() {
    container := dig.New()

    // Provide dependencies
    container.Provide(func() *sql.DB {
        db, _ := sql.Open("postgres", "connection-string")
        return db
    })

    container.Provide(func(db *sql.DB) UserRepository {
        return NewPostgresUserRepository(db)
    })

    container.Provide(func() EmailService {
        return NewSMTPEmailService("smtp.gmail.com", 587, "user", "pass")
    })

    container.Provide(func(repo UserRepository, email EmailService) UserService {
        return NewUserService(repo, email)
    })

    // Invoke with dependencies
    err := container.Invoke(func(service UserService) {
        user, err := service.RegisterUser("Dig User", "<EMAIL>")
        if err != nil {
            log.Printf("Failed to register user: %v", err)
        } else {
            fmt.Printf("User registered via Dig: %+v\n", user)
        }
    })

    if err != nil {
        log.Fatal("Dig invocation failed:", err)
    }
}
```

### Fx (Uber)

```go
// go get go.uber.org/fx

import (
    "go.uber.org/fx"
    "go.uber.org/fx/fxevent"
    "go.uber.org/zap"
)

func demonstrateFx() {
    app := fx.New(
        // Provide dependencies
        fx.Provide(
            func() (*sql.DB, error) {
                return sql.Open("postgres", "connection-string")
            },
            func(db *sql.DB) UserRepository {
                return NewPostgresUserRepository(db)
            },
            func() EmailService {
                return NewSMTPEmailService("smtp.gmail.com", 587, "user", "pass")
            },
            func(repo UserRepository, email EmailService) UserService {
                return NewUserService(repo, email)
            },
            zap.NewExample, // Provide logger
        ),

        // Invoke application logic
        fx.Invoke(func(service UserService, logger *zap.Logger) {
            logger.Info("Starting application with Fx")

            user, err := service.RegisterUser("Fx User", "<EMAIL>")
            if err != nil {
                logger.Error("Failed to register user", zap.Error(err))
            } else {
                logger.Info("User registered via Fx", zap.Any("user", user))
            }
        }),

        // Configure logging
        fx.WithLogger(func(log *zap.Logger) fxevent.Logger {
            return &fxevent.ZapLogger{Logger: log}
        }),
    )

    // Start and stop the application
    startCtx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
    defer cancel()

    if err := app.Start(startCtx); err != nil {
        log.Fatal("Failed to start Fx app:", err)
    }

    stopCtx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
    defer cancel()

    if err := app.Stop(stopCtx); err != nil {
        log.Fatal("Failed to stop Fx app:", err)
    }
}
```

## Testing with DI

How dependency injection makes testing easier and more effective.

### Mock Injection for Testing

```go
// Mock implementations for testing
type MockUserRepository struct {
    users map[int]*User
    nextID int
    shouldFail bool
}

func NewMockUserRepository() *MockUserRepository {
    return &MockUserRepository{
        users:  make(map[int]*User),
        nextID: 1,
    }
}

func (m *MockUserRepository) SetShouldFail(fail bool) {
    m.shouldFail = fail
}

func (m *MockUserRepository) Create(ctx context.Context, user *User) error {
    if m.shouldFail {
        return fmt.Errorf("mock repository error")
    }

    user.ID = m.nextID
    m.nextID++
    m.users[user.ID] = user
    return nil
}

func (m *MockUserRepository) GetByID(ctx context.Context, id int) (*User, error) {
    if m.shouldFail {
        return nil, fmt.Errorf("mock repository error")
    }

    user, exists := m.users[id]
    if !exists {
        return nil, fmt.Errorf("user not found")
    }

    userCopy := *user
    return &userCopy, nil
}

func (m *MockUserRepository) GetByEmail(ctx context.Context, email string) (*User, error) {
    if m.shouldFail {
        return nil, fmt.Errorf("mock repository error")
    }

    for _, user := range m.users {
        if user.Email == email {
            userCopy := *user
            return &userCopy, nil
        }
    }

    return nil, fmt.Errorf("user not found")
}

func (m *MockUserRepository) Update(ctx context.Context, user *User) error {
    if m.shouldFail {
        return fmt.Errorf("mock repository error")
    }

    if _, exists := m.users[user.ID]; !exists {
        return fmt.Errorf("user not found")
    }

    m.users[user.ID] = user
    return nil
}

func (m *MockUserRepository) Delete(ctx context.Context, id int) error {
    if m.shouldFail {
        return fmt.Errorf("mock repository error")
    }

    delete(m.users, id)
    return nil
}

func (m *MockUserRepository) List(ctx context.Context, limit, offset int) ([]*User, error) {
    if m.shouldFail {
        return nil, fmt.Errorf("mock repository error")
    }

    var users []*User
    for _, user := range m.users {
        userCopy := *user
        users = append(users, &userCopy)
    }

    return users, nil
}

// Mock logger for testing
type MockLogger struct {
    logs []LogEntry
}

type LogEntry struct {
    Level   string
    Message string
    Fields  []interface{}
}

func NewMockLogger() *MockLogger {
    return &MockLogger{
        logs: make([]LogEntry, 0),
    }
}

func (m *MockLogger) Debug(msg string, fields ...interface{}) {
    m.logs = append(m.logs, LogEntry{Level: "DEBUG", Message: msg, Fields: fields})
}

func (m *MockLogger) Info(msg string, fields ...interface{}) {
    m.logs = append(m.logs, LogEntry{Level: "INFO", Message: msg, Fields: fields})
}

func (m *MockLogger) Warn(msg string, fields ...interface{}) {
    m.logs = append(m.logs, LogEntry{Level: "WARN", Message: msg, Fields: fields})
}

func (m *MockLogger) Error(msg string, fields ...interface{}) {
    m.logs = append(m.logs, LogEntry{Level: "ERROR", Message: msg, Fields: fields})
}

func (m *MockLogger) GetLogs() []LogEntry {
    return m.logs
}

func (m *MockLogger) HasLogWithLevel(level string) bool {
    for _, log := range m.logs {
        if log.Level == level {
            return true
        }
    }
    return false
}

// Test examples
func TestUserServiceV2_CreateUser(t *testing.T) {
    tests := []struct {
        name           string
        user           *User
        setupMocks     func(*MockUserRepository, *InMemoryCache, *MockLogger)
        expectError    bool
        expectedLogs   []string
    }{
        {
            name: "successful creation",
            user: &User{Name: "Test User", Email: "<EMAIL>"},
            setupMocks: func(repo *MockUserRepository, cache *InMemoryCache, logger *MockLogger) {
                // No special setup needed
            },
            expectError:  false,
            expectedLogs: []string{"INFO"},
        },
        {
            name: "repository failure",
            user: &User{Name: "Test User", Email: "<EMAIL>"},
            setupMocks: func(repo *MockUserRepository, cache *InMemoryCache, logger *MockLogger) {
                repo.SetShouldFail(true)
            },
            expectError:  true,
            expectedLogs: []string{"ERROR"},
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Setup mocks
            mockRepo := NewMockUserRepository()
            mockCache := &InMemoryCache{}
            mockLogger := NewMockLogger()

            tt.setupMocks(mockRepo, mockCache, mockLogger)

            // Create service with mocks
            service := NewUserServiceV2(mockRepo, mockCache, mockLogger)

            // Execute
            ctx := context.Background()
            err := service.CreateUser(ctx, tt.user)

            // Verify
            if tt.expectError && err == nil {
                t.Error("Expected error but got none")
            }

            if !tt.expectError && err != nil {
                t.Errorf("Unexpected error: %v", err)
            }

            // Verify logs
            for _, expectedLevel := range tt.expectedLogs {
                if !mockLogger.HasLogWithLevel(expectedLevel) {
                    t.Errorf("Expected log level %s not found", expectedLevel)
                }
            }
        })
    }
}

func TestUserServiceV2_GetUser(t *testing.T) {
    mockRepo := NewMockUserRepository()
    mockCache := &InMemoryCache{}
    mockLogger := NewMockLogger()

    service := NewUserServiceV2(mockRepo, mockCache, mockLogger)

    ctx := context.Background()

    // Create a user first
    user := &User{Name: "Test User", Email: "<EMAIL>"}
    err := service.CreateUser(ctx, user)
    if err != nil {
        t.Fatalf("Failed to create user: %v", err)
    }

    // Test getting the user
    retrievedUser, err := service.GetUser(ctx, user.ID)
    if err != nil {
        t.Fatalf("Failed to get user: %v", err)
    }

    if retrievedUser.ID != user.ID {
        t.Errorf("Expected user ID %d, got %d", user.ID, retrievedUser.ID)
    }

    if retrievedUser.Name != user.Name {
        t.Errorf("Expected user name %s, got %s", user.Name, retrievedUser.Name)
    }

    // Verify cache was used on second call
    mockLogger.logs = nil // Clear logs

    _, err = service.GetUser(ctx, user.ID)
    if err != nil {
        t.Fatalf("Failed to get user from cache: %v", err)
    }

    // Should have debug log about cache hit
    if !mockLogger.HasLogWithLevel("DEBUG") {
        t.Error("Expected DEBUG log for cache hit")
    }
}
```

## Best Practices

Guidelines for effective dependency injection in Go applications.

### Design Principles

```go
// 1. Depend on interfaces, not concrete types
type OrderService struct {
    repo    OrderRepository    // Interface, not *PostgresOrderRepository
    payment PaymentProcessor   // Interface, not *StripePayment
    logger  Logger            // Interface, not *ZapLogger
}

// 2. Keep constructors simple
func NewOrderService(repo OrderRepository, payment PaymentProcessor, logger Logger) *OrderService {
    return &OrderService{
        repo:    repo,
        payment: payment,
        logger:  logger,
    }
}

// 3. Validate dependencies in constructors
func NewOrderServiceWithValidation(repo OrderRepository, payment PaymentProcessor, logger Logger) (*OrderService, error) {
    if repo == nil {
        return nil, fmt.Errorf("order repository is required")
    }
    if payment == nil {
        return nil, fmt.Errorf("payment processor is required")
    }
    if logger == nil {
        return nil, fmt.Errorf("logger is required")
    }

    return &OrderService{
        repo:    repo,
        payment: payment,
        logger:  logger,
    }, nil
}

// 4. Use composition over inheritance
type EnhancedOrderService struct {
    *OrderService
    cache    CacheService
    metrics  MetricsCollector
}

func NewEnhancedOrderService(base *OrderService, cache CacheService, metrics MetricsCollector) *EnhancedOrderService {
    return &EnhancedOrderService{
        OrderService: base,
        cache:        cache,
        metrics:      metrics,
    }
}
```

### Interface Design

```go
// Good: Small, focused interfaces
type UserReader interface {
    GetByID(ctx context.Context, id int) (*User, error)
    GetByEmail(ctx context.Context, email string) (*User, error)
}

type UserWriter interface {
    Create(ctx context.Context, user *User) error
    Update(ctx context.Context, user *User) error
    Delete(ctx context.Context, id int) error
}

type UserRepository interface {
    UserReader
    UserWriter
}

// Good: Context-specific interfaces
type UserValidator interface {
    ValidateForCreation(user *User) error
    ValidateForUpdate(user *User) error
}

type UserNotifier interface {
    NotifyUserCreated(user *User) error
    NotifyUserUpdated(user *User) error
}

// Avoid: God interfaces
type BadUserService interface {
    // Too many responsibilities
    CreateUser(*User) error
    UpdateUser(*User) error
    DeleteUser(int) error
    ValidateUser(*User) error
    SendEmail(string, string) error
    LogActivity(string) error
    CacheUser(*User) error
    GenerateReport() ([]byte, error)
}
```

### Lifecycle Management

```go
// Service with proper lifecycle management
type Application struct {
    db       *sql.DB
    cache    CacheService
    services []Service
    logger   Logger
}

type Service interface {
    Start(ctx context.Context) error
    Stop(ctx context.Context) error
}

func NewApplication(config *Config) (*Application, error) {
    // Create database connection
    db, err := sql.Open("postgres", config.DatabaseURL)
    if err != nil {
        return nil, fmt.Errorf("failed to connect to database: %w", err)
    }

    // Create cache service
    cache := NewRedisCache(config.RedisURL)

    // Create logger
    logger := NewZapLogger(config.LogLevel)

    // Create services
    userRepo := NewPostgresUserRepository(db)
    userService := NewUserService(userRepo, cache, logger)

    orderRepo := NewPostgresOrderRepository(db)
    orderService := NewOrderService(orderRepo, cache, logger)

    services := []Service{userService, orderService}

    return &Application{
        db:       db,
        cache:    cache,
        services: services,
        logger:   logger,
    }, nil
}

func (app *Application) Start(ctx context.Context) error {
    app.logger.Info("Starting application")

    // Start all services
    for _, service := range app.services {
        if err := service.Start(ctx); err != nil {
            return fmt.Errorf("failed to start service: %w", err)
        }
    }

    app.logger.Info("Application started successfully")
    return nil
}

func (app *Application) Stop(ctx context.Context) error {
    app.logger.Info("Stopping application")

    // Stop services in reverse order
    for i := len(app.services) - 1; i >= 0; i-- {
        if err := app.services[i].Stop(ctx); err != nil {
            app.logger.Error("Failed to stop service", "error", err)
        }
    }

    // Close database connection
    if err := app.db.Close(); err != nil {
        app.logger.Error("Failed to close database", "error", err)
    }

    app.logger.Info("Application stopped")
    return nil
}
```

### Configuration Management

```go
// Environment-based configuration
type Environment string

const (
    Development Environment = "development"
    Staging     Environment = "staging"
    Production  Environment = "production"
)

type ServiceFactory struct {
    env    Environment
    config *Config
}

func NewServiceFactory(env Environment, config *Config) *ServiceFactory {
    return &ServiceFactory{
        env:    env,
        config: config,
    }
}

func (f *ServiceFactory) CreateUserRepository() (UserRepository, error) {
    switch f.env {
    case Development:
        return NewInMemoryUserRepository(), nil
    case Staging, Production:
        db, err := sql.Open("postgres", f.config.DatabaseURL)
        if err != nil {
            return nil, err
        }
        return NewPostgresUserRepository(db), nil
    default:
        return nil, fmt.Errorf("unknown environment: %s", f.env)
    }
}

func (f *ServiceFactory) CreateCacheService() (CacheService, error) {
    switch f.env {
    case Development:
        return NewInMemoryCache(), nil
    case Staging, Production:
        return NewRedisCache(f.config.RedisURL), nil
    default:
        return nil, fmt.Errorf("unknown environment: %s", f.env)
    }
}

func (f *ServiceFactory) CreateLogger() (Logger, error) {
    switch f.env {
    case Development:
        return NewStandardLogger(), nil
    case Staging, Production:
        return NewZapLogger(f.config.LogLevel), nil
    default:
        return nil, fmt.Errorf("unknown environment: %s", f.env)
    }
}
```

## Common Pitfalls

Avoiding common mistakes when implementing dependency injection.

### Circular Dependencies

```go
// Bad: Circular dependency
type UserService struct {
    orderService *OrderService // UserService depends on OrderService
}

type OrderService struct {
    userService *UserService   // OrderService depends on UserService
}

// Good: Break circular dependency with interfaces
type UserOrderProcessor interface {
    ProcessUserOrders(userID int) error
}

type OrderUserValidator interface {
    ValidateUserForOrder(userID int) error
}

type UserService struct {
    orderProcessor UserOrderProcessor // Interface dependency
}

type OrderService struct {
    userValidator OrderUserValidator  // Interface dependency
}

// Good: Use events to decouple services
type EventBus interface {
    Publish(event string, data interface{}) error
    Subscribe(event string, handler func(interface{})) error
}

type UserServiceV3 struct {
    repo     UserRepository
    eventBus EventBus
}

func (s *UserServiceV3) CreateUser(ctx context.Context, user *User) error {
    if err := s.repo.Create(ctx, user); err != nil {
        return err
    }

    // Publish event instead of direct dependency
    s.eventBus.Publish("user.created", user)
    return nil
}

type OrderServiceV3 struct {
    repo     OrderRepository
    eventBus EventBus
}

func NewOrderServiceV3(repo OrderRepository, eventBus EventBus) *OrderServiceV3 {
    service := &OrderServiceV3{
        repo:     repo,
        eventBus: eventBus,
    }

    // Subscribe to user events
    eventBus.Subscribe("user.created", service.handleUserCreated)
    return service
}

func (s *OrderServiceV3) handleUserCreated(data interface{}) {
    user := data.(*User)
    // Handle user creation (e.g., create welcome order)
    fmt.Printf("User created: %s, preparing welcome order\n", user.Name)
}
```

### Over-injection

```go
// Bad: Too many dependencies
type OverInjectedService struct {
    repo1    Repository1
    repo2    Repository2
    repo3    Repository3
    cache1   CacheService1
    cache2   CacheService2
    logger1  Logger1
    logger2  Logger2
    client1  HTTPClient1
    client2  HTTPClient2
    client3  HTTPClient3
    config1  Config1
    config2  Config2
    // ... 20+ dependencies
}

// Good: Group related dependencies
type DatabaseServices struct {
    UserRepo  UserRepository
    OrderRepo OrderRepository
    ProductRepo ProductRepository
}

type ExternalServices struct {
    PaymentClient PaymentClient
    EmailClient   EmailClient
    SMSClient     SMSClient
}

type Infrastructure struct {
    Cache  CacheService
    Logger Logger
    Metrics MetricsCollector
}

type WellStructuredService struct {
    db       *DatabaseServices
    external *ExternalServices
    infra    *Infrastructure
}

func NewWellStructuredService(db *DatabaseServices, external *ExternalServices, infra *Infrastructure) *WellStructuredService {
    return &WellStructuredService{
        db:       db,
        external: external,
        infra:    infra,
    }
}
```

### Improper Scope Management

```go
// Bad: Mixing singleton and transient scopes incorrectly
type BadScopeService struct {
    // This should be singleton but created as transient
    expensiveResource *ExpensiveResource

    // This should be transient but shared as singleton
    requestSpecificData *RequestData
}

// Good: Proper scope management
type ProperScopeService struct {
    // Singleton: shared across requests
    cache  CacheService
    logger Logger
    config *Config

    // Factory for transient objects
    requestDataFactory func() *RequestData
}

func NewProperScopeService(cache CacheService, logger Logger, config *Config) *ProperScopeService {
    return &ProperScopeService{
        cache:  cache,
        logger: logger,
        config: config,
        requestDataFactory: func() *RequestData {
            return &RequestData{
                Timestamp: time.Now(),
                RequestID: generateRequestID(),
            }
        },
    }
}

func (s *ProperScopeService) ProcessRequest(ctx context.Context) error {
    // Create transient request data
    requestData := s.requestDataFactory()

    // Use singleton services
    s.logger.Info("Processing request", "request_id", requestData.RequestID)

    return nil
}
```

## Performance Considerations

Understanding the performance implications of dependency injection.

### Container Performance

```go
// Benchmark different DI approaches
func BenchmarkManualInjection(b *testing.B) {
    repo := &InMemoryUserRepository{}
    cache := &InMemoryCache{}
    logger := &StandardLogger{logger: log.New(io.Discard, "", 0)}

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        service := NewUserServiceV2(repo, cache, logger)
        _ = service
    }
}

func BenchmarkContainerInjection(b *testing.B) {
    container := NewContainer()
    container.RegisterSingleton("UserRepository", &InMemoryUserRepository{})
    container.RegisterSingleton("CacheService", &InMemoryCache{})
    container.RegisterSingleton("Logger", &StandardLogger{logger: log.New(io.Discard, "", 0)})

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        repo, _ := container.ResolveUserRepository()
        cache, _ := container.Resolve("CacheService")
        logger, _ := container.ResolveLogger()

        service := NewUserServiceV2(repo, cache.(CacheService), logger)
        _ = service
    }
}

func BenchmarkReflectionInjection(b *testing.B) {
    container := NewAdvancedContainer()
    container.RegisterSingleton(&InMemoryUserRepository{})
    container.RegisterSingleton(&InMemoryCache{})
    container.RegisterSingleton(&StandardLogger{logger: log.New(io.Discard, "", 0)})

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        service := &AutoWiredUserService{}
        container.AutoWire(service)
        _ = service
    }
}
```

### Memory Management

```go
// Good: Proper resource cleanup
type ResourceManagedService struct {
    db     *sql.DB
    cache  CacheService
    logger Logger

    // Track resources for cleanup
    resources []io.Closer
}

func NewResourceManagedService(db *sql.DB, cache CacheService, logger Logger) *ResourceManagedService {
    service := &ResourceManagedService{
        db:        db,
        cache:     cache,
        logger:    logger,
        resources: make([]io.Closer, 0),
    }

    // Track closeable resources
    if closer, ok := cache.(io.Closer); ok {
        service.resources = append(service.resources, closer)
    }

    return service
}

func (s *ResourceManagedService) Close() error {
    var errors []error

    // Close all tracked resources
    for _, resource := range s.resources {
        if err := resource.Close(); err != nil {
            errors = append(errors, err)
        }
    }

    // Close database
    if err := s.db.Close(); err != nil {
        errors = append(errors, err)
    }

    if len(errors) > 0 {
        return fmt.Errorf("failed to close resources: %v", errors)
    }

    return nil
}

// Good: Lazy initialization for expensive resources
type LazyService struct {
    configLoader func() (*Config, error)
    config       *Config
    configOnce   sync.Once
    configErr    error
}

func NewLazyService(configLoader func() (*Config, error)) *LazyService {
    return &LazyService{
        configLoader: configLoader,
    }
}

func (s *LazyService) GetConfig() (*Config, error) {
    s.configOnce.Do(func() {
        s.config, s.configErr = s.configLoader()
    })
    return s.config, s.configErr
}
```

This comprehensive guide covers all aspects of dependency injection in Go, from basic manual injection to advanced container patterns and real-world best practices. Understanding these concepts will help you build maintainable, testable, and flexible Go applications.