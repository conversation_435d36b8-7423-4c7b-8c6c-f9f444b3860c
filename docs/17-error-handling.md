# Error Handling in Go

This comprehensive guide covers error handling in Go, including error types, wrapping, custom errors, best practices, and advanced patterns for robust error management.

## Table of Contents

1. [Overview](#overview)
2. [Error Interface](#error-interface)
3. [Basic Error Handling](#basic-error-handling)
4. [Creating Errors](#creating-errors)
5. [Custom Error Types](#custom-error-types)
6. [Error Wrapping](#error-wrapping)
7. [Error Unwrapping](#error-unwrapping)
8. [Sentinel Errors](#sentinel-errors)
9. [Error Handling Patterns](#error-handling-patterns)
10. [Panic and Recover](#panic-and-recover)
11. [Best Practices](#best-practices)
12. [Testing Error Conditions](#testing-error-conditions)
13. [Performance Considerations](#performance-considerations)

## Overview

Go's approach to error handling is explicit and straightforward. Errors are values that implement the error interface, and functions return errors as their last return value.

### Key Characteristics

```go
// Errors are values, not exceptions
// Functions return errors explicitly
// Error handling is explicit, not hidden
// Errors can be wrapped and unwrapped
// Custom error types provide rich context
```

### Error Handling Philosophy

| Aspect | Go Approach | Exception-based Languages |
|--------|-------------|---------------------------|
| **Visibility** | Explicit in function signature | Hidden in throws clauses |
| **Handling** | Immediate at call site | Can be caught anywhere up the stack |
| **Performance** | No stack unwinding overhead | Stack unwinding cost |
| **Control Flow** | Clear, linear flow | Non-linear with try/catch |
| **Debugging** | Clear error propagation path | Complex stack traces |

## Error Interface

Understanding the built-in error interface and how it works.

### The Error Interface

```go
// Built-in error interface
type error interface {
    Error() string
}

// Any type that implements Error() string satisfies the error interface
type MyError struct {
    Message string
    Code    int
}

func (e MyError) Error() string {
    return fmt.Sprintf("error %d: %s", e.Code, e.Message)
}

// Usage
func demonstrateErrorInterface() {
    var err error = MyError{
        Message: "something went wrong",
        Code:    500,
    }

    fmt.Println(err.Error()) // "error 500: something went wrong"
    fmt.Println(err)         // Same output (fmt.Println calls Error() method)
}
```

### Nil Error Values

```go
// nil represents no error
func successfulOperation() error {
    // Operation completed successfully
    return nil
}

// Checking for nil errors
func demonstrateNilErrors() {
    err := successfulOperation()

    if err != nil {
        fmt.Printf("Error occurred: %v\n", err)
        return
    }

    fmt.Println("Operation completed successfully")
}

// Important: nil interface vs nil concrete type
func demonstrateNilInterface() {
    var err error
    var myErr *MyError

    fmt.Printf("err == nil: %t\n", err == nil)       // true
    fmt.Printf("myErr == nil: %t\n", myErr == nil)   // true

    err = myErr // Assign nil concrete type to interface
    fmt.Printf("err == nil: %t\n", err == nil)       // false! (nil interface != nil concrete type)

    // Correct way to return nil error
    if someCondition := false; someCondition {
        return (*MyError)(nil) // Don't do this
    }
    return nil // Do this instead
}
```

## Basic Error Handling

Fundamental patterns for handling errors in Go.

### Standard Error Handling Pattern

```go
import (
    "errors"
    "fmt"
    "os"
    "strconv"
)

// Basic error checking pattern
func readFileSize(filename string) (int64, error) {
    file, err := os.Open(filename)
    if err != nil {
        return 0, err // Propagate error
    }
    defer file.Close()

    stat, err := file.Stat()
    if err != nil {
        return 0, err // Propagate error
    }

    return stat.Size(), nil // Success
}

// Multiple error handling
func processUserInput(input string) (*User, error) {
    // Validate input
    if input == "" {
        return nil, errors.New("input cannot be empty")
    }

    // Parse user ID
    userID, err := strconv.Atoi(input)
    if err != nil {
        return nil, fmt.Errorf("invalid user ID: %w", err)
    }

    // Fetch user from database
    user, err := fetchUserFromDB(userID)
    if err != nil {
        return nil, fmt.Errorf("failed to fetch user: %w", err)
    }

    return user, nil
}

// Error handling with early returns
func validateAndProcess(data []byte) error {
    if len(data) == 0 {
        return errors.New("data cannot be empty")
    }

    if len(data) > 1024*1024 { // 1MB limit
        return errors.New("data too large")
    }

    if err := validateFormat(data); err != nil {
        return fmt.Errorf("format validation failed: %w", err)
    }

    if err := processData(data); err != nil {
        return fmt.Errorf("processing failed: %w", err)
    }

    return nil // Success
}
```

### Error Handling in Loops

```go
// Handle errors in loops
func processFiles(filenames []string) error {
    var errors []error

    for _, filename := range filenames {
        if err := processFile(filename); err != nil {
            // Collect errors instead of failing immediately
            errors = append(errors, fmt.Errorf("failed to process %s: %w", filename, err))
        }
    }

    if len(errors) > 0 {
        return fmt.Errorf("processing failed for %d files: %v", len(errors), errors)
    }

    return nil
}

// Fail fast approach
func processFilesFailFast(filenames []string) error {
    for _, filename := range filenames {
        if err := processFile(filename); err != nil {
            return fmt.Errorf("failed to process %s: %w", filename, err)
        }
    }
    return nil
}

// Continue on error with logging
func processFilesContinue(filenames []string) {
    successCount := 0

    for _, filename := range filenames {
        if err := processFile(filename); err != nil {
            log.Printf("Warning: failed to process %s: %v", filename, err)
            continue
        }
        successCount++
    }

    log.Printf("Successfully processed %d out of %d files", successCount, len(filenames))
}
```

## Creating Errors

Different ways to create and return errors.

### Using errors.New()

```go
import "errors"

// Simple error creation
func validateAge(age int) error {
    if age < 0 {
        return errors.New("age cannot be negative")
    }

    if age > 150 {
        return errors.New("age cannot be greater than 150")
    }

    return nil
}

// Pre-defined error variables
var (
    ErrInvalidInput = errors.New("invalid input")
    ErrNotFound     = errors.New("not found")
    ErrUnauthorized = errors.New("unauthorized")
    ErrTimeout      = errors.New("operation timed out")
)

func lookupUser(id int) (*User, error) {
    if id <= 0 {
        return nil, ErrInvalidInput
    }

    user := findUserInDB(id)
    if user == nil {
        return nil, ErrNotFound
    }

    return user, nil
}
```

### Using fmt.Errorf()

```go
import "fmt"

// Formatted error messages
func connectToDatabase(host string, port int) error {
    if host == "" {
        return fmt.Errorf("host cannot be empty")
    }

    if port <= 0 || port > 65535 {
        return fmt.Errorf("invalid port: %d (must be 1-65535)", port)
    }

    // Simulate connection attempt
    if err := attemptConnection(host, port); err != nil {
        return fmt.Errorf("failed to connect to %s:%d: %w", host, port, err)
    }

    return nil
}

// Dynamic error messages
func validateUserData(user *User) error {
    if user == nil {
        return fmt.Errorf("user cannot be nil")
    }

    if user.Name == "" {
        return fmt.Errorf("user name is required")
    }

    if len(user.Name) < 2 {
        return fmt.Errorf("user name '%s' is too short (minimum 2 characters)", user.Name)
    }

    if user.Email == "" {
        return fmt.Errorf("email is required for user '%s'", user.Name)
    }

    return nil
}
```

## Custom Error Types

Creating rich error types that provide additional context and functionality.

### Basic Custom Error Types

```go
// Simple custom error type
type ValidationError struct {
    Field   string
    Value   interface{}
    Message string
}

func (e ValidationError) Error() string {
    return fmt.Sprintf("validation failed for field '%s' with value '%v': %s",
        e.Field, e.Value, e.Message)
}

// Network error with retry information
type NetworkError struct {
    Operation string
    URL       string
    Attempt   int
    Err       error
}

func (e NetworkError) Error() string {
    return fmt.Sprintf("network error during %s to %s (attempt %d): %v",
        e.Operation, e.URL, e.Attempt, e.Err)
}

func (e NetworkError) Unwrap() error {
    return e.Err
}

// Database error with SQL context
type DatabaseError struct {
    Query     string
    Args      []interface{}
    Operation string
    Err       error
}

func (e DatabaseError) Error() string {
    return fmt.Sprintf("database error during %s: %v\nQuery: %s\nArgs: %v",
        e.Operation, e.Err, e.Query, e.Args)
}

func (e DatabaseError) Unwrap() error {
    return e.Err
}

// Usage examples
func demonstrateCustomErrors() {
    // Validation error
    valErr := ValidationError{
        Field:   "email",
        Value:   "invalid-email",
        Message: "must be a valid email address",
    }
    fmt.Println(valErr) // Calls Error() method

    // Network error
    netErr := NetworkError{
        Operation: "GET",
        URL:       "https://api.example.com/users",
        Attempt:   3,
        Err:       errors.New("connection timeout"),
    }
    fmt.Println(netErr)

    // Database error
    dbErr := DatabaseError{
        Query:     "SELECT * FROM users WHERE id = ?",
        Args:      []interface{}{123},
        Operation: "SELECT",
        Err:       errors.New("connection lost"),
    }
    fmt.Println(dbErr)
}
```

### Error Types with Methods

```go
// HTTP error with status code
type HTTPError struct {
    StatusCode int
    Message    string
    Headers    map[string]string
    Body       []byte
}

func (e HTTPError) Error() string {
    return fmt.Sprintf("HTTP %d: %s", e.StatusCode, e.Message)
}

func (e HTTPError) IsClientError() bool {
    return e.StatusCode >= 400 && e.StatusCode < 500
}

func (e HTTPError) IsServerError() bool {
    return e.StatusCode >= 500 && e.StatusCode < 600
}

func (e HTTPError) IsRetryable() bool {
    // Retry on server errors and specific client errors
    return e.IsServerError() || e.StatusCode == 429 || e.StatusCode == 408
}

// File operation error with path information
type FileError struct {
    Path      string
    Operation string
    Err       error
}

func (e FileError) Error() string {
    return fmt.Sprintf("file error: %s failed for '%s': %v", e.Operation, e.Path, e.Err)
}

func (e FileError) Unwrap() error {
    return e.Err
}

func (e FileError) IsNotFound() bool {
    return os.IsNotExist(e.Err)
}

func (e FileError) IsPermissionDenied() bool {
    return os.IsPermission(e.Err)
}

// Usage with error type checking
func handleFileOperation(filename string) {
    err := readFile(filename)
    if err != nil {
        var fileErr FileError
        if errors.As(err, &fileErr) {
            if fileErr.IsNotFound() {
                fmt.Printf("File not found: %s\n", fileErr.Path)
                return
            }
            if fileErr.IsPermissionDenied() {
                fmt.Printf("Permission denied: %s\n", fileErr.Path)
                return
            }
        }

        var httpErr HTTPError
        if errors.As(err, &httpErr) {
            if httpErr.IsRetryable() {
                fmt.Printf("Retryable HTTP error: %v\n", httpErr)
                return
            }
        }

        fmt.Printf("Unknown error: %v\n", err)
    }
}
```

### Error Types with Context

```go
// Multi-error type for collecting multiple errors
type MultiError struct {
    Errors []error
}

func (e MultiError) Error() string {
    if len(e.Errors) == 0 {
        return "no errors"
    }

    if len(e.Errors) == 1 {
        return e.Errors[0].Error()
    }

    var messages []string
    for i, err := range e.Errors {
        messages = append(messages, fmt.Sprintf("%d: %v", i+1, err))
    }

    return fmt.Sprintf("multiple errors occurred:\n%s", strings.Join(messages, "\n"))
}

func (e MultiError) Add(err error) {
    if err != nil {
        e.Errors = append(e.Errors, err)
    }
}

func (e MultiError) HasErrors() bool {
    return len(e.Errors) > 0
}

func (e MultiError) Unwrap() []error {
    return e.Errors
}

// Contextual error with stack trace
type ContextualError struct {
    Message   string
    Context   map[string]interface{}
    Cause     error
    Timestamp time.Time
    Stack     []string
}

func (e ContextualError) Error() string {
    var parts []string
    parts = append(parts, e.Message)

    if e.Cause != nil {
        parts = append(parts, fmt.Sprintf("caused by: %v", e.Cause))
    }

    if len(e.Context) > 0 {
        var contextParts []string
        for k, v := range e.Context {
            contextParts = append(contextParts, fmt.Sprintf("%s=%v", k, v))
        }
        parts = append(parts, fmt.Sprintf("context: %s", strings.Join(contextParts, ", ")))
    }

    return strings.Join(parts, "; ")
}

func (e ContextualError) Unwrap() error {
    return e.Cause
}

func NewContextualError(message string, cause error) *ContextualError {
    return &ContextualError{
        Message:   message,
        Context:   make(map[string]interface{}),
        Cause:     cause,
        Timestamp: time.Now(),
        Stack:     captureStack(),
    }
}

func (e *ContextualError) WithContext(key string, value interface{}) *ContextualError {
    e.Context[key] = value
    return e
}

// Usage
func demonstrateContextualError() {
    originalErr := errors.New("database connection failed")

    contextErr := NewContextualError("user operation failed", originalErr).
        WithContext("userID", 12345).
        WithContext("operation", "update").
        WithContext("table", "users")

    fmt.Println(contextErr)
}
```

## Error Wrapping

Using Go 1.13+ error wrapping features for better error context.

### Basic Error Wrapping

```go
import (
    "errors"
    "fmt"
)

// Wrapping errors with fmt.Errorf and %w verb
func processUserData(userID int) error {
    user, err := fetchUser(userID)
    if err != nil {
        return fmt.Errorf("failed to fetch user %d: %w", userID, err)
    }

    if err := validateUser(user); err != nil {
        return fmt.Errorf("user %d validation failed: %w", userID, err)
    }

    if err := saveUser(user); err != nil {
        return fmt.Errorf("failed to save user %d: %w", userID, err)
    }

    return nil
}

// Building error chains
func handleRequest(requestID string) error {
    if err := authenticateRequest(requestID); err != nil {
        return fmt.Errorf("authentication failed for request %s: %w", requestID, err)
    }

    if err := processRequest(requestID); err != nil {
        return fmt.Errorf("processing failed for request %s: %w", requestID, err)
    }

    return nil
}

func authenticateRequest(requestID string) error {
    token, err := extractToken(requestID)
    if err != nil {
        return fmt.Errorf("token extraction failed: %w", err)
    }

    if err := validateToken(token); err != nil {
        return fmt.Errorf("token validation failed: %w", err)
    }

    return nil
}

// Demonstrating error chain
func demonstrateErrorChain() {
    err := handleRequest("req-123")
    if err != nil {
        fmt.Printf("Error: %v\n", err)

        // Print the full error chain
        for err != nil {
            fmt.Printf("  -> %v\n", err)
            err = errors.Unwrap(err)
        }
    }
}
```

### Custom Wrapper Types

```go
// Custom wrapper that adds timing information
type TimedError struct {
    Err      error
    Duration time.Duration
    Start    time.Time
}

func (e TimedError) Error() string {
    return fmt.Sprintf("%v (took %v)", e.Err, e.Duration)
}

func (e TimedError) Unwrap() error {
    return e.Err
}

func WithTiming(fn func() error) error {
    start := time.Now()
    err := fn()
    duration := time.Since(start)

    if err != nil {
        return TimedError{
            Err:      err,
            Duration: duration,
            Start:    start,
        }
    }

    return nil
}

// Wrapper that adds retry information
type RetryError struct {
    Err        error
    Attempts   int
    MaxRetries int
    LastDelay  time.Duration
}

func (e RetryError) Error() string {
    return fmt.Sprintf("%v (failed after %d/%d attempts)", e.Err, e.Attempts, e.MaxRetries)
}

func (e RetryError) Unwrap() error {
    return e.Err
}

func WithRetry(fn func() error, maxRetries int) error {
    var lastErr error
    delay := 100 * time.Millisecond

    for attempt := 1; attempt <= maxRetries; attempt++ {
        err := fn()
        if err == nil {
            return nil
        }

        lastErr = err

        if attempt < maxRetries {
            time.Sleep(delay)
            delay *= 2 // Exponential backoff
        }
    }

    return RetryError{
        Err:        lastErr,
        Attempts:   maxRetries,
        MaxRetries: maxRetries,
        LastDelay:  delay / 2,
    }
}

// Usage
func demonstrateCustomWrappers() {
    // Timed operation
    err := WithTiming(func() error {
        time.Sleep(100 * time.Millisecond)
        return errors.New("operation failed")
    })

    if err != nil {
        fmt.Printf("Timed error: %v\n", err)
    }

    // Retry operation
    err = WithRetry(func() error {
        if rand.Float32() < 0.8 {
            return errors.New("random failure")
        }
        return nil
    }, 3)

    if err != nil {
        fmt.Printf("Retry error: %v\n", err)
    }
}
```

## Error Unwrapping

Using errors.Is() and errors.As() to work with wrapped errors.

### Using errors.Is()

```go
import "errors"

// Sentinel errors
var (
    ErrNotFound     = errors.New("not found")
    ErrUnauthorized = errors.New("unauthorized")
    ErrInvalidInput = errors.New("invalid input")
)

// Function that wraps sentinel errors
func fetchUser(id int) (*User, error) {
    if id <= 0 {
        return nil, fmt.Errorf("user ID must be positive: %w", ErrInvalidInput)
    }

    user := findUserInDatabase(id)
    if user == nil {
        return nil, fmt.Errorf("user with ID %d: %w", id, ErrNotFound)
    }

    if !hasPermission(user) {
        return nil, fmt.Errorf("access denied for user %d: %w", id, ErrUnauthorized)
    }

    return user, nil
}

// Using errors.Is() to check for specific errors
func handleUserFetch(id int) {
    user, err := fetchUser(id)
    if err != nil {
        // Check for specific error types
        if errors.Is(err, ErrNotFound) {
            fmt.Printf("User %d not found, creating new user\n", id)
            // Handle not found case
            return
        }

        if errors.Is(err, ErrUnauthorized) {
            fmt.Printf("Access denied for user %d\n", id)
            // Handle unauthorized case
            return
        }

        if errors.Is(err, ErrInvalidInput) {
            fmt.Printf("Invalid user ID: %d\n", id)
            // Handle invalid input case
            return
        }

        // Handle other errors
        fmt.Printf("Unexpected error: %v\n", err)
        return
    }

    fmt.Printf("User found: %+v\n", user)
}
```

### Using errors.As()

```go
// Custom error types for errors.As()
type ValidationError struct {
    Field   string
    Value   interface{}
    Message string
}

func (e ValidationError) Error() string {
    return fmt.Sprintf("validation failed for %s: %s", e.Field, e.Message)
}

type NetworkError struct {
    URL        string
    StatusCode int
    Retryable  bool
    Err        error
}

func (e NetworkError) Error() string {
    return fmt.Sprintf("network error for %s (status %d): %v", e.URL, e.StatusCode, e.Err)
}

func (e NetworkError) Unwrap() error {
    return e.Err
}

// Functions that return wrapped custom errors
func validateUserInput(input map[string]interface{}) error {
    if name, ok := input["name"].(string); !ok || name == "" {
        return fmt.Errorf("user validation failed: %w", ValidationError{
            Field:   "name",
            Value:   input["name"],
            Message: "name is required and must be a string",
        })
    }

    if age, ok := input["age"].(float64); !ok || age < 0 {
        return fmt.Errorf("user validation failed: %w", ValidationError{
            Field:   "age",
            Value:   input["age"],
            Message: "age must be a non-negative number",
        })
    }

    return nil
}

func makeAPICall(url string) error {
    // Simulate API call
    statusCode := 500
    if statusCode >= 400 {
        netErr := NetworkError{
            URL:        url,
            StatusCode: statusCode,
            Retryable:  statusCode >= 500,
            Err:        errors.New("internal server error"),
        }
        return fmt.Errorf("API call failed: %w", netErr)
    }

    return nil
}

// Using errors.As() to extract custom error types
func handleErrors() {
    // Test validation error
    input := map[string]interface{}{
        "name": "",
        "age":  -5,
    }

    if err := validateUserInput(input); err != nil {
        var valErr ValidationError
        if errors.As(err, &valErr) {
            fmt.Printf("Validation failed for field '%s' with value '%v': %s\n",
                valErr.Field, valErr.Value, valErr.Message)
        } else {
            fmt.Printf("Unknown validation error: %v\n", err)
        }
    }

    // Test network error
    if err := makeAPICall("https://api.example.com/users"); err != nil {
        var netErr NetworkError
        if errors.As(err, &netErr) {
            fmt.Printf("Network error for %s (status %d)\n", netErr.URL, netErr.StatusCode)
            if netErr.Retryable {
                fmt.Println("This error is retryable")
            }
        } else {
            fmt.Printf("Unknown network error: %v\n", err)
        }
    }
}
```

### Advanced Unwrapping Patterns

```go
// Function to extract all errors in a chain
func extractErrorChain(err error) []error {
    var errors []error

    for err != nil {
        errors = append(errors, err)
        err = errors.Unwrap(err)
    }

    return errors
}

// Function to find specific error type in chain
func findErrorType[T error](err error) (T, bool) {
    var target T

    for err != nil {
        if errors.As(err, &target) {
            return target, true
        }
        err = errors.Unwrap(err)
    }

    return target, false
}

// Function to check if error chain contains specific error
func containsError(err error, target error) bool {
    for err != nil {
        if errors.Is(err, target) {
            return true
        }
        err = errors.Unwrap(err)
    }
    return false
}

// Usage examples
func demonstrateAdvancedUnwrapping() {
    // Create a complex error chain
    originalErr := ErrNotFound
    wrappedErr := fmt.Errorf("database query failed: %w", originalErr)
    finalErr := fmt.Errorf("user service error: %w", wrappedErr)

    // Extract full error chain
    chain := extractErrorChain(finalErr)
    fmt.Printf("Error chain has %d errors:\n", len(chain))
    for i, err := range chain {
        fmt.Printf("  %d: %v\n", i+1, err)
    }

    // Find specific error type
    if netErr, found := findErrorType[NetworkError](finalErr); found {
        fmt.Printf("Found network error: %+v\n", netErr)
    } else {
        fmt.Println("No network error found in chain")
    }

    // Check if chain contains specific error
    if containsError(finalErr, ErrNotFound) {
        fmt.Println("Error chain contains ErrNotFound")
    }
}
```

## Sentinel Errors

Pre-defined error values for common error conditions.

### Defining Sentinel Errors

```go
// Package-level sentinel errors
var (
    ErrUserNotFound    = errors.New("user not found")
    ErrInvalidPassword = errors.New("invalid password")
    ErrAccountLocked   = errors.New("account locked")
    ErrSessionExpired  = errors.New("session expired")
    ErrInsufficientFunds = errors.New("insufficient funds")
)

// Grouped sentinel errors
type AuthError string

func (e AuthError) Error() string {
    return string(e)
}

const (
    ErrInvalidCredentials AuthError = "invalid credentials"
    ErrAccountDisabled    AuthError = "account disabled"
    ErrTooManyAttempts    AuthError = "too many login attempts"
)

// Domain-specific sentinel errors
type PaymentError string

func (e PaymentError) Error() string {
    return string(e)
}

const (
    ErrCardDeclined     PaymentError = "card declined"
    ErrInvalidCardNumber PaymentError = "invalid card number"
    ErrCardExpired      PaymentError = "card expired"
)
```

### Using Sentinel Errors

```go
// Authentication service
func authenticateUser(username, password string) (*User, error) {
    user, err := findUserByUsername(username)
    if err != nil {
        return nil, fmt.Errorf("user lookup failed: %w", err)
    }

    if user == nil {
        return nil, ErrUserNotFound
    }

    if user.IsLocked {
        return nil, ErrAccountLocked
    }

    if !verifyPassword(user, password) {
        return nil, ErrInvalidPassword
    }

    if user.SessionExpired() {
        return nil, ErrSessionExpired
    }

    return user, nil
}

// Payment service
func processPayment(amount float64, cardInfo CardInfo) error {
    if amount <= 0 {
        return ErrInvalidInput
    }

    if !validateCardNumber(cardInfo.Number) {
        return ErrInvalidCardNumber
    }

    if cardInfo.IsExpired() {
        return ErrCardExpired
    }

    if !chargeCard(cardInfo, amount) {
        return ErrCardDeclined
    }

    return nil
}

// Error handling with sentinel errors
func handleAuthenticationError(err error) {
    switch {
    case errors.Is(err, ErrUserNotFound):
        fmt.Println("User not found. Please check your username.")
    case errors.Is(err, ErrInvalidPassword):
        fmt.Println("Invalid password. Please try again.")
    case errors.Is(err, ErrAccountLocked):
        fmt.Println("Account is locked. Please contact support.")
    case errors.Is(err, ErrSessionExpired):
        fmt.Println("Session expired. Please log in again.")
    default:
        fmt.Printf("Authentication failed: %v\n", err)
    }
}

func handlePaymentError(err error) {
    switch {
    case errors.Is(err, ErrCardDeclined):
        fmt.Println("Card was declined. Please try a different card.")
    case errors.Is(err, ErrInvalidCardNumber):
        fmt.Println("Invalid card number. Please check and try again.")
    case errors.Is(err, ErrCardExpired):
        fmt.Println("Card has expired. Please use a valid card.")
    default:
        fmt.Printf("Payment failed: %v\n", err)
    }
}
```

### Sentinel Error Best Practices

```go
// Good: Descriptive error messages
var (
    ErrConfigFileNotFound = errors.New("configuration file not found")
    ErrInvalidConfigFormat = errors.New("configuration file format is invalid")
    ErrMissingRequiredField = errors.New("required configuration field is missing")
)

// Good: Domain-specific error grouping
type DatabaseError string

func (e DatabaseError) Error() string {
    return string(e)
}

const (
    ErrConnectionFailed   DatabaseError = "database connection failed"
    ErrQueryTimeout       DatabaseError = "database query timeout"
    ErrConstraintViolation DatabaseError = "database constraint violation"
    ErrTransactionFailed  DatabaseError = "database transaction failed"
)

// Good: Error variables with context
var (
    ErrUserAlreadyExists = errors.New("user with this email already exists")
    ErrInvalidEmailFormat = errors.New("email format is invalid")
    ErrPasswordTooWeak = errors.New("password does not meet security requirements")
)

// Avoid: Generic error messages
var (
    ErrBadInput = errors.New("bad input") // Too generic
    ErrFailed   = errors.New("failed")    // Too generic
    ErrError    = errors.New("error")     // Meaningless
)

// Good: Hierarchical error checking
func isRetryableError(err error) bool {
    return errors.Is(err, ErrConnectionFailed) ||
           errors.Is(err, ErrQueryTimeout) ||
           errors.Is(err, ErrTransactionFailed)
}

func isUserError(err error) bool {
    return errors.Is(err, ErrInvalidInput) ||
           errors.Is(err, ErrUserNotFound) ||
           errors.Is(err, ErrInvalidPassword)
}

func isSystemError(err error) bool {
    return errors.Is(err, ErrConnectionFailed) ||
           errors.Is(err, ErrQueryTimeout) ||
           errors.Is(err, ErrConfigFileNotFound)
}
```

## Panic and Recover

Understanding when and how to use panic and recover in Go.

### When to Use Panic

```go
// Panic for unrecoverable errors
func mustParseConfig(filename string) *Config {
    config, err := parseConfig(filename)
    if err != nil {
        panic(fmt.Sprintf("failed to parse config file %s: %v", filename, err))
    }
    return config
}

// Panic for programming errors
func divide(a, b float64) float64 {
    if b == 0 {
        panic("division by zero") // Programming error, should be checked by caller
    }
    return a / b
}

// Panic for impossible conditions
func processState(state string) {
    switch state {
    case "active", "inactive", "pending":
        // Handle valid states
    default:
        panic(fmt.Sprintf("impossible state: %s", state)) // Should never happen
    }
}
```

### Using Recover

```go
// Basic recover pattern
func safeOperation() (result string, err error) {
    defer func() {
        if r := recover(); r != nil {
            err = fmt.Errorf("operation panicked: %v", r)
        }
    }()

    // Code that might panic
    result = riskyOperation()
    return result, nil
}

// Recover with cleanup
func processWithCleanup(data []byte) (err error) {
    resource := acquireResource()

    defer func() {
        // Always cleanup
        resource.Release()

        // Handle panic
        if r := recover(); r != nil {
            err = fmt.Errorf("processing panicked: %v", r)
        }
    }()

    // Process data (might panic)
    return processData(data)
}

// Selective recover
func selectiveRecover() error {
    defer func() {
        if r := recover(); r != nil {
            // Only recover from specific panic types
            if err, ok := r.(error); ok && errors.Is(err, ErrExpectedPanic) {
                log.Printf("Recovered from expected panic: %v", err)
                return
            }

            // Re-panic for unexpected panics
            panic(r)
        }
    }()

    // Code that might panic
    riskyOperation()
    return nil
}
```

### Panic Best Practices

```go
// Good: Use panic for library initialization errors
func init() {
    if err := initializeLibrary(); err != nil {
        panic(fmt.Sprintf("failed to initialize library: %v", err))
    }
}

// Good: Use panic for assertion-like checks in tests
func TestSomething(t *testing.T) {
    defer func() {
        if r := recover(); r != nil {
            t.Fatalf("Test panicked: %v", r)
        }
    }()

    // Test code that should not panic
    result := functionUnderTest()
    if result == nil {
        panic("result should not be nil") // Test assertion
    }
}

// Good: Graceful server shutdown with recover
func handleRequest(w http.ResponseWriter, r *http.Request) {
    defer func() {
        if r := recover(); r != nil {
            log.Printf("Request handler panicked: %v", r)
            http.Error(w, "Internal Server Error", http.StatusInternalServerError)
        }
    }()

    // Request handling code
    processRequest(w, r)
}

// Avoid: Using panic for normal error conditions
func badExample(filename string) {
    file, err := os.Open(filename)
    if err != nil {
        panic(err) // Don't do this - use proper error handling
    }
    defer file.Close()
}

// Good: Proper error handling
func goodExample(filename string) error {
    file, err := os.Open(filename)
    if err != nil {
        return fmt.Errorf("failed to open file: %w", err)
    }
    defer file.Close()

    return nil
}
```

## Best Practices

Guidelines for effective error handling in Go applications.

### Error Message Guidelines

```go
// Good: Clear, actionable error messages
func validateUser(user *User) error {
    if user == nil {
        return errors.New("user cannot be nil")
    }

    if user.Email == "" {
        return errors.New("user email is required")
    }

    if !isValidEmail(user.Email) {
        return fmt.Errorf("invalid email format: %s", user.Email)
    }

    if user.Age < 0 {
        return fmt.Errorf("user age cannot be negative: %d", user.Age)
    }

    return nil
}

// Good: Include context in error messages
func processFile(filename string) error {
    file, err := os.Open(filename)
    if err != nil {
        return fmt.Errorf("failed to open file %s: %w", filename, err)
    }
    defer file.Close()

    data, err := io.ReadAll(file)
    if err != nil {
        return fmt.Errorf("failed to read file %s: %w", filename, err)
    }

    if err := validateFileData(data); err != nil {
        return fmt.Errorf("invalid data in file %s: %w", filename, err)
    }

    return nil
}

// Avoid: Vague error messages
func badValidateUser(user *User) error {
    if user == nil {
        return errors.New("invalid") // Too vague
    }

    if user.Email == "" {
        return errors.New("missing field") // Which field?
    }

    return nil
}
```

### Error Handling Patterns

```go
// Pattern 1: Early return for error conditions
func processOrder(order *Order) error {
    if order == nil {
        return errors.New("order cannot be nil")
    }

    if err := validateOrder(order); err != nil {
        return fmt.Errorf("order validation failed: %w", err)
    }

    if err := checkInventory(order); err != nil {
        return fmt.Errorf("inventory check failed: %w", err)
    }

    if err := processPayment(order); err != nil {
        return fmt.Errorf("payment processing failed: %w", err)
    }

    if err := fulfillOrder(order); err != nil {
        return fmt.Errorf("order fulfillment failed: %w", err)
    }

    return nil
}

// Pattern 2: Collect and return multiple errors
func validateUserData(users []User) error {
    var validationErrors []error

    for i, user := range users {
        if err := validateUser(&user); err != nil {
            validationErrors = append(validationErrors,
                fmt.Errorf("user %d: %w", i, err))
        }
    }

    if len(validationErrors) > 0 {
        return fmt.Errorf("validation failed for %d users: %v",
            len(validationErrors), validationErrors)
    }

    return nil
}

// Pattern 3: Error handling with cleanup
func processWithTransaction(db *sql.DB, fn func(*sql.Tx) error) error {
    tx, err := db.Begin()
    if err != nil {
        return fmt.Errorf("failed to begin transaction: %w", err)
    }

    defer func() {
        if err != nil {
            if rollbackErr := tx.Rollback(); rollbackErr != nil {
                log.Printf("Failed to rollback transaction: %v", rollbackErr)
            }
        }
    }()

    if err = fn(tx); err != nil {
        return err // Rollback will be called by defer
    }

    if err = tx.Commit(); err != nil {
        return fmt.Errorf("failed to commit transaction: %w", err)
    }

    return nil
}

// Pattern 4: Retry with exponential backoff
func retryWithBackoff(operation func() error, maxRetries int) error {
    var lastErr error
    delay := 100 * time.Millisecond

    for attempt := 1; attempt <= maxRetries; attempt++ {
        err := operation()
        if err == nil {
            return nil
        }

        lastErr = err

        // Don't sleep after the last attempt
        if attempt < maxRetries {
            time.Sleep(delay)
            delay *= 2 // Exponential backoff
        }
    }

    return fmt.Errorf("operation failed after %d attempts: %w", maxRetries, lastErr)
}
```

### Error Context and Logging

```go
// Add structured context to errors
type ErrorContext struct {
    UserID    int
    RequestID string
    Operation string
    Timestamp time.Time
}

func (ec ErrorContext) String() string {
    return fmt.Sprintf("user=%d request=%s operation=%s time=%s",
        ec.UserID, ec.RequestID, ec.Operation, ec.Timestamp.Format(time.RFC3339))
}

func processUserRequest(ctx ErrorContext, data []byte) error {
    if err := validateRequestData(data); err != nil {
        return fmt.Errorf("request validation failed [%s]: %w", ctx, err)
    }

    if err := processData(data); err != nil {
        return fmt.Errorf("data processing failed [%s]: %w", ctx, err)
    }

    return nil
}

// Structured logging with errors
func logError(err error, context map[string]interface{}) {
    logEntry := map[string]interface{}{
        "error":     err.Error(),
        "timestamp": time.Now(),
    }

    // Add context
    for k, v := range context {
        logEntry[k] = v
    }

    // Add error chain information
    var errorChain []string
    for e := err; e != nil; e = errors.Unwrap(e) {
        errorChain = append(errorChain, e.Error())
    }
    logEntry["error_chain"] = errorChain

    // Log as JSON (in real code, use a proper logging library)
    jsonData, _ := json.Marshal(logEntry)
    log.Printf("ERROR: %s", jsonData)
}

// Usage
func demonstrateErrorLogging() {
    ctx := ErrorContext{
        UserID:    12345,
        RequestID: "req-abc-123",
        Operation: "update_profile",
        Timestamp: time.Now(),
    }

    err := processUserRequest(ctx, []byte("invalid data"))
    if err != nil {
        logError(err, map[string]interface{}{
            "user_id":    ctx.UserID,
            "request_id": ctx.RequestID,
            "operation":  ctx.Operation,
        })
    }
}
```

## Testing Error Conditions

Strategies for testing error handling in Go.

### Testing Error Cases

```go
import "testing"

// Test function that returns errors
func divide(a, b float64) (float64, error) {
    if b == 0 {
        return 0, errors.New("division by zero")
    }
    return a / b, nil
}

// Test successful case
func TestDivide_Success(t *testing.T) {
    result, err := divide(10, 2)
    if err != nil {
        t.Fatalf("Expected no error, got %v", err)
    }

    expected := 5.0
    if result != expected {
        t.Errorf("Expected %f, got %f", expected, result)
    }
}

// Test error case
func TestDivide_DivisionByZero(t *testing.T) {
    _, err := divide(10, 0)
    if err == nil {
        t.Fatal("Expected error for division by zero, got nil")
    }

    expectedMsg := "division by zero"
    if err.Error() != expectedMsg {
        t.Errorf("Expected error message %q, got %q", expectedMsg, err.Error())
    }
}

// Test with specific error types
func TestValidateUser_ValidationError(t *testing.T) {
    user := &User{Email: "invalid-email"}

    err := validateUser(user)
    if err == nil {
        t.Fatal("Expected validation error, got nil")
    }

    var valErr ValidationError
    if !errors.As(err, &valErr) {
        t.Fatalf("Expected ValidationError, got %T", err)
    }

    if valErr.Field != "email" {
        t.Errorf("Expected field 'email', got %q", valErr.Field)
    }
}
```

### Table-Driven Error Tests

```go
func TestProcessPayment(t *testing.T) {
    tests := []struct {
        name        string
        amount      float64
        cardInfo    CardInfo
        expectedErr error
    }{
        {
            name:        "successful payment",
            amount:      100.0,
            cardInfo:    CardInfo{Number: "****************", Expiry: "12/25"},
            expectedErr: nil,
        },
        {
            name:        "invalid amount",
            amount:      -10.0,
            cardInfo:    CardInfo{Number: "****************", Expiry: "12/25"},
            expectedErr: ErrInvalidInput,
        },
        {
            name:        "invalid card number",
            amount:      100.0,
            cardInfo:    CardInfo{Number: "invalid", Expiry: "12/25"},
            expectedErr: ErrInvalidCardNumber,
        },
        {
            name:        "expired card",
            amount:      100.0,
            cardInfo:    CardInfo{Number: "****************", Expiry: "12/20"},
            expectedErr: ErrCardExpired,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            err := processPayment(tt.amount, tt.cardInfo)

            if tt.expectedErr == nil {
                if err != nil {
                    t.Errorf("Expected no error, got %v", err)
                }
                return
            }

            if err == nil {
                t.Errorf("Expected error %v, got nil", tt.expectedErr)
                return
            }

            if !errors.Is(err, tt.expectedErr) {
                t.Errorf("Expected error %v, got %v", tt.expectedErr, err)
            }
        })
    }
}
```

### Testing Error Wrapping

```go
func TestErrorWrapping(t *testing.T) {
    // Create a chain of wrapped errors
    originalErr := errors.New("original error")
    wrappedErr := fmt.Errorf("wrapped: %w", originalErr)
    finalErr := fmt.Errorf("final: %w", wrappedErr)

    // Test that we can find the original error
    if !errors.Is(finalErr, originalErr) {
        t.Error("Expected to find original error in chain")
    }

    // Test error message contains all context
    expectedSubstrings := []string{"final", "wrapped", "original error"}
    errMsg := finalErr.Error()

    for _, substr := range expectedSubstrings {
        if !strings.Contains(errMsg, substr) {
            t.Errorf("Expected error message to contain %q, got %q", substr, errMsg)
        }
    }
}

// Test custom error unwrapping
func TestCustomErrorUnwrapping(t *testing.T) {
    originalErr := errors.New("database connection failed")
    dbErr := DatabaseError{
        Query:     "SELECT * FROM users",
        Operation: "SELECT",
        Err:       originalErr,
    }

    // Test that Unwrap works correctly
    unwrapped := errors.Unwrap(dbErr)
    if unwrapped != originalErr {
        t.Errorf("Expected unwrapped error to be %v, got %v", originalErr, unwrapped)
    }

    // Test that errors.Is works with wrapped custom errors
    if !errors.Is(dbErr, originalErr) {
        t.Error("Expected errors.Is to find original error in custom error")
    }
}
```

### Mock Errors for Testing

```go
// Mock error for testing
type MockError struct {
    message string
    code    int
}

func (e MockError) Error() string {
    return e.message
}

func (e MockError) Code() int {
    return e.code
}

// Test helper for creating mock errors
func NewMockError(message string, code int) MockError {
    return MockError{message: message, code: code}
}

// Test with mock errors
func TestErrorHandling_WithMocks(t *testing.T) {
    mockErr := NewMockError("mock error", 500)

    // Test that our error handling works with mock errors
    result := handleError(mockErr)

    expected := "handled error: mock error (code: 500)"
    if result != expected {
        t.Errorf("Expected %q, got %q", expected, result)
    }
}

// Test error behavior with interfaces
type ErrorWithCode interface {
    error
    Code() int
}

func handleError(err error) string {
    if errWithCode, ok := err.(ErrorWithCode); ok {
        return fmt.Sprintf("handled error: %s (code: %d)", err.Error(), errWithCode.Code())
    }
    return fmt.Sprintf("handled error: %s", err.Error())
}
```

## Performance Considerations

Understanding the performance implications of error handling.

### Error Creation Performance

```go
import "testing"

// Benchmark different error creation methods
func BenchmarkErrorCreation_New(b *testing.B) {
    for i := 0; i < b.N; i++ {
        _ = errors.New("simple error")
    }
}

func BenchmarkErrorCreation_Errorf(b *testing.B) {
    for i := 0; i < b.N; i++ {
        _ = fmt.Errorf("formatted error: %d", i)
    }
}

func BenchmarkErrorCreation_Wrap(b *testing.B) {
    baseErr := errors.New("base error")
    b.ResetTimer()

    for i := 0; i < b.N; i++ {
        _ = fmt.Errorf("wrapped error: %w", baseErr)
    }
}

func BenchmarkErrorCreation_CustomError(b *testing.B) {
    for i := 0; i < b.N; i++ {
        _ = ValidationError{
            Field:   "test",
            Value:   i,
            Message: "test error",
        }
    }
}

// Pre-allocated vs dynamic errors
var preAllocatedError = errors.New("pre-allocated error")

func BenchmarkErrorCreation_PreAllocated(b *testing.B) {
    for i := 0; i < b.N; i++ {
        _ = preAllocatedError
    }
}
```

### Error Handling Performance

```go
// Benchmark error checking patterns
func BenchmarkErrorCheck_Simple(b *testing.B) {
    err := errors.New("test error")

    for i := 0; i < b.N; i++ {
        if err != nil {
            // Handle error
        }
    }
}

func BenchmarkErrorCheck_Is(b *testing.B) {
    targetErr := errors.New("target error")
    wrappedErr := fmt.Errorf("wrapped: %w", targetErr)

    for i := 0; i < b.N; i++ {
        if errors.Is(wrappedErr, targetErr) {
            // Handle error
        }
    }
}

func BenchmarkErrorCheck_As(b *testing.B) {
    customErr := ValidationError{Field: "test", Message: "test"}
    wrappedErr := fmt.Errorf("wrapped: %w", customErr)

    for i := 0; i < b.N; i++ {
        var valErr ValidationError
        if errors.As(wrappedErr, &valErr) {
            // Handle error
        }
    }
}

// Benchmark error vs success paths
func operationWithError() error {
    return errors.New("operation failed")
}

func operationWithoutError() error {
    return nil
}

func BenchmarkOperation_WithError(b *testing.B) {
    for i := 0; i < b.N; i++ {
        err := operationWithError()
        if err != nil {
            // Handle error
        }
    }
}

func BenchmarkOperation_WithoutError(b *testing.B) {
    for i := 0; i < b.N; i++ {
        err := operationWithoutError()
        if err != nil {
            // Handle error
        }
    }
}
```

### Optimization Strategies

```go
// Strategy 1: Use sentinel errors for common cases
var (
    ErrCommonCase1 = errors.New("common case 1")
    ErrCommonCase2 = errors.New("common case 2")
)

func optimizedErrorHandling(input string) error {
    switch input {
    case "case1":
        return ErrCommonCase1 // Reuse pre-allocated error
    case "case2":
        return ErrCommonCase2 // Reuse pre-allocated error
    default:
        return fmt.Errorf("unexpected input: %s", input) // Dynamic error only when needed
    }
}

// Strategy 2: Avoid error wrapping in hot paths
func hotPathFunction(data []byte) error {
    if len(data) == 0 {
        return ErrInvalidInput // Direct return, no wrapping
    }

    // Process data...
    return nil
}

// Strategy 3: Pool error objects for high-frequency operations
var errorPool = sync.Pool{
    New: func() interface{} {
        return &ValidationError{}
    },
}

func getPooledValidationError(field, message string, value interface{}) *ValidationError {
    err := errorPool.Get().(*ValidationError)
    err.Field = field
    err.Message = message
    err.Value = value
    return err
}

func returnPooledValidationError(err *ValidationError) {
    // Reset fields
    err.Field = ""
    err.Message = ""
    err.Value = nil
    errorPool.Put(err)
}

// Strategy 4: Minimize allocations in error paths
func efficientErrorHandling(items []Item) error {
    var errorCount int

    // Count errors first to avoid slice reallocations
    for _, item := range items {
        if !isValid(item) {
            errorCount++
        }
    }

    if errorCount == 0 {
        return nil
    }

    // Pre-allocate slice with known capacity
    errors := make([]string, 0, errorCount)
    for i, item := range items {
        if !isValid(item) {
            errors = append(errors, fmt.Sprintf("item %d is invalid", i))
        }
    }

    return fmt.Errorf("validation failed for %d items: %v", errorCount, errors)
}
```

This comprehensive guide covers all aspects of error handling in Go, from basic patterns to advanced techniques and performance optimization. Understanding these concepts will help you build robust, maintainable applications with excellent error handling.