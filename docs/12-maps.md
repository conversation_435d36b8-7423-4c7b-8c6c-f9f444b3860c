# Maps in Go

This comprehensive guide covers maps in Go, including their internal structure, operations, performance characteristics, and advanced usage patterns.

## Table of Contents

1. [Overview](#overview)
2. [Map Declaration and Initialization](#map-declaration-and-initialization)
3. [Map Operations](#map-operations)
4. [Map Internals](#map-internals)
5. [Key Types and Constraints](#key-types-and-constraints)
6. [Map Iteration](#map-iteration)
7. [Concurrent Access](#concurrent-access)
8. [Performance Considerations](#performance-considerations)
9. [Advanced Patterns](#advanced-patterns)
10. [Memory Management](#memory-management)
11. [Best Practices](#best-practices)
12. [Common Mistakes](#common-mistakes)
13. [Alternatives to Maps](#alternatives-to-maps)

## Overview

Maps are Go's built-in associative data type, similar to hash tables, dictionaries, or associative arrays in other languages. They provide efficient key-value storage with average O(1) lookup time.

### Key Characteristics

```go
// Maps are reference types
var m map[string]int        // nil map
m = make(map[string]int)    // initialized map

// Maps are unordered
// Maps require comparable key types
// Maps are not safe for concurrent access
// Zero value of map is nil
```

### Map vs Other Data Structures

| Feature | Map | Slice | Array |
|---------|-----|-------|-------|
| **Access Time** | O(1) average | O(1) | O(1) |
| **Search Time** | O(1) average | O(n) | O(n) |
| **Insertion** | O(1) average | O(n) worst | N/A |
| **Deletion** | O(1) average | O(n) | N/A |
| **Ordering** | Unordered | Ordered | Ordered |
| **Key Type** | Comparable | Integer | Integer |

## Map Declaration and Initialization

Multiple ways to create and initialize maps in Go.

### Basic Declaration

```go
// Nil map declaration
var m1 map[string]int               // nil map, cannot be written to
var m2 map[int]string               // nil map
var m3 map[string][]int             // nil map with slice values

// Check for nil
if m1 == nil {
    fmt.Println("m1 is nil")       // This will print
}

// Reading from nil map returns zero value
value := m1["key"]                  // Returns 0 (zero value of int)
fmt.Println(value)                  // 0

// Writing to nil map causes panic
// m1["key"] = 42                   // Panic: assignment to entry in nil map
```

### Map Initialization

```go
// Using make function
m1 := make(map[string]int)          // Empty map
m2 := make(map[string]int, 10)      // Empty map with initial capacity hint

// Map literal initialization
m3 := map[string]int{
    "apple":  5,
    "banana": 3,
    "orange": 8,
}

// Empty map literal
m4 := map[string]int{}              // Empty but not nil

// Multi-line initialization
m5 := map[string]int{
    "monday":    1,
    "tuesday":   2,
    "wednesday": 3,
    "thursday":  4,
    "friday":    5,
    "saturday":  6,
    "sunday":    7,
}

// Complex value types
m6 := map[string][]int{
    "evens": {2, 4, 6, 8},
    "odds":  {1, 3, 5, 7},
}

// Nested maps
m7 := map[string]map[string]int{
    "fruits": {
        "apple":  5,
        "banana": 3,
    },
    "vegetables": {
        "carrot": 10,
        "potato": 15,
    },
}
```

### Map with Custom Types

```go
// Custom key types
type UserID int
type ProductCode string

// Map with custom key types
users := map[UserID]string{
    1001: "Alice",
    1002: "Bob",
    1003: "Charlie",
}

products := map[ProductCode]float64{
    "LAPTOP001": 999.99,
    "MOUSE001":  29.99,
    "KEYBOARD001": 79.99,
}

// Struct as value type
type User struct {
    Name  string
    Email string
    Age   int
}

userMap := map[UserID]User{
    1001: {Name: "Alice", Email: "<EMAIL>", Age: 30},
    1002: {Name: "Bob", Email: "<EMAIL>", Age: 25},
}

// Map of functions
operations := map[string]func(int, int) int{
    "add":      func(a, b int) int { return a + b },
    "subtract": func(a, b int) int { return a - b },
    "multiply": func(a, b int) int { return a * b },
    "divide":   func(a, b int) int { return a / b },
}
```

## Map Operations

Comprehensive coverage of map operations and their behaviors.

### Basic Operations

```go
// Create map
m := make(map[string]int)

// Set values
m["apple"] = 5
m["banana"] = 3
m["orange"] = 8

// Get values
value := m["apple"]                 // 5
fmt.Println(value)

// Get with existence check
value, exists := m["apple"]
if exists {
    fmt.Printf("apple: %d\n", value)
} else {
    fmt.Println("apple not found")
}

// Get non-existent key
value = m["grape"]                  // Returns 0 (zero value)
value, exists = m["grape"]          // value=0, exists=false

// Update existing value
m["apple"] = 10                     // Update apple count

// Delete key
delete(m, "banana")                 // Remove banana
delete(m, "nonexistent")            // Safe to delete non-existent key

// Get map length
length := len(m)                    // Number of key-value pairs
fmt.Printf("Map has %d items\n", length)
```

### Advanced Operations

```go
// Check if key exists
func keyExists(m map[string]int, key string) bool {
    _, exists := m[key]
    return exists
}

// Get value with default
func getWithDefault(m map[string]int, key string, defaultValue int) int {
    if value, exists := m[key]; exists {
        return value
    }
    return defaultValue
}

// Increment value (with zero value handling)
func increment(m map[string]int, key string) {
    m[key]++  // Works even if key doesn't exist (starts from 0)
}

// Conditional set (set only if key doesn't exist)
func setIfNotExists(m map[string]int, key string, value int) bool {
    if _, exists := m[key]; !exists {
        m[key] = value
        return true
    }
    return false
}

// Usage examples
m := make(map[string]int)

increment(m, "counter")             // m["counter"] = 1
increment(m, "counter")             // m["counter"] = 2

value := getWithDefault(m, "missing", 42)  // Returns 42

success := setIfNotExists(m, "new", 100)   // Returns true, sets value
success = setIfNotExists(m, "new", 200)    // Returns false, doesn't change value
```

### Bulk Operations

```go
// Copy map
func copyMap(original map[string]int) map[string]int {
    copy := make(map[string]int, len(original))
    for key, value := range original {
        copy[key] = value
    }
    return copy
}

// Merge maps
func mergeMaps(map1, map2 map[string]int) map[string]int {
    result := make(map[string]int)

    // Copy from first map
    for key, value := range map1 {
        result[key] = value
    }

    // Copy from second map (overwrites conflicts)
    for key, value := range map2 {
        result[key] = value
    }

    return result
}

// Filter map
func filterMap(m map[string]int, predicate func(string, int) bool) map[string]int {
    result := make(map[string]int)
    for key, value := range m {
        if predicate(key, value) {
            result[key] = value
        }
    }
    return result
}

// Map transformation
func transformMap(m map[string]int, transformer func(int) int) map[string]int {
    result := make(map[string]int, len(m))
    for key, value := range m {
        result[key] = transformer(value)
    }
    return result
}

// Usage examples
original := map[string]int{"a": 1, "b": 2, "c": 3}
copied := copyMap(original)

map1 := map[string]int{"a": 1, "b": 2}
map2 := map[string]int{"b": 20, "c": 3}
merged := mergeMaps(map1, map2)  // {"a": 1, "b": 20, "c": 3}

// Filter even values
evens := filterMap(original, func(k string, v int) bool {
    return v%2 == 0
})

// Double all values
doubled := transformMap(original, func(v int) int {
    return v * 2
})
```

## Map Internals

Understanding how maps work internally helps write more efficient code.

### Hash Table Implementation

```go
// Conceptual map structure (simplified)
type Map struct {
    buckets    []Bucket    // Array of buckets
    oldbuckets []Bucket    // Used during growth
    nevacuate  uintptr     // Progress counter for evacuation
    count      int         // Number of key-value pairs
    B          uint8       // log_2 of number of buckets
    hash0      uint32      // Hash seed
}

type Bucket struct {
    tophash [8]uint8       // Top 8 bits of hash for each key
    keys    [8]KeyType     // Keys in this bucket
    values  [8]ValueType   // Values in this bucket
    overflow *Bucket       // Pointer to overflow bucket
}
```

### Hash Function and Collision Handling

```go
// Go uses different hash functions for different key types
import "unsafe"

func demonstrateHashing() {
    // String keys use a different hash than integer keys
    m1 := make(map[string]int)
    m2 := make(map[int]string)

    // Hash collisions are handled by chaining (overflow buckets)
    // Go tries to keep load factor around 6.5

    // When map grows, it doubles in size
    // Growth is incremental to avoid long pauses
}

// Hash quality affects performance
func poorHashExample() {
    // Poor hash distribution can cause performance issues
    type BadKey struct {
        id int
    }

    // If BadKey always hashes to same value, all entries
    // would go to same bucket, degrading to O(n) performance
}
```

### Map Growth and Rehashing

```go
// Map growth demonstration
func demonstrateGrowth() {
    m := make(map[int]int)

    // Monitor map growth
    for i := 0; i < 100; i++ {
        m[i] = i

        // Map grows when load factor exceeds threshold
        if i%10 == 0 {
            fmt.Printf("After %d insertions, len=%d\n", i, len(m))
        }
    }
}

// Growth is incremental
func incrementalGrowth() {
    // When map needs to grow:
    // 1. Allocate new bucket array (double size)
    // 2. Start incremental evacuation
    // 3. Each map operation evacuates some old buckets
    // 4. Eventually all data is moved to new buckets

    // This prevents long pauses during growth
}
```

## Key Types and Constraints

Understanding what types can be used as map keys.

### Comparable Types (Valid Keys)

```go
// Basic types (all comparable)
var m1 map[bool]int
var m2 map[int]string
var m3 map[int8]string
var m4 map[int16]string
var m5 map[int32]string
var m6 map[int64]string
var m7 map[uint]string
var m8 map[uint8]string
var m9 map[uint16]string
var m10 map[uint32]string
var m11 map[uint64]string
var m12 map[uintptr]string
var m13 map[float32]string
var m14 map[float64]string
var m15 map[complex64]string
var m16 map[complex128]string
var m17 map[string]int

// Pointer types
var m18 map[*int]string

// Channel types
var m19 map[chan int]string

// Interface types
var m20 map[interface{}]string

// Array types (if element type is comparable)
var m21 map[[3]int]string
var m22 map[[2]string]int

// Struct types (if all fields are comparable)
type Person struct {
    Name string
    Age  int
}
var m23 map[Person]string
```

### Non-Comparable Types (Invalid Keys)

```go
// These types CANNOT be used as map keys:

// Slices
// var m1 map[[]int]string     // Error: invalid map key type []int

// Maps
// var m2 map[map[string]int]string  // Error: invalid map key type map[string]int

// Functions
// var m3 map[func()]string    // Error: invalid map key type func()

// Structs with non-comparable fields
type BadStruct struct {
    Name string
    Tags []string  // Slice makes struct non-comparable
}
// var m4 map[BadStruct]string  // Error: invalid map key type BadStruct
```

### Custom Key Types and Best Practices

```go
// Good: Use meaningful key types
type UserID int
type ProductCode string
type SessionToken string

var users map[UserID]User
var products map[ProductCode]Product
var sessions map[SessionToken]Session

// Good: Composite keys using structs
type CacheKey struct {
    UserID    int
    Timestamp int64
}

var cache map[CacheKey]CachedData

// Good: String keys for flexibility
type ConfigKey string
const (
    DatabaseURL ConfigKey = "database_url"
    APIKey      ConfigKey = "api_key"
    Debug       ConfigKey = "debug"
)

var config map[ConfigKey]string

// Avoid: Float keys (precision issues)
func floatKeyProblems() {
    m := make(map[float64]string)

    m[0.1+0.2] = "sum"
    m[0.3] = "direct"

    // These might not be equal due to floating-point precision
    fmt.Println(0.1+0.2 == 0.3)  // Might be false

    // Better: Use integer keys or string representations
    m2 := make(map[string]string)
    m2["0.3"] = "value"
}
```

## Map Iteration

Maps are unordered, and iteration order is not guaranteed.

### Basic Iteration

```go
m := map[string]int{
    "apple":  5,
    "banana": 3,
    "orange": 8,
}

// Iterate over key-value pairs
for key, value := range m {
    fmt.Printf("%s: %d\n", key, value)
}

// Iterate over keys only
for key := range m {
    fmt.Printf("Key: %s\n", key)
}

// Iterate over values only
for _, value := range m {
    fmt.Printf("Value: %d\n", value)
}
```

### Iteration Order

```go
// Map iteration order is randomized
func demonstrateRandomOrder() {
    m := map[string]int{
        "a": 1, "b": 2, "c": 3, "d": 4, "e": 5,
    }

    fmt.Println("First iteration:")
    for k, v := range m {
        fmt.Printf("%s:%d ", k, v)
    }
    fmt.Println()

    fmt.Println("Second iteration:")
    for k, v := range m {
        fmt.Printf("%s:%d ", k, v)
    }
    fmt.Println()

    // Order will likely be different between runs
}

// If you need consistent order, collect and sort keys
func orderedIteration(m map[string]int) {
    // Collect keys
    keys := make([]string, 0, len(m))
    for key := range m {
        keys = append(keys, key)
    }

    // Sort keys
    sort.Strings(keys)

    // Iterate in sorted order
    for _, key := range keys {
        fmt.Printf("%s: %d\n", key, m[key])
    }
}
```

### Safe Iteration Patterns

```go
// Safe modification during iteration
func safeModification() {
    m := map[string]int{
        "a": 1, "b": 2, "c": 3, "d": 4, "e": 5,
    }

    // Collect keys to delete
    var toDelete []string
    for key, value := range m {
        if value%2 == 0 {
            toDelete = append(toDelete, key)
        }
    }

    // Delete after iteration
    for _, key := range toDelete {
        delete(m, key)
    }
}

// Adding during iteration (safe)
func safeAddition() {
    m := map[string]int{"a": 1, "b": 2}

    // This is safe - new entries won't appear in current iteration
    for key, value := range m {
        if value < 5 {
            m[key+"_copy"] = value  // Safe to add
        }
    }
}

// Unsafe: modifying same key during iteration
func unsafeModification() {
    m := map[string]int{"a": 1, "b": 2}

    for key, value := range m {
        m[key] = value * 2  // This is actually safe
        // delete(m, key)   // This would be problematic
    }
}
```

### Iteration Performance

```go
import "testing"

// Benchmark map iteration
func BenchmarkMapIteration(b *testing.B) {
    m := make(map[int]int, 1000)
    for i := 0; i < 1000; i++ {
        m[i] = i
    }

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        sum := 0
        for _, v := range m {
            sum += v
        }
    }
}

// Large maps: iteration can be expensive
func iterationCost() {
    // For large maps, consider:
    // 1. Avoiding frequent full iterations
    // 2. Using separate data structures for ordered access
    // 3. Caching iteration results if needed

    largeMap := make(map[int]int, 1000000)
    // ... populate map

    // Expensive: full iteration
    sum := 0
    for _, v := range largeMap {
        sum += v
    }

    // Better: maintain running total
    type MapWithSum struct {
        data map[int]int
        sum  int
    }

    // Update sum when modifying map
}
```

## Concurrent Access

Maps are not safe for concurrent access and require synchronization.

### The Problem with Concurrent Access

```go
// Dangerous: concurrent map access
func dangerousConcurrentAccess() {
    m := make(map[int]int)

    // This will cause a race condition and likely panic
    go func() {
        for i := 0; i < 1000; i++ {
            m[i] = i  // Writing
        }
    }()

    go func() {
        for i := 0; i < 1000; i++ {
            _ = m[i]  // Reading
        }
    }()

    // Fatal error: concurrent map writes
}
```

### Synchronization Solutions

```go
import "sync"

// Solution 1: Mutex protection
type SafeMap struct {
    mu sync.RWMutex
    m  map[string]int
}

func NewSafeMap() *SafeMap {
    return &SafeMap{
        m: make(map[string]int),
    }
}

func (sm *SafeMap) Set(key string, value int) {
    sm.mu.Lock()
    defer sm.mu.Unlock()
    sm.m[key] = value
}

func (sm *SafeMap) Get(key string) (int, bool) {
    sm.mu.RLock()
    defer sm.mu.RUnlock()
    value, exists := sm.m[key]
    return value, exists
}

func (sm *SafeMap) Delete(key string) {
    sm.mu.Lock()
    defer sm.mu.Unlock()
    delete(sm.m, key)
}

func (sm *SafeMap) Len() int {
    sm.mu.RLock()
    defer sm.mu.RUnlock()
    return len(sm.m)
}

// Solution 2: sync.Map (for specific use cases)
func usingSyncMap() {
    var m sync.Map

    // Store
    m.Store("key1", "value1")
    m.Store("key2", "value2")

    // Load
    if value, ok := m.Load("key1"); ok {
        fmt.Printf("Found: %v\n", value)
    }

    // LoadOrStore
    actual, loaded := m.LoadOrStore("key3", "value3")
    if loaded {
        fmt.Printf("Key existed with value: %v\n", actual)
    } else {
        fmt.Printf("Key was stored with value: %v\n", actual)
    }

    // Delete
    m.Delete("key1")

    // Range (iteration)
    m.Range(func(key, value interface{}) bool {
        fmt.Printf("%v: %v\n", key, value)
        return true  // Continue iteration
    })
}

// Solution 3: Channel-based map server
type MapServer struct {
    requests chan MapRequest
    data     map[string]int
}

type MapRequest struct {
    operation string
    key       string
    value     int
    response  chan MapResponse
}

type MapResponse struct {
    value  int
    exists bool
}

func NewMapServer() *MapServer {
    ms := &MapServer{
        requests: make(chan MapRequest),
        data:     make(map[string]int),
    }
    go ms.serve()
    return ms
}

func (ms *MapServer) serve() {
    for req := range ms.requests {
        switch req.operation {
        case "get":
            value, exists := ms.data[req.key]
            req.response <- MapResponse{value: value, exists: exists}
        case "set":
            ms.data[req.key] = req.value
            req.response <- MapResponse{}
        case "delete":
            delete(ms.data, req.key)
            req.response <- MapResponse{}
        }
    }
}

func (ms *MapServer) Get(key string) (int, bool) {
    response := make(chan MapResponse)
    ms.requests <- MapRequest{
        operation: "get",
        key:       key,
        response:  response,
    }
    resp := <-response
    return resp.value, resp.exists
}
```

### When to Use Each Approach

```go
// Use regular map + mutex when:
// - Read/write ratio is balanced
// - You need full map operations
// - Performance is not critical

// Use sync.Map when:
// - Mostly reads with occasional writes
// - Keys are stable (not frequently added/removed)
// - You can work with interface{} types

// Use channel-based approach when:
// - You want to avoid locks
// - You can batch operations
// - You need complex atomic operations
```

## Performance Considerations

Understanding map performance characteristics and optimization techniques.

### Time Complexity

```go
// Map operations time complexity:
// - Access: O(1) average, O(n) worst case
// - Insert: O(1) average, O(n) worst case (during resize)
// - Delete: O(1) average, O(n) worst case
// - Iteration: O(n)

// Factors affecting performance:
// 1. Hash function quality
// 2. Load factor
// 3. Key distribution
// 4. Map size
```

### Benchmarking Map Operations

```go
import "testing"

func BenchmarkMapAccess(b *testing.B) {
    m := make(map[int]int, 1000)
    for i := 0; i < 1000; i++ {
        m[i] = i
    }

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        _ = m[i%1000]
    }
}

func BenchmarkMapInsert(b *testing.B) {
    m := make(map[int]int)

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        m[i] = i
    }
}

func BenchmarkMapDelete(b *testing.B) {
    m := make(map[int]int, b.N)
    for i := 0; i < b.N; i++ {
        m[i] = i
    }

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        delete(m, i)
    }
}

// Compare different key types
func BenchmarkStringKeys(b *testing.B) {
    m := make(map[string]int)
    keys := make([]string, 1000)
    for i := 0; i < 1000; i++ {
        keys[i] = fmt.Sprintf("key%d", i)
        m[keys[i]] = i
    }

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        _ = m[keys[i%1000]]
    }
}

func BenchmarkIntKeys(b *testing.B) {
    m := make(map[int]int, 1000)
    for i := 0; i < 1000; i++ {
        m[i] = i
    }

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        _ = m[i%1000]
    }
}
```

### Optimization Techniques

```go
// 1. Pre-allocate maps when size is known
func optimizedMapCreation(expectedSize int) map[string]int {
    // Good: pre-allocate
    return make(map[string]int, expectedSize)

    // Avoid: letting map grow incrementally
    // return make(map[string]int)
}

// 2. Choose efficient key types
func efficientKeys() {
    // Fast: integer keys
    m1 := make(map[int]string)

    // Slower: string keys (but still fast)
    m2 := make(map[string]int)

    // Avoid: large struct keys
    type LargeKey struct {
        field1, field2, field3, field4 string
        field5, field6, field7, field8 int
    }
    // m3 := make(map[LargeKey]int)  // Expensive to hash

    // Better: use pointer or smaller key
    m4 := make(map[*LargeKey]int)  // Pointer is fast to hash
}

// 3. Batch operations when possible
func batchOperations(data []KeyValue) {
    m := make(map[string]int, len(data))

    // Good: batch insert
    for _, kv := range data {
        m[kv.Key] = kv.Value
    }

    // Avoid: individual operations with locks in concurrent code
}

// 4. Reuse maps when possible
func reuseMap() {
    m := make(map[string]int, 100)

    for iteration := 0; iteration < 10; iteration++ {
        // Clear map for reuse
        for k := range m {
            delete(m, k)
        }

        // Or use a helper function
        clearMap(m)

        // Reuse the map
        populateMap(m)
    }
}

func clearMap(m map[string]int) {
    for k := range m {
        delete(m, k)
    }
}

// 5. Consider alternatives for specific use cases
func considerAlternatives() {
    // For small, fixed sets: use slice + linear search
    type SmallSet []string

    func (s SmallSet) Contains(item string) bool {
        for _, v := range s {
            if v == item {
                return true
            }
        }
        return false
    }

    // For ordered data: use slice + binary search
    // For frequent iteration: maintain separate slice of keys
}
```

## Advanced Patterns

Sophisticated patterns for working with maps.

### Map of Slices Pattern

```go
// Grouping items by category
func groupBy() {
    type Person struct {
        Name string
        Age  int
        City string
    }

    people := []Person{
        {"Alice", 30, "New York"},
        {"Bob", 25, "New York"},
        {"Charlie", 35, "London"},
        {"David", 28, "London"},
    }

    // Group by city
    cityGroups := make(map[string][]Person)
    for _, person := range people {
        cityGroups[person.City] = append(cityGroups[person.City], person)
    }

    // Result: map[string][]Person{
    //   "New York": {{"Alice", 30, "New York"}, {"Bob", 25, "New York"}},
    //   "London":   {{"Charlie", 35, "London"}, {"David", 28, "London"}},
    // }
}

// Generic groupBy function
func GroupBy[T any, K comparable](items []T, keyFunc func(T) K) map[K][]T {
    result := make(map[K][]T)
    for _, item := range items {
        key := keyFunc(item)
        result[key] = append(result[key], item)
    }
    return result
}
```

### Set Implementation Using Maps

```go
// Set using map[T]bool
type Set[T comparable] map[T]bool

func NewSet[T comparable]() Set[T] {
    return make(Set[T])
}

func (s Set[T]) Add(item T) {
    s[item] = true
}

func (s Set[T]) Remove(item T) {
    delete(s, item)
}

func (s Set[T]) Contains(item T) bool {
    return s[item]
}

func (s Set[T]) Size() int {
    return len(s)
}

func (s Set[T]) Items() []T {
    items := make([]T, 0, len(s))
    for item := range s {
        items = append(items, item)
    }
    return items
}

func (s Set[T]) Union(other Set[T]) Set[T] {
    result := NewSet[T]()
    for item := range s {
        result.Add(item)
    }
    for item := range other {
        result.Add(item)
    }
    return result
}

func (s Set[T]) Intersection(other Set[T]) Set[T] {
    result := NewSet[T]()
    for item := range s {
        if other.Contains(item) {
            result.Add(item)
        }
    }
    return result
}

// Alternative: Set using map[T]struct{} (more memory efficient)
type StructSet[T comparable] map[T]struct{}

func (s StructSet[T]) Add(item T) {
    s[item] = struct{}{}
}

func (s StructSet[T]) Contains(item T) bool {
    _, exists := s[item]
    return exists
}
```

### Cache Implementation

```go
import "time"

// Simple cache with expiration
type CacheItem struct {
    Value      interface{}
    Expiration time.Time
}

type Cache struct {
    items map[string]CacheItem
    mu    sync.RWMutex
}

func NewCache() *Cache {
    c := &Cache{
        items: make(map[string]CacheItem),
    }

    // Start cleanup goroutine
    go c.cleanup()

    return c
}

func (c *Cache) Set(key string, value interface{}, duration time.Duration) {
    c.mu.Lock()
    defer c.mu.Unlock()

    c.items[key] = CacheItem{
        Value:      value,
        Expiration: time.Now().Add(duration),
    }
}

func (c *Cache) Get(key string) (interface{}, bool) {
    c.mu.RLock()
    defer c.mu.RUnlock()

    item, exists := c.items[key]
    if !exists {
        return nil, false
    }

    if time.Now().After(item.Expiration) {
        return nil, false
    }

    return item.Value, true
}

func (c *Cache) cleanup() {
    ticker := time.NewTicker(time.Minute)
    defer ticker.Stop()

    for range ticker.C {
        c.mu.Lock()
        now := time.Now()
        for key, item := range c.items {
            if now.After(item.Expiration) {
                delete(c.items, key)
            }
        }
        c.mu.Unlock()
    }
}
```

## Memory Management

Understanding memory usage and optimization for maps.

### Map Memory Layout

```go
import "unsafe"

// Map memory usage
func mapMemoryUsage() {
    // Empty map
    m1 := make(map[string]int)
    fmt.Printf("Empty map size: %d bytes\n", unsafe.Sizeof(m1))  // ~8 bytes (pointer)

    // Map with data
    m2 := make(map[string]int)
    for i := 0; i < 1000; i++ {
        m2[fmt.Sprintf("key%d", i)] = i
    }

    // Map memory grows with buckets, not just entries
    // Each bucket can hold 8 key-value pairs
    // Memory usage = buckets * bucket_size + key/value storage
}

// Memory overhead considerations
func memoryOverhead() {
    // Map overhead:
    // - Map header: ~48 bytes
    // - Bucket array: 8 * num_buckets bytes (pointers)
    // - Buckets: ~208 bytes each (for string/int map)
    // - Key/value storage: depends on types

    // For small maps, overhead can be significant
    smallMap := make(map[string]int)
    smallMap["key"] = 1
    // Overhead might be 200+ bytes for 1 key-value pair

    // For large maps, overhead becomes negligible
    largeMap := make(map[string]int, 10000)
    // Overhead is small percentage of total memory
}
```

### Memory Leaks and Prevention

```go
// Memory leak: map keeps growing
func memoryLeakExample() {
    cache := make(map[string][]byte)

    for {
        key := generateKey()
        data := make([]byte, 1024*1024)  // 1MB
        cache[key] = data

        // Problem: cache never shrinks, memory keeps growing
        // Solution: implement eviction policy
    }
}

// Fixed: LRU cache with size limit
type LRUCache struct {
    capacity int
    items    map[string]*Item
    order    *list.List
    mu       sync.Mutex
}

type Item struct {
    key   string
    value interface{}
    node  *list.Element
}

func NewLRUCache(capacity int) *LRUCache {
    return &LRUCache{
        capacity: capacity,
        items:    make(map[string]*Item),
        order:    list.New(),
    }
}

func (c *LRUCache) Get(key string) (interface{}, bool) {
    c.mu.Lock()
    defer c.mu.Unlock()

    if item, exists := c.items[key]; exists {
        c.order.MoveToFront(item.node)
        return item.value, true
    }
    return nil, false
}

func (c *LRUCache) Set(key string, value interface{}) {
    c.mu.Lock()
    defer c.mu.Unlock()

    if item, exists := c.items[key]; exists {
        item.value = value
        c.order.MoveToFront(item.node)
        return
    }

    // Add new item
    item := &Item{key: key, value: value}
    item.node = c.order.PushFront(item)
    c.items[key] = item

    // Evict if over capacity
    if len(c.items) > c.capacity {
        oldest := c.order.Back()
        if oldest != nil {
            c.order.Remove(oldest)
            delete(c.items, oldest.Value.(*Item).key)
        }
    }
}

// Memory leak: large keys/values
func avoidLargeKeyValues() {
    // Problem: storing large objects directly
    type LargeObject struct {
        data [1024 * 1024]byte  // 1MB
    }

    badMap := make(map[string]LargeObject)

    // Better: store pointers or references
    goodMap := make(map[string]*LargeObject)

    // Or use indirection
    type ObjectID int
    objects := make(map[ObjectID]*LargeObject)
    index := make(map[string]ObjectID)
}
```

## Best Practices

Guidelines for effective map usage.

### Map Creation and Initialization

```go
// 1. Pre-allocate when size is known
func goodMapCreation() {
    // Good: pre-allocate capacity
    expectedSize := 1000
    m := make(map[string]int, expectedSize)

    // Avoid: letting map grow from zero
    // m := make(map[string]int)
}

// 2. Use meaningful key types
func meaningfulKeys() {
    // Good: custom types for clarity
    type UserID int
    type SessionID string

    users := make(map[UserID]User)
    sessions := make(map[SessionID]Session)

    // Avoid: generic types when specific types are better
    // users := make(map[int]User)
    // sessions := make(map[string]Session)
}

// 3. Initialize with literals when appropriate
func literalInitialization() {
    // Good: for known, static data
    statusCodes := map[int]string{
        200: "OK",
        404: "Not Found",
        500: "Internal Server Error",
    }

    // Good: for configuration
    config := map[string]interface{}{
        "timeout":    30,
        "retries":    3,
        "debug":      false,
        "endpoints": []string{"api1", "api2"},
    }
}
```

### Error Handling and Safety

```go
// 1. Always check for existence when needed
func safeMapAccess() {
    m := make(map[string]int)

    // Good: check existence
    if value, exists := m["key"]; exists {
        fmt.Printf("Found: %d\n", value)
    } else {
        fmt.Println("Key not found")
    }

    // Avoid: assuming key exists
    // value := m["key"]  // Returns zero value if not found
}

// 2. Handle nil maps safely
func handleNilMaps(m map[string]int) {
    // Good: check for nil
    if m == nil {
        return
    }

    // Safe to use map
    for k, v := range m {
        fmt.Printf("%s: %d\n", k, v)
    }
}

// 3. Use helper functions for complex operations
func mapHelpers() {
    // Helper for safe increment
    func increment(m map[string]int, key string) {
        m[key]++  // Safe: zero value is 0
    }

    // Helper for conditional set
    func setIfNotExists(m map[string]int, key string, value int) bool {
        if _, exists := m[key]; !exists {
            m[key] = value
            return true
        }
        return false
    }

    // Helper for get with default
    func getWithDefault(m map[string]int, key string, defaultValue int) int {
        if value, exists := m[key]; exists {
            return value
        }
        return defaultValue
    }
}
```

### Performance Best Practices

```go
// 1. Choose appropriate key types
func efficientKeyTypes() {
    // Fast: integer keys
    intMap := make(map[int]string)

    // Fast: string keys (for reasonable lengths)
    stringMap := make(map[string]int)

    // Slower: large struct keys
    type LargeKey struct {
        field1, field2, field3 string
        field4, field5, field6 int
    }
    // Avoid: largeMap := make(map[LargeKey]int)

    // Better: use pointer or hash
    pointerMap := make(map[*LargeKey]int)
    hashMap := make(map[string]int)  // Use hash of struct as key
}

// 2. Batch operations when possible
func batchOperations() {
    m := make(map[string]int, 1000)

    // Good: batch inserts
    data := []struct{ key string; value int }{
        {"a", 1}, {"b", 2}, {"c", 3},
    }

    for _, item := range data {
        m[item.key] = item.value
    }

    // Avoid: individual operations with overhead
}

// 3. Reuse maps when possible
func reuseMapPattern() {
    // Reusable map for temporary operations
    tempMap := make(map[string]int, 100)

    for iteration := 0; iteration < 10; iteration++ {
        // Clear previous data
        for k := range tempMap {
            delete(tempMap, k)
        }

        // Reuse for new data
        populateMap(tempMap)
        processMap(tempMap)
    }
}
```

## Common Mistakes

Avoiding common pitfalls when working with maps.

### Mistake 1: Concurrent Access

```go
// Wrong: concurrent access without synchronization
func concurrentMistake() {
    m := make(map[string]int)

    // This will cause race conditions
    go func() {
        for i := 0; i < 1000; i++ {
            m[fmt.Sprintf("key%d", i)] = i
        }
    }()

    go func() {
        for i := 0; i < 1000; i++ {
            _ = m[fmt.Sprintf("key%d", i)]
        }
    }()

    // Fatal error: concurrent map read and map write
}

// Correct: use synchronization
func concurrentCorrect() {
    m := make(map[string]int)
    var mu sync.RWMutex

    go func() {
        for i := 0; i < 1000; i++ {
            mu.Lock()
            m[fmt.Sprintf("key%d", i)] = i
            mu.Unlock()
        }
    }()

    go func() {
        for i := 0; i < 1000; i++ {
            mu.RLock()
            _ = m[fmt.Sprintf("key%d", i)]
            mu.RUnlock()
        }
    }()
}
```

### Mistake 2: Modifying Map During Iteration

```go
// Wrong: deleting during iteration
func iterationMistake() {
    m := map[string]int{
        "a": 1, "b": 2, "c": 3, "d": 4,
    }

    // This can cause unpredictable behavior
    for key, value := range m {
        if value%2 == 0 {
            delete(m, key)  // Dangerous during iteration
        }
    }
}

// Correct: collect keys first
func iterationCorrect() {
    m := map[string]int{
        "a": 1, "b": 2, "c": 3, "d": 4,
    }

    var toDelete []string
    for key, value := range m {
        if value%2 == 0 {
            toDelete = append(toDelete, key)
        }
    }

    for _, key := range toDelete {
        delete(m, key)
    }
}
```

### Mistake 3: Assuming Map Ordering

```go
// Wrong: assuming iteration order
func orderingMistake() {
    m := map[string]int{
        "first":  1,
        "second": 2,
        "third":  3,
    }

    // Wrong: expecting specific order
    var keys []string
    for key := range m {
        keys = append(keys, key)
    }
    // keys order is not guaranteed!
}

// Correct: sort when order matters
func orderingCorrect() {
    m := map[string]int{
        "first":  1,
        "second": 2,
        "third":  3,
    }

    // Collect and sort keys
    keys := make([]string, 0, len(m))
    for key := range m {
        keys = append(keys, key)
    }
    sort.Strings(keys)

    // Now iterate in sorted order
    for _, key := range keys {
        fmt.Printf("%s: %d\n", key, m[key])
    }
}
```

### Mistake 4: Memory Leaks

```go
// Wrong: unbounded map growth
func memoryLeakMistake() {
    cache := make(map[string][]byte)

    for {
        key := generateKey()
        data := generateData()
        cache[key] = data
        // Map grows forever!
    }
}

// Correct: implement eviction
func memoryLeakCorrect() {
    const maxSize = 1000
    cache := make(map[string][]byte)
    keys := make([]string, 0, maxSize)

    for {
        key := generateKey()
        data := generateData()

        // Add new entry
        cache[key] = data
        keys = append(keys, key)

        // Evict oldest if over limit
        if len(keys) > maxSize {
            oldestKey := keys[0]
            delete(cache, oldestKey)
            keys = keys[1:]
        }
    }
}
```

## Alternatives to Maps

When to consider other data structures instead of maps.

### Small Fixed Sets

```go
// For small, known sets, slices might be better
type Permission string

const (
    Read   Permission = "read"
    Write  Permission = "write"
    Delete Permission = "delete"
)

// Instead of map[Permission]bool
func hasPermissionMap(userPerms map[Permission]bool, perm Permission) bool {
    return userPerms[perm]
}

// Use slice for small sets
func hasPermissionSlice(userPerms []Permission, perm Permission) bool {
    for _, p := range userPerms {
        if p == perm {
            return true
        }
    }
    return false
}

// Benchmark shows slices are faster for small sets (< 10 items)
```

### Ordered Data

```go
// For ordered key-value pairs, consider other structures
import "sort"

// Sorted slice of pairs
type KeyValue struct {
    Key   string
    Value int
}

type SortedMap []KeyValue

func (sm SortedMap) Get(key string) (int, bool) {
    i := sort.Search(len(sm), func(i int) bool {
        return sm[i].Key >= key
    })

    if i < len(sm) && sm[i].Key == key {
        return sm[i].Value, true
    }
    return 0, false
}

func (sm *SortedMap) Set(key string, value int) {
    i := sort.Search(len(*sm), func(i int) bool {
        return (*sm)[i].Key >= key
    })

    if i < len(*sm) && (*sm)[i].Key == key {
        (*sm)[i].Value = value
        return
    }

    // Insert at position i
    *sm = append(*sm, KeyValue{})
    copy((*sm)[i+1:], (*sm)[i:])
    (*sm)[i] = KeyValue{Key: key, Value: value}
}
```

### Specialized Data Structures

```go
// For specific use cases, consider specialized structures

// 1. Trie for string prefixes
type Trie struct {
    children map[rune]*Trie
    isEnd    bool
    value    interface{}
}

// 2. Bloom filter for membership testing
type BloomFilter struct {
    bits []bool
    hash []func(string) uint
}

// 3. Consistent hashing for distributed systems
type ConsistentHash struct {
    ring map[uint32]string
    keys []uint32
}

// 4. LRU cache for bounded caches
// (Already shown in previous examples)

// Choose the right data structure for your use case:
// - Maps: general-purpose key-value storage
// - Slices: small sets, ordered data
// - Tries: prefix matching
// - Bloom filters: membership testing with false positives OK
// - Specialized structures: specific algorithms
```

This comprehensive guide covers all aspects of maps in Go, from basic usage to advanced patterns and performance optimization. Understanding these concepts will help you use maps effectively in your Go applications.