# Control Structures in Go

This comprehensive guide covers all control structures in Go, including conditional statements, loops, and switch statements with their various forms and best practices.

## Table of Contents

1. [Overview](#overview)
2. [If Statements](#if-statements)
3. [Switch Statements](#switch-statements)
4. [For Loops](#for-loops)
5. [Range Loops](#range-loops)
6. [Break and Continue](#break-and-continue)
7. [Goto Statement](#goto-statement)
8. [Defer Statement](#defer-statement)
9. [Select Statement](#select-statement)
10. [Best Practices](#best-practices)
11. [Common Patterns](#common-patterns)
12. [Performance Considerations](#performance-considerations)

## Overview

Go has a simple and clean set of control structures that provide all the necessary flow control mechanisms while maintaining readability and simplicity.

### Control Structure Categories

```go
// Conditional statements
if condition { }
if condition { } else { }
switch value { }

// Loops
for condition { }
for i := 0; i < n; i++ { }
for range collection { }

// Jump statements
break
continue
goto label
return

// Special statements
defer function()
select { case <-ch: }
```

### Key Characteristics

- **No parentheses required** around conditions
- **Braces are mandatory** for all blocks
- **Short variable declarations** allowed in if and switch
- **Type switches** for interface types
- **Range loops** for collections
- **Select statement** for channel operations

## If Statements

The if statement is the most basic conditional control structure in Go.

### Basic If Statement

```go
// Simple if statement
if x > 0 {
    fmt.Println("x is positive")
}

// If-else statement
if x > 0 {
    fmt.Println("x is positive")
} else {
    fmt.Println("x is not positive")
}

// If-else if-else chain
if x > 0 {
    fmt.Println("x is positive")
} else if x < 0 {
    fmt.Println("x is negative")
} else {
    fmt.Println("x is zero")
}
```

### If with Short Variable Declaration

```go
// Declare and use variable in if statement
if err := someFunction(); err != nil {
    fmt.Printf("Error: %v\n", err)
    return err
}

// Variable scope is limited to if block
if value := getValue(); value > threshold {
    fmt.Printf("Value %d exceeds threshold\n", value)
} else {
    fmt.Printf("Value %d is within threshold\n", value)
}
// value is not accessible here

// Multiple variable declarations
if a, b := getValues(); a > b {
    fmt.Printf("%d is greater than %d\n", a, b)
}
```

### Complex Conditions

```go
// Logical operators
if x > 0 && y > 0 {
    fmt.Println("Both x and y are positive")
}

if x > 0 || y > 0 {
    fmt.Println("At least one of x or y is positive")
}

if !(x > 0) {
    fmt.Println("x is not positive")
}

// Parentheses for clarity
if (x > 0 && y > 0) || (x < 0 && y < 0) {
    fmt.Println("x and y have the same sign")
}

// Function calls in conditions
if isValid(input) && hasPermission(user) {
    processInput(input)
}
```

### If Statement Patterns

```go
// Error handling pattern
func processFile(filename string) error {
    file, err := os.Open(filename)
    if err != nil {
        return fmt.Errorf("failed to open file: %w", err)
    }
    defer file.Close()

    // Process file
    return nil
}

// Early return pattern
func validateUser(user *User) error {
    if user == nil {
        return errors.New("user cannot be nil")
    }

    if user.Name == "" {
        return errors.New("user name cannot be empty")
    }

    if user.Age < 0 {
        return errors.New("user age cannot be negative")
    }

    return nil
}

// Guard clause pattern
func divide(a, b float64) (float64, error) {
    if b == 0 {
        return 0, errors.New("division by zero")
    }

    return a / b, nil
}
```

## Switch Statements

Switch statements provide a clean way to handle multiple conditions.

### Basic Switch Statement

```go
// Basic switch
switch day {
case "Monday":
    fmt.Println("Start of work week")
case "Friday":
    fmt.Println("TGIF!")
case "Saturday", "Sunday":
    fmt.Println("Weekend!")
default:
    fmt.Println("Regular day")
}

// Switch with expression
switch {
case score >= 90:
    grade = "A"
case score >= 80:
    grade = "B"
case score >= 70:
    grade = "C"
case score >= 60:
    grade = "D"
default:
    grade = "F"
}
```

### Switch with Short Variable Declaration

```go
// Switch with variable declaration
switch err := doSomething(); {
case err == nil:
    fmt.Println("Success")
case errors.Is(err, ErrNotFound):
    fmt.Println("Not found")
case errors.Is(err, ErrPermission):
    fmt.Println("Permission denied")
default:
    fmt.Printf("Unknown error: %v\n", err)
}

// Switch on function result
switch result := calculate(); result {
case 0:
    fmt.Println("Zero result")
case 1, 2, 3:
    fmt.Println("Small positive result")
default:
    fmt.Printf("Result: %d\n", result)
}
```

### Type Switch

```go
// Type switch for interface{}
func processValue(value interface{}) {
    switch v := value.(type) {
    case int:
        fmt.Printf("Integer: %d\n", v)
    case string:
        fmt.Printf("String: %s\n", v)
    case bool:
        fmt.Printf("Boolean: %t\n", v)
    case []int:
        fmt.Printf("Slice of ints: %v\n", v)
    case nil:
        fmt.Println("Nil value")
    default:
        fmt.Printf("Unknown type: %T\n", v)
    }
}

// Type switch with multiple types
func handleValue(value interface{}) {
    switch value.(type) {
    case int, int8, int16, int32, int64:
        fmt.Println("Integer type")
    case uint, uint8, uint16, uint32, uint64:
        fmt.Println("Unsigned integer type")
    case float32, float64:
        fmt.Println("Floating point type")
    case string:
        fmt.Println("String type")
    default:
        fmt.Println("Other type")
    }
}
```

### Fallthrough in Switch

```go
// Fallthrough keyword (rarely used)
func checkGrade(grade string) {
    switch grade {
    case "A":
        fmt.Println("Excellent!")
        fallthrough
    case "B":
        fmt.Println("Good job!")
        fallthrough
    case "C":
        fmt.Println("Passing grade")
    case "D":
        fmt.Println("Needs improvement")
    case "F":
        fmt.Println("Failed")
    default:
        fmt.Println("Invalid grade")
    }
}

// Better approach without fallthrough
func checkGradeImproved(grade string) {
    messages := []string{}

    switch grade {
    case "A":
        messages = append(messages, "Excellent!")
        messages = append(messages, "Good job!")
        messages = append(messages, "Passing grade")
    case "B":
        messages = append(messages, "Good job!")
        messages = append(messages, "Passing grade")
    case "C":
        messages = append(messages, "Passing grade")
    case "D":
        messages = append(messages, "Needs improvement")
    case "F":
        messages = append(messages, "Failed")
    default:
        messages = append(messages, "Invalid grade")
    }

    for _, msg := range messages {
        fmt.Println(msg)
    }
}
```

## For Loops

Go has only one loop construct: the for loop, but it can be used in several different forms.

### Basic For Loop

```go
// Traditional C-style for loop
for i := 0; i < 10; i++ {
    fmt.Printf("%d ", i)
}

// Multiple variables
for i, j := 0, 10; i < j; i, j = i+1, j-1 {
    fmt.Printf("i=%d, j=%d\n", i, j)
}

// Different increment patterns
for i := 0; i < 100; i += 10 {
    fmt.Printf("%d ", i)
}

for i := 10; i > 0; i-- {
    fmt.Printf("%d ", i)
}
```

### While-style For Loop

```go
// While loop equivalent
i := 0
for i < 10 {
    fmt.Printf("%d ", i)
    i++
}

// Infinite loop
for {
    // Do something forever
    if shouldBreak() {
        break
    }
}

// Reading until condition
scanner := bufio.NewScanner(os.Stdin)
for scanner.Scan() {
    line := scanner.Text()
    if line == "quit" {
        break
    }
    fmt.Println("You said:", line)
}
```

### Nested For Loops

```go
// Nested loops
for i := 0; i < 3; i++ {
    for j := 0; j < 3; j++ {
        fmt.Printf("(%d,%d) ", i, j)
    }
    fmt.Println()
}

// Matrix operations
matrix := [][]int{
    {1, 2, 3},
    {4, 5, 6},
    {7, 8, 9},
}

for i := 0; i < len(matrix); i++ {
    for j := 0; j < len(matrix[i]); j++ {
        fmt.Printf("%d ", matrix[i][j])
    }
    fmt.Println()
}
```

### For Loop Patterns

```go
// Retry pattern
const maxRetries = 3
for attempt := 1; attempt <= maxRetries; attempt++ {
    err := tryOperation()
    if err == nil {
        break // Success
    }

    if attempt == maxRetries {
        return fmt.Errorf("failed after %d attempts: %w", maxRetries, err)
    }

    fmt.Printf("Attempt %d failed, retrying...\n", attempt)
    time.Sleep(time.Second * time.Duration(attempt))
}

// Processing with timeout
timeout := time.After(5 * time.Second)
ticker := time.NewTicker(100 * time.Millisecond)
defer ticker.Stop()

for {
    select {
    case <-timeout:
        return errors.New("operation timed out")
    case <-ticker.C:
        if isComplete() {
            return nil
        }
    }
}
```

## Range Loops

Range loops provide a convenient way to iterate over collections.

### Range over Slices and Arrays

```go
// Range over slice
numbers := []int{1, 2, 3, 4, 5}

// Both index and value
for i, v := range numbers {
    fmt.Printf("Index: %d, Value: %d\n", i, v)
}

// Only index
for i := range numbers {
    fmt.Printf("Index: %d\n", i)
}

// Only value
for _, v := range numbers {
    fmt.Printf("Value: %d\n", v)
}

// Range over array
arr := [3]string{"a", "b", "c"}
for i, v := range arr {
    fmt.Printf("%d: %s\n", i, v)
}
```

### Range over Maps

```go
// Range over map
ages := map[string]int{
    "Alice": 30,
    "Bob":   25,
    "Carol": 35,
}

// Both key and value
for name, age := range ages {
    fmt.Printf("%s is %d years old\n", name, age)
}

// Only keys
for name := range ages {
    fmt.Printf("Name: %s\n", name)
}

// Only values (rare)
for _, age := range ages {
    fmt.Printf("Age: %d\n", age)
}
```

### Range over Strings

```go
// Range over string (iterates over runes)
text := "Hello, 世界"

// Both index and rune
for i, r := range text {
    fmt.Printf("Index: %d, Rune: %c, Unicode: U+%04X\n", i, r, r)
}

// Only runes
for _, r := range text {
    fmt.Printf("Character: %c\n", r)
}

// Byte iteration (different from range)
for i := 0; i < len(text); i++ {
    fmt.Printf("Byte %d: %d\n", i, text[i])
}
```

### Range over Channels

```go
// Range over channel
ch := make(chan int, 5)

// Send some values
go func() {
    for i := 1; i <= 5; i++ {
        ch <- i
    }
    close(ch) // Important: close channel to end range loop
}()

// Receive values using range
for value := range ch {
    fmt.Printf("Received: %d\n", value)
}

// Range will exit when channel is closed
fmt.Println("Channel closed, range loop ended")
```

### Range Gotchas and Best Practices

```go
// Gotcha 1: Range variable reuse
numbers := []int{1, 2, 3}
var funcs []func()

// Wrong: all functions will print 3
for _, v := range numbers {
    funcs = append(funcs, func() {
        fmt.Println(v) // v is reused, will be 3 for all
    })
}

// Correct: capture the value
for _, v := range numbers {
    v := v // Create new variable
    funcs = append(funcs, func() {
        fmt.Println(v) // Now each function has its own v
    })
}

// Gotcha 2: Modifying slice during iteration
slice := []int{1, 2, 3, 4, 5}

// This can cause issues
for i, v := range slice {
    if v%2 == 0 {
        slice = append(slice[:i], slice[i+1:]...) // Modifying slice
    }
}

// Better approach: iterate backwards or collect indices
var toRemove []int
for i, v := range slice {
    if v%2 == 0 {
        toRemove = append(toRemove, i)
    }
}

// Remove in reverse order
for i := len(toRemove) - 1; i >= 0; i-- {
    idx := toRemove[i]
    slice = append(slice[:idx], slice[idx+1:]...)
}
```

## Break and Continue

Break and continue statements control loop execution flow.

### Break Statement

```go
// Break out of loop
for i := 0; i < 10; i++ {
    if i == 5 {
        break // Exit loop when i equals 5
    }
    fmt.Printf("%d ", i)
}
// Output: 0 1 2 3 4

// Break with label (for nested loops)
outer:
for i := 0; i < 3; i++ {
    for j := 0; j < 3; j++ {
        if i == 1 && j == 1 {
            break outer // Break out of both loops
        }
        fmt.Printf("(%d,%d) ", i, j)
    }
}
// Output: (0,0) (0,1) (0,2) (1,0)

// Break in switch (implicit)
switch value {
case 1:
    fmt.Println("One")
    // Implicit break here
case 2:
    fmt.Println("Two")
}
```

### Continue Statement

```go
// Continue to next iteration
for i := 0; i < 10; i++ {
    if i%2 == 0 {
        continue // Skip even numbers
    }
    fmt.Printf("%d ", i)
}
// Output: 1 3 5 7 9

// Continue with label
outer:
for i := 0; i < 3; i++ {
    for j := 0; j < 3; j++ {
        if j == 1 {
            continue outer // Continue outer loop
        }
        fmt.Printf("(%d,%d) ", i, j)
    }
}
// Output: (0,0) (1,0) (2,0)

// Continue in range loop
numbers := []int{1, 2, 3, 4, 5}
for _, num := range numbers {
    if num%2 == 0 {
        continue
    }
    fmt.Printf("Odd: %d\n", num)
}
```

### Practical Examples

```go
// Processing with error handling
func processItems(items []Item) error {
    for i, item := range items {
        if err := validateItem(item); err != nil {
            fmt.Printf("Skipping invalid item at index %d: %v\n", i, err)
            continue
        }

        if err := processItem(item); err != nil {
            return fmt.Errorf("failed to process item at index %d: %w", i, err)
        }
    }
    return nil
}

// Search with early termination
func findUser(users []User, id int) *User {
    for _, user := range users {
        if user.ID == id {
            return &user // Found, break out of function
        }
    }
    return nil // Not found
}

// Filtering with continue
func filterEvenNumbers(numbers []int) []int {
    var result []int
    for _, num := range numbers {
        if num%2 != 0 {
            continue // Skip odd numbers
        }
        result = append(result, num)
    }
    return result
}
```

## Goto Statement

The goto statement is rarely used in Go but available for specific cases.

### Basic Goto Usage

```go
// Basic goto (rarely recommended)
func gotoExample() {
    i := 0

loop:
    fmt.Printf("%d ", i)
    i++
    if i < 5 {
        goto loop
    }
    fmt.Println("Done")
}

// Goto for error handling (sometimes useful)
func processWithGoto() error {
    file, err := os.Open("input.txt")
    if err != nil {
        goto cleanup
    }

    data, err := io.ReadAll(file)
    if err != nil {
        goto cleanup
    }

    err = processData(data)
    if err != nil {
        goto cleanup
    }

    file.Close()
    return nil

cleanup:
    if file != nil {
        file.Close()
    }
    return err
}
```

### When to Avoid Goto

```go
// Bad: Using goto instead of proper control structures
func badGotoExample() {
    i := 0

start:
    if i >= 10 {
        goto end
    }

    if i%2 == 0 {
        goto skip
    }

    fmt.Printf("Odd: %d\n", i)

skip:
    i++
    goto start

end:
    fmt.Println("Done")
}

// Good: Using proper control structures
func goodExample() {
    for i := 0; i < 10; i++ {
        if i%2 == 0 {
            continue
        }
        fmt.Printf("Odd: %d\n", i)
    }
    fmt.Println("Done")
}
```

## Defer Statement

Defer schedules function calls to be executed when the surrounding function returns.

### Basic Defer Usage

```go
// Basic defer
func deferExample() {
    fmt.Println("Start")
    defer fmt.Println("Deferred 1")
    defer fmt.Println("Deferred 2")
    fmt.Println("End")
}
// Output:
// Start
// End
// Deferred 2
// Deferred 1

// Defer with function calls
func fileProcessing() error {
    file, err := os.Open("data.txt")
    if err != nil {
        return err
    }
    defer file.Close() // Always close file when function returns

    // Process file
    data, err := io.ReadAll(file)
    if err != nil {
        return err // file.Close() will still be called
    }

    return processData(data)
}
```

### Defer Execution Order

```go
// Defer executes in LIFO (Last In, First Out) order
func deferOrder() {
    defer fmt.Println("First defer")
    defer fmt.Println("Second defer")
    defer fmt.Println("Third defer")
    fmt.Println("Function body")
}
// Output:
// Function body
// Third defer
// Second defer
// First defer

// Defer with loop
func deferInLoop() {
    for i := 0; i < 3; i++ {
        defer fmt.Printf("Deferred: %d\n", i)
    }
    fmt.Println("Loop done")
}
// Output:
// Loop done
// Deferred: 2
// Deferred: 1
// Deferred: 0
```

### Defer Patterns

```go
// Resource cleanup pattern
func resourceCleanup() error {
    mutex.Lock()
    defer mutex.Unlock() // Always unlock

    conn, err := database.Connect()
    if err != nil {
        return err
    }
    defer conn.Close() // Always close connection

    tx, err := conn.BeginTransaction()
    if err != nil {
        return err
    }
    defer tx.Rollback() // Always rollback if not committed

    // Do work
    if err := doWork(tx); err != nil {
        return err // Rollback will happen automatically
    }

    return tx.Commit()
}

// Timing pattern
func timedFunction() {
    start := time.Now()
    defer func() {
        fmt.Printf("Function took %v\n", time.Since(start))
    }()

    // Do work
    time.Sleep(100 * time.Millisecond)
}

// Recovery pattern
func safeFunction() (err error) {
    defer func() {
        if r := recover(); r != nil {
            err = fmt.Errorf("panic recovered: %v", r)
        }
    }()

    // Code that might panic
    riskyOperation()
    return nil
}
```

### Defer Gotchas

```go
// Gotcha 1: Defer evaluates arguments immediately
func deferGotcha1() {
    i := 0
    defer fmt.Println(i) // Will print 0, not 5
    i = 5
}

// Solution: Use closure
func deferSolution1() {
    i := 0
    defer func() {
        fmt.Println(i) // Will print 5
    }()
    i = 5
}

// Gotcha 2: Defer in loop with closure
func deferGotcha2() {
    for i := 0; i < 3; i++ {
        defer func() {
            fmt.Println(i) // Will print 3, 3, 3
        }()
    }
}

// Solution: Pass value to closure
func deferSolution2() {
    for i := 0; i < 3; i++ {
        defer func(val int) {
            fmt.Println(val) // Will print 2, 1, 0
        }(i)
    }
}
```

## Select Statement

Select statement is used for channel operations and is fundamental to Go's concurrency model.

### Basic Select Usage

```go
// Basic select with channels
func selectExample() {
    ch1 := make(chan string)
    ch2 := make(chan string)

    go func() {
        time.Sleep(1 * time.Second)
        ch1 <- "Message from ch1"
    }()

    go func() {
        time.Sleep(2 * time.Second)
        ch2 <- "Message from ch2"
    }()

    select {
    case msg1 := <-ch1:
        fmt.Println("Received:", msg1)
    case msg2 := <-ch2:
        fmt.Println("Received:", msg2)
    }
}

// Select with default case (non-blocking)
func nonBlockingSelect() {
    ch := make(chan string)

    select {
    case msg := <-ch:
        fmt.Println("Received:", msg)
    default:
        fmt.Println("No message received")
    }
}
```

### Select Patterns

```go
// Timeout pattern
func withTimeout() error {
    ch := make(chan string)

    go func() {
        time.Sleep(2 * time.Second)
        ch <- "Result"
    }()

    select {
    case result := <-ch:
        fmt.Println("Got result:", result)
        return nil
    case <-time.After(1 * time.Second):
        return errors.New("operation timed out")
    }
}

// Worker pool pattern
func workerPool() {
    jobs := make(chan int, 100)
    results := make(chan int, 100)
    done := make(chan bool)

    // Start workers
    for w := 1; w <= 3; w++ {
        go worker(w, jobs, results)
    }

    // Send jobs
    go func() {
        for j := 1; j <= 5; j++ {
            jobs <- j
        }
        close(jobs)
    }()

    // Collect results
    go func() {
        for a := 1; a <= 5; a++ {
            <-results
        }
        done <- true
    }()

    <-done
}

func worker(id int, jobs <-chan int, results chan<- int) {
    for j := range jobs {
        fmt.Printf("Worker %d processing job %d\n", id, j)
        time.Sleep(time.Second)
        results <- j * 2
    }
}

// Fan-in pattern
func fanIn(input1, input2 <-chan string) <-chan string {
    output := make(chan string)

    go func() {
        for {
            select {
            case msg := <-input1:
                output <- msg
            case msg := <-input2:
                output <- msg
            }
        }
    }()

    return output
}
```

## Best Practices

### Control Structure Best Practices

```go
// 1. Prefer early returns over nested if statements
func validateUser(user *User) error {
    // Good: Early returns
    if user == nil {
        return errors.New("user cannot be nil")
    }

    if user.Name == "" {
        return errors.New("name is required")
    }

    if user.Age < 0 {
        return errors.New("age cannot be negative")
    }

    return nil
}

// Avoid: Nested if statements
func validateUserBad(user *User) error {
    if user != nil {
        if user.Name != "" {
            if user.Age >= 0 {
                return nil
            } else {
                return errors.New("age cannot be negative")
            }
        } else {
            return errors.New("name is required")
        }
    } else {
        return errors.New("user cannot be nil")
    }
}

// 2. Use switch for multiple conditions
func getGrade(score int) string {
    // Good: Switch statement
    switch {
    case score >= 90:
        return "A"
    case score >= 80:
        return "B"
    case score >= 70:
        return "C"
    case score >= 60:
        return "D"
    default:
        return "F"
    }
}

// Avoid: Long if-else chain
func getGradeBad(score int) string {
    if score >= 90 {
        return "A"
    } else if score >= 80 {
        return "B"
    } else if score >= 70 {
        return "C"
    } else if score >= 60 {
        return "D"
    } else {
        return "F"
    }
}

// 3. Use range for collections
func processItems(items []Item) {
    // Good: Range loop
    for i, item := range items {
        if err := processItem(item); err != nil {
            fmt.Printf("Error processing item %d: %v\n", i, err)
        }
    }
}

// Avoid: Manual indexing when not needed
func processItemsBad(items []Item) {
    for i := 0; i < len(items); i++ {
        if err := processItem(items[i]); err != nil {
            fmt.Printf("Error processing item %d: %v\n", i, err)
        }
    }
}
```

## Common Patterns

### Error Handling Patterns

```go
// Pattern 1: Guard clauses
func processRequest(req *Request) error {
    if req == nil {
        return errors.New("request cannot be nil")
    }

    if req.UserID == 0 {
        return errors.New("user ID is required")
    }

    if req.Action == "" {
        return errors.New("action is required")
    }

    // Main logic here
    return nil
}

// Pattern 2: Error accumulation
func validateFields(data map[string]string) []error {
    var errors []error

    for field, value := range data {
        if value == "" {
            errors = append(errors, fmt.Errorf("field %s is required", field))
            continue
        }

        if len(value) > 100 {
            errors = append(errors, fmt.Errorf("field %s is too long", field))
        }
    }

    return errors
}

// Pattern 3: Retry with backoff
func retryWithBackoff(operation func() error, maxRetries int) error {
    for attempt := 1; attempt <= maxRetries; attempt++ {
        err := operation()
        if err == nil {
            return nil
        }

        if attempt == maxRetries {
            return fmt.Errorf("failed after %d attempts: %w", maxRetries, err)
        }

        backoff := time.Duration(attempt) * time.Second
        time.Sleep(backoff)
    }

    return nil
}
```

## Performance Considerations

### Loop Performance

```go
// 1. Avoid repeated calculations in loops
func inefficientLoop(items []Item) {
    for i := 0; i < len(items); i++ { // len() called every iteration
        processItem(items[i])
    }
}

func efficientLoop(items []Item) {
    length := len(items) // Calculate once
    for i := 0; i < length; i++ {
        processItem(items[i])
    }
}

// Even better: Use range
func bestLoop(items []Item) {
    for _, item := range items {
        processItem(item)
    }
}

// 2. Pre-allocate slices when size is known
func inefficientSliceBuilding(n int) []int {
    var result []int
    for i := 0; i < n; i++ {
        result = append(result, i) // Multiple reallocations
    }
    return result
}

func efficientSliceBuilding(n int) []int {
    result := make([]int, 0, n) // Pre-allocate capacity
    for i := 0; i < n; i++ {
        result = append(result, i) // No reallocations
    }
    return result
}

// 3. Use break/continue to avoid unnecessary work
func optimizedProcessing(items []Item) {
    for _, item := range items {
        if !item.IsValid() {
            continue // Skip invalid items early
        }

        if item.IsComplete() {
            break // Stop when complete item found
        }

        processItem(item)
    }
}
```

This comprehensive guide covers all control structures in Go with practical examples, best practices, and performance considerations. Understanding these patterns will help you write more efficient and readable Go code.
