# Variables in Go

This comprehensive guide covers everything you need to know about variables in Go, from basic declarations to advanced concepts like memory allocation and performance considerations.

**Note:** For constants, see [Constants in Go](7-constants.md). For naming conventions, see [Naming Conventions in Go](9-naming-conventions.md). For detailed data type information, see [Data Types in Go](8-data-types.md).

## Table of Contents

1. [Variable Declaration](#variable-declaration)
2. [Basic Data Types Overview](#basic-data-types-overview)
3. [Zero Values](#zero-values)
4. [Type Inference](#type-inference)
5. [Scope and Visibility](#scope-and-visibility)
6. [Variable Shadowing](#variable-shadowing)
7. [Pointers and Memory Addresses](#pointers-and-memory-addresses)
8. [Type Conversion and Type Assertion](#type-conversion-and-type-assertion)
9. [Alias Types](#alias-types)
10. [Variable Lifecycle and Memory Allocation](#variable-lifecycle-and-memory-allocation)
11. [Best Practices](#best-practices)
12. [Common Mistakes](#common-mistakes)
13. [Performance Considerations](#performance-considerations)

## Variable Declaration

Go provides several ways to declare variables, each with its own use cases and benefits.

### 1. Using `var` Keyword

The most explicit way to declare variables:

```go
// Basic syntax: var name type
var message string
var count int
var isActive bool

// With initialization
var message string = "Hello, World!"
var count int = 42
var isActive bool = true

// Multiple variables of same type
var x, y, z int

// Multiple variables with initialization
var x, y, z int = 1, 2, 3

// Different types in one declaration
var (
    name    string = "John"
    age     int    = 30
    height  float64 = 5.9
    married bool   = true
)
```

### 2. Short Variable Declaration (`:=`)

The most common way to declare and initialize variables:

```go
// Short declaration syntax: name := value
message := "Hello, World!"
count := 42
isActive := true

// Multiple variables
x, y, z := 1, 2, 3
name, age := "John", 30

// Mixed declaration (at least one new variable)
var existing int
existing, newVar := 10, 20  // newVar is newly declared
```

**Important Notes:**
- `:=` can only be used inside functions
- At least one variable on the left must be new
- Cannot specify type explicitly with `:=`

### 3. Package-Level Variables

Variables declared outside functions:

```go
package main

import "fmt"

// Package-level variables
var globalCounter int = 0
var appName string = "MyApp"

// Using var block for organization
var (
    version    = "1.0.0"
    buildDate  = "2024-01-01"
    debugMode  = false
)

func main() {
    fmt.Println(appName, version)
}
```

### 4. Variable Declaration Patterns

```go
// Pattern 1: Declare then assign
var result int
result = calculateSomething()

// Pattern 2: Declare and initialize
var result int = calculateSomething()

// Pattern 3: Short declaration (most common)
result := calculateSomething()

// Pattern 4: Multiple return values
value, err := someFunction()
if err != nil {
    // handle error
}

// Pattern 5: Ignoring values with blank identifier
_, err := someFunction()  // ignore first return value
```

## Basic Data Types Overview

Go is a statically typed language with a rich set of built-in types. For detailed information about each type, see [Data Types in Go](8-data-types.md).

### Type Categories

```go
// Basic types
var number int = 42
var text string = "hello"
var flag bool = true
var price float64 = 99.99

// Composite types
var numbers []int = []int{1, 2, 3}
var ages map[string]int = make(map[string]int)
var person struct{ name string; age int }

// Reference types
var slice []int        // Reference to array
var channel chan int   // Reference to channel
var pointer *int       // Reference to memory location
var iface interface{}  // Reference to concrete type
```

### Quick Type Reference

```go
// Numeric types
int, int8, int16, int32, int64          // Signed integers
uint, uint8, uint16, uint32, uint64     // Unsigned integers
float32, float64                        // Floating-point numbers
complex64, complex128                   // Complex numbers
byte                                    // Alias for uint8
rune                                    // Alias for int32 (Unicode code point)

// Other basic types
bool                                    // Boolean
string                                  // String

// Composite types
[n]T                                    // Array of n elements of type T
[]T                                     // Slice of type T
map[K]V                                 // Map with keys of type K and values of type V
struct{ ... }                           // Struct type
chan T                                  // Channel of type T
interface{ ... }                        // Interface type
func(T) R                              // Function type
*T                                      // Pointer to type T
```

### Variable Declaration with Types

```go
// Explicit type declaration
var count int = 42
var name string = "John"
var isActive bool = true

// Type inference (Go infers the type)
var count = 42          // int
var name = "John"       // string
var isActive = true     // bool

// Short declaration (type inferred)
count := 42             // int
name := "John"          // string
isActive := true        // bool

// Multiple variables
var x, y int = 10, 20
var name, age = "John", 30
x, y := 10, 20
name, age := "John", 30
```

## Zero Values

Go automatically initializes variables with zero values when no explicit value is provided.

### Zero Values by Type

```go
// Numeric types
var i int           // 0
var f float64       // 0.0
var c complex128    // (0+0i)

// Boolean
var b bool          // false

// String
var s string        // "" (empty string)

// Pointers, slices, maps, channels, functions, interfaces
var p *int          // nil
var slice []int     // nil
var m map[string]int // nil
var ch chan int     // nil
var fn func()       // nil
var iface interface{} // nil

// Arrays and structs
var arr [3]int      // [0, 0, 0]
var person Person   // {Name: "", Age: 0, Email: "", IsActive: false}
```

### Zero Value Behavior

```go
// Zero values are ready to use
var slice []int
slice = append(slice, 1, 2, 3)  // Works! slice grows from nil

var m map[string]int
// m["key"] = 1  // Panic! nil map, need to initialize first
m = make(map[string]int)
m["key"] = 1     // Now it works

// Checking for zero values
if slice == nil {
    fmt.Println("slice is nil")
}

if len(slice) == 0 {
    fmt.Println("slice is empty")
}
```

### Zero Value Patterns

```go
// Pattern 1: Check and initialize
func processSlice(data []int) {
    if data == nil {
        data = make([]int, 0)
    }
    // process data
}

// Pattern 2: Zero value as valid state
type Counter struct {
    value int  // zero value (0) is valid
}

func (c *Counter) Increment() {
    c.value++  // works even if Counter is zero value
}

// Pattern 3: Distinguish between zero value and not set
type Config struct {
    Port     int
    PortSet  bool  // flag to indicate if Port was explicitly set
}
```

## Type Inference

Go can automatically infer types in many situations, making code more concise while maintaining type safety.

### Basic Type Inference

```go
// Go infers the type from the value
var name = "John"        // string
var age = 30             // int
var height = 5.9         // float64
var isActive = true      // bool

// Short declaration always uses type inference
message := "Hello"       // string
count := 42              // int
pi := 3.14159           // float64
```

### Type Inference Rules

```go
// Integer literals
var small = 10           // int
var large = 1000000000000 // int (if fits) or int64

// Floating-point literals
var f1 = 3.14            // float64
var f2 = 3.14159265359   // float64

// Complex literals
var c = 1 + 2i           // complex128

// String literals
var s1 = "hello"         // string
var s2 = `raw string`    // string

// Rune literals
var r = 'A'              // rune (int32)

// Boolean literals
var b = true             // bool
```

### Type Inference with Functions

```go
// Function return type inference
func getString() string {
    return "hello"
}

var message = getString()  // string (inferred from function return)

// Multiple return values
func getNameAge() (string, int) {
    return "John", 30
}

name, age := getNameAge()  // name: string, age: int

// Type inference with type conversion
var i int = 42
var f = float64(i)         // float64 (explicit conversion)
var j = i                  // int (same type)
```

### Limitations of Type Inference

```go
// Cannot infer from nil
// var slice = nil        // Error: cannot use nil
var slice []int = nil     // OK: explicit type

// Cannot infer interface type
// var writer = os.Stdout // Error: type is *os.File, not io.Writer
var writer io.Writer = os.Stdout  // OK: explicit interface type

// Cannot infer in some contexts
func process(data interface{}) {
    // value := data       // interface{} type
    value := data.(string) // type assertion needed
}
```

## Constants vs Variables

Understanding the difference between constants and variables is crucial for writing efficient and safe Go code.

### Constants

```go
// Constant declaration
const pi = 3.14159
const greeting = "Hello, World!"
const maxRetries = 3

// Multiple constants
const (
    statusOK    = 200
    statusError = 500
    statusNotFound = 404
)

// Typed constants
const pi float64 = 3.14159
const greeting string = "Hello"

// Untyped constants (more flexible)
const pi = 3.14159        // untyped floating-point constant
const maxSize = 1000      // untyped integer constant
```

### Constant Rules

```go
// Constants must be compile-time values
const now = time.Now()    // Error: not compile-time constant
const size = len("hello") // OK: len of string literal is compile-time

// Constants can be used in array sizes
const arraySize = 10
var arr [arraySize]int    // OK

// Constants can be used in case labels
const caseValue = 42
switch x {
case caseValue:           // OK
    // handle case
}
```

### iota - Constant Generator

```go
// Basic iota usage
const (
    Sunday = iota    // 0
    Monday           // 1
    Tuesday          // 2
    Wednesday        // 3
    Thursday         // 4
    Friday           // 5
    Saturday         // 6
)

// iota with expressions
const (
    KB = 1 << (10 * iota)  // 1 << (10 * 0) = 1
    MB                     // 1 << (10 * 1) = 1024
    GB                     // 1 << (10 * 2) = 1048576
    TB                     // 1 << (10 * 3) = 1073741824
)

// Skipping values
const (
    _  = iota        // 0 (ignored)
    Red              // 1
    Green            // 2
    Blue             // 3
)

// Complex iota patterns
const (
    FlagA = 1 << iota    // 1 << 0 = 1
    FlagB                // 1 << 1 = 2
    FlagC                // 1 << 2 = 4
    FlagD                // 1 << 3 = 8
)
```

### Variables vs Constants Comparison

| Aspect | Variables | Constants |
|--------|-----------|-----------|
| **Mutability** | Can be changed | Cannot be changed |
| **Declaration** | `var` or `:=` | `const` |
| **Initialization** | Optional | Required |
| **Scope** | Package, function, block | Package, function, block |
| **Memory** | Runtime allocation | Compile-time value |
| **Type** | Must be specified or inferred | Can be typed or untyped |
| **Usage** | Runtime values | Compile-time values |

```go
// Variable example
var counter int = 0
counter++                 // OK: variables can be modified
counter = 100            // OK: reassignment allowed

// Constant example
const maxRetries = 3
// maxRetries++          // Error: cannot modify constant
// maxRetries = 5        // Error: cannot reassign constant

// Constants are more efficient
const pi = 3.14159       // No memory allocation
var piVar = 3.14159      // Memory allocation at runtime
```

## Scope and Visibility

Variable scope determines where a variable can be accessed in your code. Go has several scope levels.

### Package Scope

Variables declared outside any function have package scope:

```go
package main

import "fmt"

// Package-level variables (visible throughout the package)
var globalCounter int = 0
var appName string = "MyApp"

// Exported variables (visible to other packages)
var ExportedVar string = "visible to other packages"

// Unexported variables (only visible within this package)
var unexportedVar string = "only visible in this package"

func main() {
    fmt.Println(globalCounter, appName)  // Accessible
    incrementCounter()
}

func incrementCounter() {
    globalCounter++  // Can access package-level variable
}
```

### Function Scope

Variables declared inside a function are only accessible within that function:

```go
func processData() {
    // Function-scoped variables
    var localVar int = 42
    var result string = "processing"

    // These variables are only accessible within processData()
    fmt.Println(localVar, result)

    // Can access package-level variables
    globalCounter++
}

func anotherFunction() {
    // fmt.Println(localVar)  // Error: localVar not accessible here
}
```

### Block Scope

Variables declared inside blocks (if, for, switch, etc.) have block scope:

```go
func demonstrateBlockScope() {
    var outerVar = "outer"

    if true {
        var innerVar = "inner"      // Block-scoped
        blockVar := "block"         // Block-scoped

        fmt.Println(outerVar)       // Accessible
        fmt.Println(innerVar)       // Accessible
        fmt.Println(blockVar)       // Accessible
    }

    // fmt.Println(innerVar)        // Error: not accessible
    // fmt.Println(blockVar)        // Error: not accessible

    for i := 0; i < 3; i++ {        // i is block-scoped to for loop
        loopVar := i * 2            // Block-scoped to for loop
        fmt.Println(loopVar)
    }

    // fmt.Println(i)               // Error: i not accessible
    // fmt.Println(loopVar)         // Error: loopVar not accessible
}
```

### Scope Rules Summary

```go
package main

var packageVar = "package level"    // Package scope

func main() {
    var functionVar = "function level"  // Function scope

    {
        var blockVar = "block level"    // Block scope

        // All three are accessible here
        fmt.Println(packageVar)         // OK
        fmt.Println(functionVar)        // OK
        fmt.Println(blockVar)           // OK
    }

    // Only package and function variables accessible here
    fmt.Println(packageVar)             // OK
    fmt.Println(functionVar)            // OK
    // fmt.Println(blockVar)            // Error: not in scope
}
```

## Variable Shadowing

Variable shadowing occurs when a variable in an inner scope has the same name as a variable in an outer scope.

### Shadowing Examples

```go
package main

import "fmt"

var name = "Global John"  // Package-level variable

func demonstrateShadowing() {
    var name = "Function John"  // Shadows package-level variable
    fmt.Println(name)           // Prints: "Function John"

    {
        var name = "Block John" // Shadows function-level variable
        fmt.Println(name)       // Prints: "Block John"
    }

    fmt.Println(name)           // Prints: "Function John"
}

func anotherFunction() {
    fmt.Println(name)           // Prints: "Global John"
}
```

### Shadowing with Short Declaration

```go
func shadowingWithShortDeclaration() {
    var err error = errors.New("original error")

    if true {
        err := errors.New("shadowed error")  // Shadows outer err
        fmt.Println(err)                     // Prints: "shadowed error"
    }

    fmt.Println(err)                         // Prints: "original error"
}

// Common mistake: unintended shadowing
func commonMistake() {
    var result string
    var err error

    if someCondition {
        result, err := someFunction()  // Creates new variables (shadows outer ones)
        // This doesn't modify the outer result and err!
        _ = result
        _ = err
    }

    // result and err are still their original values
    fmt.Println(result, err)  // Empty string and nil
}

// Correct approach
func correctApproach() {
    var result string
    var err error

    if someCondition {
        result, err = someFunction()  // Assigns to outer variables
    }

    fmt.Println(result, err)  // Modified values
}
```

### Detecting Shadowing

```go
// Use go vet to detect shadowing issues
// go vet -shadow ./...

// Example of problematic shadowing
func problematicShadowing() {
    var client *http.Client

    if needsProxy {
        client, err := createProxyClient()  // Shadows outer client!
        if err != nil {
            return
        }
        // client is only set in this block
        _ = client
    }

    // client is still nil here!
    client.Get("http://example.com")  // Panic: nil pointer dereference
}
```

## Pointers and Memory Addresses

Pointers store memory addresses of variables, enabling indirect access and efficient memory usage.

### Basic Pointer Operations

```go
// Pointer declaration
var p *int              // Pointer to int, initially nil
var s *string           // Pointer to string, initially nil

// Getting address of variable
var x int = 42
p = &x                  // p now points to x's memory address

// Dereferencing pointer
fmt.Println(*p)         // Prints: 42 (value at address p points to)
*p = 100               // Changes x to 100
fmt.Println(x)         // Prints: 100

// Pointer to pointer
var pp **int = &p      // Pointer to pointer to int
fmt.Println(**pp)      // Prints: 100
```

### Pointer Examples

```go
func pointerExamples() {
    // Example 1: Swapping values
    a, b := 10, 20
    swap(&a, &b)
    fmt.Println(a, b)  // Prints: 20, 10

    // Example 2: Modifying struct through pointer
    person := Person{Name: "John", Age: 30}
    updateAge(&person, 31)
    fmt.Println(person.Age)  // Prints: 31

    // Example 3: Avoiding large struct copies
    largeStruct := LargeStruct{/* ... */}
    processLargeStruct(&largeStruct)  // Pass pointer, not copy
}

func swap(x, y *int) {
    *x, *y = *y, *x
}

func updateAge(p *Person, newAge int) {
    p.Age = newAge  // Automatic dereferencing
    // (*p).Age = newAge  // Explicit dereferencing (same effect)
}
```

### Pointer Safety

```go
func pointerSafety() {
    var p *int

    // Check for nil before dereferencing
    if p != nil {
        fmt.Println(*p)
    }

    // Safe pointer initialization
    var x int = 42
    p = &x
    fmt.Println(*p)  // Safe to dereference

    // Using new() function
    p = new(int)     // Allocates memory and returns pointer
    *p = 42
    fmt.Println(*p)  // Prints: 42
}

// Common pointer mistakes
func pointerMistakes() {
    // Mistake 1: Dereferencing nil pointer
    var p *int
    // fmt.Println(*p)  // Panic: runtime error

    // Mistake 2: Returning pointer to local variable
    // func badFunction() *int {
    //     x := 42
    //     return &x  // Dangerous: x might be deallocated
    // }

    // Correct: Return value or use heap allocation
    func goodFunction() *int {
        x := 42
        return &x  // Go's escape analysis moves x to heap
    }
}
```

### Pointer Arithmetic (Not Allowed)

```go
// Go does NOT support pointer arithmetic (unlike C/C++)
func noPointerArithmetic() {
    arr := [3]int{1, 2, 3}
    p := &arr[0]

    // p++        // Error: invalid operation
    // p += 1     // Error: invalid operation
    // p - q      // Error: invalid operation

    // Use slices instead for sequential access
    slice := arr[:]
    for i, v := range slice {
        fmt.Printf("Index %d: %d\n", i, v)
    }
}
```

## Type Conversion and Type Assertion

Go requires explicit type conversion and provides type assertion for interfaces.

### Type Conversion

```go
// Numeric type conversions
var i int = 42
var f float64 = float64(i)    // int to float64
var u uint = uint(i)          // int to uint
var b byte = byte(i)          // int to byte (if fits)

// String conversions
var s string = string(i)      // int to string (Unicode code point)
var s2 string = strconv.Itoa(i)  // int to string representation

// Slice conversions
var bytes []byte = []byte("hello")     // string to []byte
var str string = string(bytes)         // []byte to string

// Pointer conversions (unsafe)
import "unsafe"
var ptr unsafe.Pointer = unsafe.Pointer(&i)
```

### Type Conversion Rules

```go
// Conversion between compatible types
type Celsius float64
type Fahrenheit float64

var c Celsius = 100
var f Fahrenheit = Fahrenheit(c * 9/5 + 32)  // Explicit conversion required

// Cannot convert between incompatible types
type Person struct{ Name string }
type Employee struct{ Name string }

var p Person = Person{Name: "John"}
// var e Employee = Employee(p)  // Error: cannot convert

// Must use field-by-field assignment
var e Employee = Employee{Name: p.Name}
```

### Type Assertion

```go
// Type assertion for interfaces
var i interface{} = "hello"

// Basic type assertion
s := i.(string)        // Asserts i is string, panics if not
fmt.Println(s)         // Prints: "hello"

// Safe type assertion
s, ok := i.(string)    // Returns value and boolean
if ok {
    fmt.Println("i is a string:", s)
} else {
    fmt.Println("i is not a string")
}

// Type assertion with different types
var i interface{} = 42

// This will panic
// s := i.(string)     // Panic: interface conversion

// Safe approach
if s, ok := i.(string); ok {
    fmt.Println("String:", s)
} else {
    fmt.Println("Not a string")
}

if n, ok := i.(int); ok {
    fmt.Println("Integer:", n)  // This will execute
}
```

### Type Switch

```go
func typeSwitch(i interface{}) {
    switch v := i.(type) {
    case int:
        fmt.Printf("Integer: %d\n", v)
    case string:
        fmt.Printf("String: %s\n", v)
    case bool:
        fmt.Printf("Boolean: %t\n", v)
    case []int:
        fmt.Printf("Slice of ints: %v\n", v)
    case nil:
        fmt.Println("nil value")
    default:
        fmt.Printf("Unknown type: %T\n", v)
    }
}

// Usage
typeSwitch(42)           // Integer: 42
typeSwitch("hello")      // String: hello
typeSwitch(true)         // Boolean: true
typeSwitch([]int{1,2,3}) // Slice of ints: [1 2 3]
typeSwitch(nil)          // nil value
```

**Note:** For detailed naming conventions, see [Naming Conventions in Go](9-naming-conventions.md).

## Alias Types

Alias types create new type names for existing types, providing type safety and code clarity.

### Type Definitions vs Type Aliases

```go
// Type definition (creates a new type)
type UserID int
type Temperature float64

// Type alias (creates an alias for existing type)
type MyInt = int
type MyString = string

// Difference in behavior
var uid UserID = 123
var regularInt int = 456

// uid = regularInt        // Error: cannot assign int to UserID
uid = UserID(regularInt)   // OK: explicit conversion required

var aliasInt MyInt = 789
var anotherInt int = 101

aliasInt = anotherInt      // OK: MyInt is just an alias for int
```

### Practical Type Definitions

```go
// Domain-specific types for clarity and type safety
type (
    UserID       int64
    ProductID    int64
    OrderID      int64
    EmailAddress string
    PhoneNumber  string
    Currency     string
)

// Methods can be added to defined types
func (uid UserID) String() string {
    return fmt.Sprintf("user-%d", uid)
}

func (email EmailAddress) IsValid() bool {
    return strings.Contains(string(email), "@")
}

// Usage provides type safety
func GetUser(id UserID) (*User, error) {
    // Implementation
    return nil, nil
}

func GetProduct(id ProductID) (*Product, error) {
    // Implementation
    return nil, nil
}

// This prevents mixing up IDs
var userID UserID = 123
var productID ProductID = 456

user, err := GetUser(userID)        // OK
// user, err := GetUser(productID)  // Error: cannot use ProductID as UserID
```

### Enum-like Types

```go
// Status type with predefined values
type Status int

const (
    StatusPending Status = iota
    StatusProcessing
    StatusCompleted
    StatusFailed
)

func (s Status) String() string {
    switch s {
    case StatusPending:
        return "pending"
    case StatusProcessing:
        return "processing"
    case StatusCompleted:
        return "completed"
    case StatusFailed:
        return "failed"
    default:
        return "unknown"
    }
}

// Usage
var orderStatus Status = StatusPending
fmt.Println(orderStatus)  // Prints: "pending"

// String-based enums
type Priority string

const (
    PriorityLow    Priority = "low"
    PriorityMedium Priority = "medium"
    PriorityHigh   Priority = "high"
)
```

### Type Aliases for Complex Types

```go
// Simplify complex type signatures
type HandlerFunc = func(http.ResponseWriter, *http.Request)
type MiddlewareFunc = func(HandlerFunc) HandlerFunc
type ValidationFunc = func(interface{}) error

// Map aliases for clarity
type UserCache = map[UserID]*User
type ConfigMap = map[string]interface{}
type Headers = map[string]string

// Channel aliases
type EventChannel = chan Event
type ErrorChannel = chan error
type DoneChannel = chan struct{}

// Function type aliases
type ProcessorFunc = func(data []byte) ([]byte, error)
type ValidatorFunc = func(value string) bool
type ComparatorFunc = func(a, b interface{}) int

// Usage examples
func NewUserCache() UserCache {
    return make(UserCache)
}

func ProcessRequest(handler HandlerFunc) MiddlewareFunc {
    return func(next HandlerFunc) HandlerFunc {
        return func(w http.ResponseWriter, r *http.Request) {
            // Middleware logic
            handler(w, r)
        }
    }
}
```

### Type Constraints and Generics (Go 1.18+)

```go
// Type constraints using type aliases
type Numeric interface {
    int | int8 | int16 | int32 | int64 |
    uint | uint8 | uint16 | uint32 | uint64 |
    float32 | float64
}

type Ordered interface {
    Numeric | string
}

// Generic functions with type constraints
func Max[T Ordered](a, b T) T {
    if a > b {
        return a
    }
    return b
}

// Usage
var maxInt = Max(10, 20)           // int
var maxFloat = Max(3.14, 2.71)     // float64
var maxString = Max("apple", "banana") // string
```

## Variable Lifecycle and Memory Allocation

Understanding when and where variables are allocated helps write efficient Go code.

### Stack vs Heap Allocation

```go
// Stack allocation (function scope, automatic cleanup)
func stackAllocation() {
    var x int = 42           // Stack allocated
    var arr [100]int         // Stack allocated (fixed size)

    // These variables are automatically cleaned up when function returns
}

// Heap allocation (escapes function scope)
func heapAllocation() *int {
    var x int = 42           // Heap allocated (escapes via return)
    return &x                // Pointer escapes function scope
}

func sliceAllocation() {
    var slice []int = make([]int, 1000)  // Heap allocated (dynamic size)
    var bigArray [10000]int              // Might be heap allocated (large size)

    // Large objects typically go to heap
    _ = slice
    _ = bigArray
}
```

### Escape Analysis

Go's compiler performs escape analysis to determine allocation location:

```go
// Check escape analysis with: go build -gcflags="-m" file.go

func noEscape() {
    var x int = 42           // Stack: doesn't escape
    fmt.Println(x)           // Stack: local usage only
}

func escapeViaReturn() *int {
    var x int = 42           // Heap: escapes via return
    return &x
}

func escapeViaGlobal() {
    var x int = 42           // Heap: escapes to global
    globalVar = &x
}

func escapeViaChannel(ch chan *int) {
    var x int = 42           // Heap: escapes via channel
    ch <- &x
}

func escapeViaSlice() {
    var x int = 42           // Heap: escapes via slice
    slice := []*int{&x}
    _ = slice
}
```

### Variable Lifetime Patterns

```go
// Pattern 1: Short-lived variables (stack allocated)
func processData(data []byte) error {
    var buffer [1024]byte    // Stack allocated, short-lived
    var index int = 0        // Stack allocated, short-lived

    // Process data using local variables
    for i, b := range data {
        buffer[index] = b
        index++
    }

    return nil
    // buffer and index automatically cleaned up
}

// Pattern 2: Long-lived variables (heap allocated)
type Cache struct {
    data map[string]interface{}  // Heap allocated, long-lived
}

func NewCache() *Cache {
    return &Cache{
        data: make(map[string]interface{}),  // Heap allocated
    }
}

// Pattern 3: Reusable variables (reduce allocations)
var bufferPool = sync.Pool{
    New: func() interface{} {
        return make([]byte, 1024)
    },
}

func efficientProcessing(data []byte) {
    buffer := bufferPool.Get().([]byte)  // Reuse existing buffer
    defer bufferPool.Put(buffer)         // Return to pool

    // Use buffer for processing
    copy(buffer, data)
}
```

### Memory Management Best Practices

```go
// 1. Prefer stack allocation when possible
func preferStack() {
    var localData [100]int   // Stack allocated (fixed size)
    // Process localData
}

// 2. Avoid unnecessary heap allocations
func avoidHeapAllocations() {
    // Bad: creates many small allocations
    var result string
    for i := 0; i < 1000; i++ {
        result += fmt.Sprintf("item-%d ", i)  // Many allocations
    }

    // Good: single allocation with pre-sized buffer
    var builder strings.Builder
    builder.Grow(1000 * 10)  // Pre-allocate capacity
    for i := 0; i < 1000; i++ {
        fmt.Fprintf(&builder, "item-%d ", i)
    }
    result = builder.String()
}

// 3. Reuse slices and maps
func reuseCollections() {
    var slice []int
    var m map[string]int

    for {
        // Reuse slice (reset length, keep capacity)
        slice = slice[:0]

        // Reuse map (clear all keys)
        for k := range m {
            delete(m, k)
        }

        // Process data using reused collections
    }
}

// 4. Use object pools for frequent allocations
var objectPool = sync.Pool{
    New: func() interface{} {
        return &ExpensiveObject{}
    },
}

func useObjectPool() {
    obj := objectPool.Get().(*ExpensiveObject)
    defer objectPool.Put(obj)

    // Use obj for processing
    obj.Reset()  // Reset state before returning to pool
}
```

## Best Practices

Following Go's best practices for variables leads to more readable, maintainable, and efficient code.

### Declaration Best Practices

```go
// 1. Use short variable declaration when possible
func goodDeclarations() {
    // Preferred: short and clear
    name := "John"
    age := 30

    // Avoid: unnecessarily verbose
    // var name string = "John"
    // var age int = 30
}

// 2. Group related variables
func groupVariables() {
    // Good: group related variables
    var (
        host     = "localhost"
        port     = 8080
        timeout  = 30 * time.Second
        retries  = 3
    )

    // Avoid: scattered declarations
    // var host = "localhost"
    // var port = 8080
    // var timeout = 30 * time.Second
    // var retries = 3
}

// 3. Initialize variables close to usage
func initializeNearUsage() {
    // Good: declare and initialize when needed
    if needsProcessing {
        data := fetchData()  // Declared when needed
        result := process(data)
        return result
    }

    // Avoid: early declaration far from usage
    // data := fetchData()  // Might not be used
    // if needsProcessing {
    //     result := process(data)
    //     return result
    // }
}
```

### Error Handling Best Practices

```go
// 1. Handle errors immediately
func handleErrorsImmediately() error {
    // Good: check error immediately
    file, err := os.Open("config.txt")
    if err != nil {
        return fmt.Errorf("failed to open config: %w", err)
    }
    defer file.Close()

    data, err := io.ReadAll(file)
    if err != nil {
        return fmt.Errorf("failed to read config: %w", err)
    }

    return processConfig(data)
}

// 2. Use meaningful error messages
func meaningfulErrors() error {
    userID := 123
    user, err := getUserFromDB(userID)
    if err != nil {
        // Good: context-specific error
        return fmt.Errorf("failed to get user %d from database: %w", userID, err)

        // Avoid: generic error
        // return err
    }

    return processUser(user)
}
```

### Variable Scope Best Practices

```go
// 1. Minimize variable scope
func minimizeScope() {
    // Good: variables scoped to where they're needed
    if condition1 {
        result := calculateSomething()  // Scoped to if block
        fmt.Println(result)
    }

    if condition2 {
        data := fetchData()  // Scoped to different if block
        processData(data)
    }

    // Avoid: unnecessarily wide scope
    // result := calculateSomething()  // Available everywhere
    // data := fetchData()             // Available everywhere
    // if condition1 {
    //     fmt.Println(result)
    // }
    // if condition2 {
    //     processData(data)
    // }
}

// 2. Use blank identifier for unused values
func useBlankIdentifier() {
    // Good: explicitly ignore unused return values
    _, err := someFunction()  // Ignore first return value
    if err != nil {
        // handle error
    }

    // Good: ignore unused loop variables
    for _, value := range slice {  // Ignore index
        process(value)
    }

    for i := range slice {  // Ignore value, use index
        fmt.Printf("Index: %d\n", i)
    }
}
```

### Performance Best Practices

```go
// 1. Reuse slices and maps
func reuseCollections() {
    var results []Result
    var cache map[string]interface{}

    for {
        // Reuse slice (keep capacity, reset length)
        results = results[:0]

        // Reuse map (clear contents)
        for k := range cache {
            delete(cache, k)
        }

        // Process using reused collections
        processData(results, cache)
    }
}

// 2. Pre-allocate when size is known
func preAllocate() {
    // Good: pre-allocate with known capacity
    items := make([]Item, 0, 1000)  // Length 0, capacity 1000

    for i := 0; i < 1000; i++ {
        items = append(items, createItem(i))
    }

    // Good: pre-allocate map with estimated size
    cache := make(map[string]Value, 100)

    // Avoid: growing slice/map incrementally
    // var items []Item  // Will grow multiple times
    // var cache map[string]Value  // Will resize multiple times
}

// 3. Use string builder for concatenation
func efficientStringBuilding() string {
    // Good: use strings.Builder for multiple concatenations
    var builder strings.Builder
    builder.Grow(1000)  // Pre-allocate if size is known

    for i := 0; i < 100; i++ {
        builder.WriteString(fmt.Sprintf("item-%d ", i))
    }

    return builder.String()

    // Avoid: string concatenation in loop
    // var result string
    // for i := 0; i < 100; i++ {
    //     result += fmt.Sprintf("item-%d ", i)  // Creates new string each time
    // }
    // return result
}
```

## Common Mistakes

Understanding common variable-related mistakes helps avoid bugs and performance issues.

### Mistake 1: Variable Shadowing

```go
// Problem: Unintended variable shadowing
func shadowingMistake() error {
    var result string
    var err error

    if someCondition {
        // BUG: This creates new variables, doesn't modify outer ones
        result, err := processData()  // Shadows outer variables
        if err != nil {
            return err
        }
        // result is only set in this scope
    }

    // result is still empty here!
    fmt.Println(result)  // Prints empty string
    return err           // Returns nil
}

// Solution: Use assignment instead of declaration
func shadowingFixed() error {
    var result string
    var err error

    if someCondition {
        // Correct: assigns to outer variables
        result, err = processData()  // No := here
        if err != nil {
            return err
        }
    }

    fmt.Println(result)  // Prints actual result
    return err
}
```

### Mistake 2: Nil Pointer Dereference

```go
// Problem: Dereferencing nil pointers
func nilPointerMistake() {
    var p *int

    // BUG: p is nil, this will panic
    fmt.Println(*p)  // Panic: runtime error

    var slice []int
    // BUG: slice is nil, this will panic
    slice[0] = 42    // Panic: runtime error

    var m map[string]int
    // BUG: map is nil, this will panic
    m["key"] = 42    // Panic: assignment to entry in nil map
}

// Solution: Check for nil and initialize properly
func nilPointerFixed() {
    var p *int

    // Check before dereferencing
    if p != nil {
        fmt.Println(*p)
    }

    // Initialize pointer
    x := 42
    p = &x
    fmt.Println(*p)  // Safe

    // Initialize slice
    var slice []int = make([]int, 1)
    slice[0] = 42    // Safe

    // Initialize map
    var m map[string]int = make(map[string]int)
    m["key"] = 42    // Safe
}
```

### Mistake 3: Slice/Map Modification During Iteration

```go
// Problem: Modifying slice during iteration
func modificationMistake() {
    slice := []int{1, 2, 3, 4, 5}

    // BUG: Modifying slice during iteration can cause issues
    for i, v := range slice {
        if v%2 == 0 {
            slice = append(slice[:i], slice[i+1:]...)  // Dangerous!
        }
    }
}

// Solution: Iterate backwards or collect indices
func modificationFixed() {
    slice := []int{1, 2, 3, 4, 5}

    // Method 1: Iterate backwards
    for i := len(slice) - 1; i >= 0; i-- {
        if slice[i]%2 == 0 {
            slice = append(slice[:i], slice[i+1:]...)
        }
    }

    // Method 2: Collect indices to remove
    var toRemove []int
    for i, v := range slice {
        if v%2 == 0 {
            toRemove = append(toRemove, i)
        }
    }

    // Remove in reverse order
    for i := len(toRemove) - 1; i >= 0; i-- {
        idx := toRemove[i]
        slice = append(slice[:idx], slice[idx+1:]...)
    }
}
```

### Mistake 4: Goroutine Variable Capture

```go
// Problem: Loop variable capture in goroutines
func goroutineMistake() {
    for i := 0; i < 5; i++ {
        go func() {
            // BUG: All goroutines will likely print 5
            fmt.Println(i)  // Captures loop variable by reference
        }()
    }
    time.Sleep(time.Second)
}

// Solution: Pass variable as parameter or create local copy
func goroutineFixed() {
    // Method 1: Pass as parameter
    for i := 0; i < 5; i++ {
        go func(val int) {
            fmt.Println(val)  // Each goroutine gets its own copy
        }(i)
    }

    // Method 2: Create local copy
    for i := 0; i < 5; i++ {
        i := i  // Create local copy
        go func() {
            fmt.Println(i)  // Captures local copy
        }()
    }

    time.Sleep(time.Second)
}
```

## Performance Considerations

Understanding performance implications of variable usage helps write efficient Go code.

### Memory Allocation Performance

```go
// Benchmark: Stack vs Heap allocation
func BenchmarkStackAllocation(b *testing.B) {
    for i := 0; i < b.N; i++ {
        var x [100]int  // Stack allocated
        _ = x
    }
}

func BenchmarkHeapAllocation(b *testing.B) {
    for i := 0; i < b.N; i++ {
        x := make([]int, 100)  // Heap allocated
        _ = x
    }
}

// Result: Stack allocation is ~10x faster
```

### String Concatenation Performance

```go
// Benchmark: String concatenation methods
func BenchmarkStringConcat(b *testing.B) {
    for i := 0; i < b.N; i++ {
        var result string
        for j := 0; j < 100; j++ {
            result += "hello"  // Creates new string each time
        }
        _ = result
    }
}

func BenchmarkStringBuilder(b *testing.B) {
    for i := 0; i < b.N; i++ {
        var builder strings.Builder
        for j := 0; j < 100; j++ {
            builder.WriteString("hello")  // Efficient
        }
        result := builder.String()
        _ = result
    }
}

// Result: strings.Builder is ~100x faster for many concatenations
```

### Slice Growth Performance

```go
// Benchmark: Slice growth strategies
func BenchmarkSliceGrowth(b *testing.B) {
    for i := 0; i < b.N; i++ {
        var slice []int
        for j := 0; j < 1000; j++ {
            slice = append(slice, j)  // Multiple reallocations
        }
        _ = slice
    }
}

func BenchmarkSlicePrealloc(b *testing.B) {
    for i := 0; i < b.N; i++ {
        slice := make([]int, 0, 1000)  // Pre-allocated capacity
        for j := 0; j < 1000; j++ {
            slice = append(slice, j)  // No reallocations
        }
        _ = slice
    }
}

// Result: Pre-allocation is ~3x faster
```

### Variable Reuse Performance

```go
// Benchmark: Variable reuse vs recreation
func BenchmarkVariableRecreation(b *testing.B) {
    for i := 0; i < b.N; i++ {
        slice := make([]int, 100)  // New allocation each time
        processSlice(slice)
    }
}

func BenchmarkVariableReuse(b *testing.B) {
    slice := make([]int, 100)  // Single allocation
    for i := 0; i < b.N; i++ {
        slice = slice[:0]  // Reset length, keep capacity
        processSlice(slice)
    }
}

// Result: Reuse is ~5x faster
```

### Performance Tips Summary

1. **Prefer stack allocation**: Use fixed-size arrays and local variables when possible
2. **Pre-allocate collections**: Use `make([]T, 0, capacity)` when size is known
3. **Reuse variables**: Reset and reuse slices/maps instead of creating new ones
4. **Use string.Builder**: For multiple string concatenations
5. **Avoid unnecessary heap escapes**: Keep variables local when possible
6. **Use object pools**: For frequently allocated/deallocated objects
7. **Profile your code**: Use `go test -bench` and `go tool pprof` to identify bottlenecks

This comprehensive guide covers all aspects of variables in Go, from basic declarations to advanced performance optimization techniques. Understanding these concepts will help you write more efficient and maintainable Go code.