# Functions in Go

This comprehensive guide covers functions in Go, including their definition, advanced features, closures, higher-order functions, and functional programming patterns.

## Table of Contents

1. [Overview](#overview)
2. [Function Basics](#function-basics)
3. [Function Parameters](#function-parameters)
4. [Return Values](#return-values)
5. [Variadic Functions](#variadic-functions)
6. [Anonymous Functions](#anonymous-functions)
7. [Closures](#closures)
8. [Higher-Order Functions](#higher-order-functions)
9. [Function Types](#function-types)
10. [Methods vs Functions](#methods-vs-functions)
11. [Recursive Functions](#recursive-functions)
12. [Generic Functions](#generic-functions)
13. [Function Patterns](#function-patterns)
14. [Best Practices](#best-practices)
15. [Performance Considerations](#performance-considerations)

## Overview

Functions are first-class citizens in Go, meaning they can be assigned to variables, passed as arguments, and returned from other functions. This enables powerful functional programming patterns.

### Key Characteristics

```go
// Functions are first-class citizens
// Functions can have multiple return values
// Functions support variadic parameters
// Functions can be anonymous (lambdas)
// Functions can capture variables (closures)
// Functions can be generic (Go 1.18+)
```

### Functions vs Other Languages

| Feature | Go Functions | JavaScript Functions | Python Functions |
|---------|--------------|---------------------|------------------|
| **Multiple Returns** | Yes | No (objects/arrays) | Yes (tuples) |
| **Named Returns** | Yes | No | No |
| **Variadic** | Yes | Yes (rest params) | Yes (*args) |
| **Closures** | Yes | Yes | Yes |
| **Generics** | Yes (1.18+) | No (TypeScript) | Yes |

## Function Basics

Understanding the fundamental syntax and concepts of functions.

### Basic Function Declaration

```go
// Basic function syntax
func functionName(parameters) returnType {
    // function body
    return value
}

// Simple function
func greet(name string) string {
    return "Hello, " + name
}

// Function with no parameters
func getCurrentTime() string {
    return time.Now().Format("2006-01-02 15:04:05")
}

// Function with no return value
func printMessage(message string) {
    fmt.Println(message)
}

// Function with multiple parameters
func add(a, b int) int {
    return a + b
}

// Parameters of same type can be grouped
func multiply(a, b, c int) int {
    return a * b * c
}
```

### Function Visibility

```go
// Public function (exported) - starts with capital letter
func PublicFunction() string {
    return "This function is exported"
}

// Private function (unexported) - starts with lowercase letter
func privateFunction() string {
    return "This function is not exported"
}

// Package-level function
func PackageFunction() {
    // Can be called from other files in the same package
}
```

### Function Documentation

```go
// Add adds two integers and returns the result.
// This function demonstrates proper Go documentation.
func Add(a, b int) int {
    return a + b
}

// Calculate performs a mathematical operation on two numbers.
// The operation parameter determines which operation to perform:
//   - "add": addition
//   - "sub": subtraction
//   - "mul": multiplication
//   - "div": division
// Returns the result and an error if the operation is invalid.
func Calculate(a, b float64, operation string) (float64, error) {
    switch operation {
    case "add":
        return a + b, nil
    case "sub":
        return a - b, nil
    case "mul":
        return a * b, nil
    case "div":
        if b == 0 {
            return 0, errors.New("division by zero")
        }
        return a / b, nil
    default:
        return 0, fmt.Errorf("unknown operation: %s", operation)
    }
}
```

## Function Parameters

Different ways to define and use function parameters.

### Parameter Types

```go
// Basic parameter types
func processData(
    id int,
    name string,
    active bool,
    score float64,
) {
    fmt.Printf("ID: %d, Name: %s, Active: %t, Score: %.2f\n",
        id, name, active, score)
}

// Pointer parameters
func modifyValue(ptr *int) {
    if ptr != nil {
        *ptr = 100
    }
}

// Slice parameters
func processSlice(numbers []int) int {
    sum := 0
    for _, num := range numbers {
        sum += num
    }
    return sum
}

// Map parameters
func processMap(data map[string]int) {
    for key, value := range data {
        fmt.Printf("%s: %d\n", key, value)
    }
}

// Struct parameters
type Person struct {
    Name string
    Age  int
}

func processPerson(p Person) {
    fmt.Printf("Processing %s, age %d\n", p.Name, p.Age)
}

func processPersonPtr(p *Person) {
    if p != nil {
        p.Age++  // Modify original
    }
}
```

### Parameter Passing

```go
// Pass by value (copy)
func passByValue(x int) {
    x = 100  // Only modifies the copy
}

// Pass by reference (pointer)
func passByReference(x *int) {
    if x != nil {
        *x = 100  // Modifies the original
    }
}

// Demonstration
func demonstrateParameterPassing() {
    value := 42

    passByValue(value)
    fmt.Println(value)  // Still 42

    passByReference(&value)
    fmt.Println(value)  // Now 100
}

// Slices and maps are reference types
func modifySlice(slice []int) {
    if len(slice) > 0 {
        slice[0] = 999  // Modifies original slice
    }
}

func modifyMap(m map[string]int) {
    m["new"] = 123  // Modifies original map
}
```

### Default Parameters Pattern

```go
// Go doesn't have default parameters, but we can simulate them

// Using function overloading pattern
func ConnectDefault() *Connection {
    return Connect("localhost", 8080, 30*time.Second)
}

func ConnectWithHost(host string) *Connection {
    return Connect(host, 8080, 30*time.Second)
}

func ConnectWithHostPort(host string, port int) *Connection {
    return Connect(host, port, 30*time.Second)
}

func Connect(host string, port int, timeout time.Duration) *Connection {
    // Implementation
    return &Connection{
        Host:    host,
        Port:    port,
        Timeout: timeout,
    }
}

// Using options pattern (preferred)
type ConnectionOptions struct {
    Host    string
    Port    int
    Timeout time.Duration
}

func NewConnection(opts *ConnectionOptions) *Connection {
    // Set defaults
    if opts == nil {
        opts = &ConnectionOptions{}
    }
    if opts.Host == "" {
        opts.Host = "localhost"
    }
    if opts.Port == 0 {
        opts.Port = 8080
    }
    if opts.Timeout == 0 {
        opts.Timeout = 30 * time.Second
    }

    return &Connection{
        Host:    opts.Host,
        Port:    opts.Port,
        Timeout: opts.Timeout,
    }
}

// Usage
func demonstrateDefaultParameters() {
    // Using defaults
    conn1 := NewConnection(nil)

    // Partial customization
    conn2 := NewConnection(&ConnectionOptions{
        Host: "example.com",
    })

    // Full customization
    conn3 := NewConnection(&ConnectionOptions{
        Host:    "api.example.com",
        Port:    443,
        Timeout: 60 * time.Second,
    })

    _ = conn1
    _ = conn2
    _ = conn3
}
```

## Return Values

Go's powerful return value features including multiple returns and named returns.

### Multiple Return Values

```go
// Multiple return values
func divide(a, b float64) (float64, error) {
    if b == 0 {
        return 0, errors.New("division by zero")
    }
    return a / b, nil
}

// Multiple values of same type
func getMinMax(numbers []int) (int, int) {
    if len(numbers) == 0 {
        return 0, 0
    }

    min, max := numbers[0], numbers[0]
    for _, num := range numbers {
        if num < min {
            min = num
        }
        if num > max {
            max = num
        }
    }
    return min, max
}

// Multiple different types
func parseUser(data string) (string, int, bool, error) {
    parts := strings.Split(data, ",")
    if len(parts) != 3 {
        return "", 0, false, errors.New("invalid format")
    }

    name := strings.TrimSpace(parts[0])
    age, err := strconv.Atoi(strings.TrimSpace(parts[1]))
    if err != nil {
        return "", 0, false, err
    }

    active := strings.TrimSpace(parts[2]) == "true"
    return name, age, active, nil
}

// Usage
func demonstrateMultipleReturns() {
    // Handle multiple returns
    result, err := divide(10, 3)
    if err != nil {
        fmt.Printf("Error: %v\n", err)
        return
    }
    fmt.Printf("Result: %.2f\n", result)

    // Ignore some return values
    min, _ := getMinMax([]int{5, 2, 8, 1, 9})
    fmt.Printf("Minimum: %d\n", min)

    // Use all return values
    name, age, active, err := parseUser("Alice, 30, true")
    if err != nil {
        fmt.Printf("Parse error: %v\n", err)
        return
    }
    fmt.Printf("User: %s, Age: %d, Active: %t\n", name, age, active)
}
```

### Named Return Values

```go
// Named return values
func calculateStats(numbers []int) (sum, count int, average float64) {
    count = len(numbers)
    if count == 0 {
        return  // Returns zero values: 0, 0, 0.0
    }

    for _, num := range numbers {
        sum += num
    }

    average = float64(sum) / float64(count)
    return  // Returns sum, count, average
}

// Named returns with explicit values
func processFile(filename string) (content []byte, size int64, err error) {
    file, err := os.Open(filename)
    if err != nil {
        return nil, 0, err  // Explicit return
    }
    defer file.Close()

    stat, err := file.Stat()
    if err != nil {
        return  // Returns nil, 0, err
    }
    size = stat.Size()

    content, err = io.ReadAll(file)
    return  // Returns content, size, err
}

// Named returns for clarity
func validateAndTransform(input string) (output string, valid bool, reason string) {
    if input == "" {
        return "", false, "input cannot be empty"
    }

    if len(input) < 3 {
        return "", false, "input too short"
    }

    output = strings.ToUpper(strings.TrimSpace(input))
    valid = true
    reason = "validation passed"
    return
}
```

### Return Value Patterns

```go
// Result pattern with struct
type Result struct {
    Value interface{}
    Error error
}

func processAsync(data string) Result {
    // Simulate processing
    if data == "" {
        return Result{Error: errors.New("empty data")}
    }

    return Result{Value: strings.ToUpper(data)}
}

// Option pattern (similar to Rust's Option)
type Option[T any] struct {
    value *T
}

func Some[T any](value T) Option[T] {
    return Option[T]{value: &value}
}

func None[T any]() Option[T] {
    return Option[T]{}
}

func (o Option[T]) IsSome() bool {
    return o.value != nil
}

func (o Option[T]) IsNone() bool {
    return o.value == nil
}

func (o Option[T]) Unwrap() T {
    if o.value == nil {
        panic("called Unwrap on None value")
    }
    return *o.value
}

func (o Option[T]) UnwrapOr(defaultValue T) T {
    if o.value == nil {
        return defaultValue
    }
    return *o.value
}

// Function returning Option
func findUser(id int) Option[User] {
    // Simulate database lookup
    if id <= 0 {
        return None[User]()
    }

    user := User{ID: id, Name: "Alice"}
    return Some(user)
}
```

## Variadic Functions

Functions that accept a variable number of arguments.

### Basic Variadic Functions

```go
// Variadic function with same type
func sum(numbers ...int) int {
    total := 0
    for _, num := range numbers {
        total += num
    }
    return total
}

// Variadic function with different usage patterns
func printf(format string, args ...interface{}) {
    fmt.Printf(format, args...)
}

// Variadic function with validation
func max(first int, rest ...int) int {
    maximum := first
    for _, num := range rest {
        if num > maximum {
            maximum = num
        }
    }
    return maximum
}

// Usage examples
func demonstrateVariadicFunctions() {
    // Different ways to call variadic functions
    fmt.Println(sum())           // 0
    fmt.Println(sum(1))          // 1
    fmt.Println(sum(1, 2, 3))    // 6
    fmt.Println(sum(1, 2, 3, 4, 5)) // 15

    // Spread slice into variadic function
    numbers := []int{10, 20, 30}
    fmt.Println(sum(numbers...)) // 60

    // Mixed parameters
    fmt.Println(max(5, 1, 9, 3, 7)) // 9

    // Custom printf
    printf("Hello %s, you are %d years old\n", "Alice", 30)
}
```

### Advanced Variadic Patterns

```go
// Variadic function with options pattern
type LogOption func(*LogConfig)

type LogConfig struct {
    Level     string
    Timestamp bool
    Color     bool
    Output    io.Writer
}

func WithLevel(level string) LogOption {
    return func(config *LogConfig) {
        config.Level = level
    }
}

func WithTimestamp(enabled bool) LogOption {
    return func(config *LogConfig) {
        config.Timestamp = enabled
    }
}

func WithColor(enabled bool) LogOption {
    return func(config *LogConfig) {
        config.Color = enabled
    }
}

func WithOutput(output io.Writer) LogOption {
    return func(config *LogConfig) {
        config.Output = output
    }
}

func NewLogger(options ...LogOption) *Logger {
    config := &LogConfig{
        Level:     "INFO",
        Timestamp: true,
        Color:     false,
        Output:    os.Stdout,
    }

    for _, option := range options {
        option(config)
    }

    return &Logger{config: config}
}

// Usage
func demonstrateVariadicOptions() {
    // Default logger
    logger1 := NewLogger()

    // Customized logger
    logger2 := NewLogger(
        WithLevel("DEBUG"),
        WithColor(true),
        WithOutput(os.Stderr),
    )

    _ = logger1
    _ = logger2
}

// Variadic function for building queries
func buildQuery(table string, conditions ...string) string {
    query := "SELECT * FROM " + table

    if len(conditions) > 0 {
        query += " WHERE " + strings.Join(conditions, " AND ")
    }

    return query
}

// Usage
func demonstrateQueryBuilder() {
    query1 := buildQuery("users")
    query2 := buildQuery("users", "age > 18")
    query3 := buildQuery("users", "age > 18", "active = true", "city = 'NYC'")

    fmt.Println(query1) // SELECT * FROM users
    fmt.Println(query2) // SELECT * FROM users WHERE age > 18
    fmt.Println(query3) // SELECT * FROM users WHERE age > 18 AND active = true AND city = 'NYC'
}
```

## Anonymous Functions

Functions without names, also known as lambda functions.

### Basic Anonymous Functions

```go
// Anonymous function assigned to variable
func demonstrateAnonymousFunctions() {
    // Simple anonymous function
    greet := func(name string) string {
        return "Hello, " + name
    }

    fmt.Println(greet("Alice"))

    // Anonymous function with multiple parameters
    add := func(a, b int) int {
        return a + b
    }

    result := add(5, 3)
    fmt.Println(result) // 8

    // Immediately invoked function expression (IIFE)
    result2 := func(x, y int) int {
        return x * y
    }(4, 5)

    fmt.Println(result2) // 20
}
```

### Anonymous Functions in Data Structures

```go
// Map of functions
func demonstrateFunctionMaps() {
    operations := map[string]func(int, int) int{
        "add": func(a, b int) int {
            return a + b
        },
        "subtract": func(a, b int) int {
            return a - b
        },
        "multiply": func(a, b int) int {
            return a * b
        },
        "divide": func(a, b int) int {
            if b != 0 {
                return a / b
            }
            return 0
        },
    }

    // Use the functions
    fmt.Println(operations["add"](10, 5))      // 15
    fmt.Println(operations["multiply"](3, 4))  // 12
}

// Slice of functions
func demonstrateFunctionSlices() {
    validators := []func(string) bool{
        func(s string) bool { return len(s) > 0 },           // Not empty
        func(s string) bool { return len(s) >= 3 },          // Min length
        func(s string) bool { return len(s) <= 50 },         // Max length
        func(s string) bool { return !strings.Contains(s, " ") }, // No spaces
    }

    input := "hello"
    valid := true

    for i, validator := range validators {
        if !validator(input) {
            fmt.Printf("Validation %d failed\n", i+1)
            valid = false
        }
    }

    if valid {
        fmt.Println("All validations passed")
    }
}
```

### Anonymous Functions as Parameters

```go
// Function that accepts anonymous function as parameter
func processNumbers(numbers []int, processor func(int) int) []int {
    result := make([]int, len(numbers))
    for i, num := range numbers {
        result[i] = processor(num)
    }
    return result
}

func filterNumbers(numbers []int, predicate func(int) bool) []int {
    var result []int
    for _, num := range numbers {
        if predicate(num) {
            result = append(result, num)
        }
    }
    return result
}

// Usage
func demonstrateAnonymousAsParameters() {
    numbers := []int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

    // Square all numbers
    squares := processNumbers(numbers, func(n int) int {
        return n * n
    })
    fmt.Println("Squares:", squares)

    // Double all numbers
    doubles := processNumbers(numbers, func(n int) int {
        return n * 2
    })
    fmt.Println("Doubles:", doubles)

    // Filter even numbers
    evens := filterNumbers(numbers, func(n int) bool {
        return n%2 == 0
    })
    fmt.Println("Evens:", evens)

    // Filter numbers greater than 5
    greaterThan5 := filterNumbers(numbers, func(n int) bool {
        return n > 5
    })
    fmt.Println("Greater than 5:", greaterThan5)
}
```

## Closures

Functions that capture and access variables from their surrounding scope.

### Basic Closures

```go
// Simple closure
func createCounter() func() int {
    count := 0
    return func() int {
        count++
        return count
    }
}

// Closure with parameters
func createMultiplier(factor int) func(int) int {
    return func(value int) int {
        return value * factor
    }
}

// Closure capturing multiple variables
func createCalculator(initial int) (func(int), func() int) {
    value := initial

    add := func(n int) {
        value += n
    }

    get := func() int {
        return value
    }

    return add, get
}

// Usage
func demonstrateBasicClosures() {
    // Counter closure
    counter := createCounter()
    fmt.Println(counter()) // 1
    fmt.Println(counter()) // 2
    fmt.Println(counter()) // 3

    // Multiple counters have independent state
    counter2 := createCounter()
    fmt.Println(counter2()) // 1
    fmt.Println(counter())  // 4

    // Multiplier closure
    double := createMultiplier(2)
    triple := createMultiplier(3)

    fmt.Println(double(5)) // 10
    fmt.Println(triple(5)) // 15

    // Calculator closure
    add, get := createCalculator(10)
    add(5)
    add(3)
    fmt.Println(get()) // 18
}
```

### Advanced Closure Patterns

```go
// Closure for configuration
func createValidator(rules map[string]func(string) bool) func(string) (bool, []string) {
    return func(input string) (bool, []string) {
        var errors []string

        for ruleName, rule := range rules {
            if !rule(input) {
                errors = append(errors, ruleName)
            }
        }

        return len(errors) == 0, errors
    }
}

// Closure for caching (memoization)
func memoize[T comparable, R any](fn func(T) R) func(T) R {
    cache := make(map[T]R)

    return func(arg T) R {
        if result, exists := cache[arg]; exists {
            return result
        }

        result := fn(arg)
        cache[arg] = result
        return result
    }
}

// Closure for rate limiting
func createRateLimiter(maxCalls int, duration time.Duration) func() bool {
    calls := make([]time.Time, 0, maxCalls)

    return func() bool {
        now := time.Now()

        // Remove old calls
        for len(calls) > 0 && now.Sub(calls[0]) > duration {
            calls = calls[1:]
        }

        // Check if we can make another call
        if len(calls) >= maxCalls {
            return false
        }

        calls = append(calls, now)
        return true
    }
}

// Usage
func demonstrateAdvancedClosures() {
    // Validator closure
    validator := createValidator(map[string]func(string) bool{
        "not_empty": func(s string) bool { return len(s) > 0 },
        "min_length": func(s string) bool { return len(s) >= 3 },
        "max_length": func(s string) bool { return len(s) <= 20 },
    })

    valid, errors := validator("hi")
    fmt.Printf("Valid: %t, Errors: %v\n", valid, errors)

    // Memoized function
    fibonacci := memoize(func(n int) int {
        if n <= 1 {
            return n
        }
        return fibonacci(n-1) + fibonacci(n-2)
    })

    fmt.Println(fibonacci(10)) // Calculated and cached
    fmt.Println(fibonacci(10)) // Retrieved from cache

    // Rate limiter
    limiter := createRateLimiter(3, time.Second)

    for i := 0; i < 5; i++ {
        if limiter() {
            fmt.Printf("Call %d: Allowed\n", i+1)
        } else {
            fmt.Printf("Call %d: Rate limited\n", i+1)
        }
    }
}
```

### Closure Gotchas

```go
// Common mistake: loop variable capture
func demonstrateClosureGotchas() {
    // Wrong: all closures capture the same variable
    var funcs []func() int

    for i := 0; i < 5; i++ {
        funcs = append(funcs, func() int {
            return i // All closures capture the same 'i'
        })
    }

    for _, f := range funcs {
        fmt.Print(f(), " ") // Prints: 5 5 5 5 5
    }
    fmt.Println()

    // Correct: capture loop variable properly
    var funcs2 []func() int

    for i := 0; i < 5; i++ {
        i := i // Create new variable in loop scope
        funcs2 = append(funcs2, func() int {
            return i
        })
    }

    for _, f := range funcs2 {
        fmt.Print(f(), " ") // Prints: 0 1 2 3 4
    }
    fmt.Println()

    // Alternative: pass as parameter
    var funcs3 []func() int

    for i := 0; i < 5; i++ {
        funcs3 = append(funcs3, func(val int) func() int {
            return func() int {
                return val
            }
        }(i))
    }

    for _, f := range funcs3 {
        fmt.Print(f(), " ") // Prints: 0 1 2 3 4
    }
    fmt.Println()
}
```

## Higher-Order Functions

Functions that take other functions as parameters or return functions.

### Functions as Parameters

```go
// Higher-order function for array operations
func Map[T, R any](slice []T, mapper func(T) R) []R {
    result := make([]R, len(slice))
    for i, item := range slice {
        result[i] = mapper(item)
    }
    return result
}

func Filter[T any](slice []T, predicate func(T) bool) []T {
    var result []T
    for _, item := range slice {
        if predicate(item) {
            result = append(result, item)
        }
    }
    return result
}

func Reduce[T, R any](slice []T, initial R, reducer func(R, T) R) R {
    result := initial
    for _, item := range slice {
        result = reducer(result, item)
    }
    return result
}

func ForEach[T any](slice []T, action func(T)) {
    for _, item := range slice {
        action(item)
    }
}

// Usage
func demonstrateHigherOrderFunctions() {
    numbers := []int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

    // Map: transform each element
    squares := Map(numbers, func(n int) int {
        return n * n
    })
    fmt.Println("Squares:", squares)

    strings := Map(numbers, func(n int) string {
        return fmt.Sprintf("num_%d", n)
    })
    fmt.Println("Strings:", strings)

    // Filter: select elements
    evens := Filter(numbers, func(n int) bool {
        return n%2 == 0
    })
    fmt.Println("Evens:", evens)

    // Reduce: aggregate elements
    sum := Reduce(numbers, 0, func(acc, n int) int {
        return acc + n
    })
    fmt.Println("Sum:", sum)

    product := Reduce(numbers, 1, func(acc, n int) int {
        return acc * n
    })
    fmt.Println("Product:", product)

    // ForEach: side effects
    ForEach(numbers, func(n int) {
        if n%3 == 0 {
            fmt.Printf("%d is divisible by 3\n", n)
        }
    })
}
```

### Functions Returning Functions

```go
// Function factory
func createValidator(minLength int) func(string) bool {
    return func(s string) bool {
        return len(s) >= minLength
    }
}

// Decorator pattern
func withLogging[T any](fn func() T, name string) func() T {
    return func() T {
        fmt.Printf("Calling function: %s\n", name)
        start := time.Now()
        result := fn()
        fmt.Printf("Function %s took: %v\n", name, time.Since(start))
        return result
    }
}

func withRetry[T any](fn func() (T, error), maxRetries int) func() (T, error) {
    return func() (T, error) {
        var result T
        var err error

        for i := 0; i <= maxRetries; i++ {
            result, err = fn()
            if err == nil {
                return result, nil
            }

            if i < maxRetries {
                fmt.Printf("Attempt %d failed: %v, retrying...\n", i+1, err)
                time.Sleep(time.Duration(i+1) * 100 * time.Millisecond)
            }
        }

        return result, fmt.Errorf("failed after %d retries: %w", maxRetries, err)
    }
}

// Composition
func compose[A, B, C any](f func(B) C, g func(A) B) func(A) C {
    return func(a A) C {
        return f(g(a))
    }
}

// Usage
func demonstrateFunctionFactories() {
    // Validator factory
    validateMinLength3 := createValidator(3)
    validateMinLength5 := createValidator(5)

    fmt.Println(validateMinLength3("hi"))    // false
    fmt.Println(validateMinLength3("hello")) // true
    fmt.Println(validateMinLength5("hello")) // true

    // Decorator pattern
    slowFunction := func() string {
        time.Sleep(100 * time.Millisecond)
        return "done"
    }

    loggedFunction := withLogging(slowFunction, "slowFunction")
    result := loggedFunction()
    fmt.Println("Result:", result)

    // Retry decorator
    unreliableFunction := func() (string, error) {
        if rand.Float32() < 0.7 {
            return "", errors.New("random failure")
        }
        return "success", nil
    }

    reliableFunction := withRetry(unreliableFunction, 3)
    result2, err := reliableFunction()
    if err != nil {
        fmt.Printf("Failed: %v\n", err)
    } else {
        fmt.Printf("Success: %s\n", result2)
    }

    // Function composition
    addOne := func(x int) int { return x + 1 }
    double := func(x int) int { return x * 2 }

    addOneThenDouble := compose(double, addOne)
    fmt.Println(addOneThenDouble(5)) // (5 + 1) * 2 = 12
}
```

## Function Types

Defining and using function types for better code organization.

### Basic Function Types

```go
// Define function types
type BinaryOperation func(int, int) int
type Predicate func(int) bool
type Transformer func(string) string
type EventHandler func(Event)

// Using function types
func calculate(a, b int, op BinaryOperation) int {
    return op(a, b)
}

func validateNumber(num int, validator Predicate) bool {
    return validator(num)
}

func processText(text string, transformer Transformer) string {
    return transformer(text)
}

// Function type with multiple signatures
type Processor interface {
    Process(data interface{}) (interface{}, error)
}

type ProcessorFunc func(data interface{}) (interface{}, error)

func (f ProcessorFunc) Process(data interface{}) (interface{}, error) {
    return f(data)
}

// Usage
func demonstrateFunctionTypes() {
    // Binary operations
    add := BinaryOperation(func(a, b int) int { return a + b })
    multiply := BinaryOperation(func(a, b int) int { return a * b })

    fmt.Println(calculate(5, 3, add))      // 8
    fmt.Println(calculate(5, 3, multiply)) // 15

    // Predicates
    isEven := Predicate(func(n int) bool { return n%2 == 0 })
    isPositive := Predicate(func(n int) bool { return n > 0 })

    fmt.Println(validateNumber(4, isEven))     // true
    fmt.Println(validateNumber(-5, isPositive)) // false

    // Transformers
    toUpper := Transformer(strings.ToUpper)
    addPrefix := Transformer(func(s string) string { return "prefix_" + s })

    fmt.Println(processText("hello", toUpper))    // HELLO
    fmt.Println(processText("world", addPrefix))  // prefix_world
}
```

### Advanced Function Types

```go
// Generic function types
type Mapper[T, R any] func(T) R
type Reducer[T, R any] func(R, T) R
type Comparator[T any] func(T, T) int

// Function type with options
type HTTPClientOption func(*HTTPClient)
type MiddlewareFunc func(http.Handler) http.Handler

// Function type for event handling
type EventType string
type EventListener func(EventType, interface{})
type EventEmitter struct {
    listeners map[EventType][]EventListener
}

func (ee *EventEmitter) On(eventType EventType, listener EventListener) {
    if ee.listeners == nil {
        ee.listeners = make(map[EventType][]EventListener)
    }
    ee.listeners[eventType] = append(ee.listeners[eventType], listener)
}

func (ee *EventEmitter) Emit(eventType EventType, data interface{}) {
    if listeners, exists := ee.listeners[eventType]; exists {
        for _, listener := range listeners {
            listener(eventType, data)
        }
    }
}

// Function type for dependency injection
type ServiceFactory func() interface{}
type Container map[string]ServiceFactory

func (c Container) Register(name string, factory ServiceFactory) {
    c[name] = factory
}

func (c Container) Get(name string) interface{} {
    if factory, exists := c[name]; exists {
        return factory()
    }
    return nil
}

// Usage
func demonstrateAdvancedFunctionTypes() {
    // Event emitter
    emitter := &EventEmitter{}

    emitter.On("user_created", func(eventType EventType, data interface{}) {
        fmt.Printf("Event %s: User created with data %v\n", eventType, data)
    })

    emitter.On("user_created", func(eventType EventType, data interface{}) {
        fmt.Printf("Sending welcome email for event %s\n", eventType)
    })

    emitter.Emit("user_created", map[string]interface{}{
        "id":   1,
        "name": "Alice",
    })

    // Dependency injection container
    container := make(Container)

    container.Register("logger", func() interface{} {
        return &Logger{level: "INFO"}
    })

    container.Register("database", func() interface{} {
        return &Database{host: "localhost"}
    })

    logger := container.Get("logger").(*Logger)
    database := container.Get("database").(*Database)

    _ = logger
    _ = database
}
```

## Generic Functions

Functions with type parameters (Go 1.18+).

### Basic Generic Functions

```go
// Generic function with single type parameter
func Max[T comparable](a, b T) T {
    if a > b {
        return a
    }
    return b
}

// Generic function with multiple type parameters
func Pair[T, U any](first T, second U) struct {
    First  T
    Second U
} {
    return struct {
        First  T
        Second U
    }{First: first, Second: second}
}

// Generic function with constraints
func Sum[T ~int | ~float64](values []T) T {
    var sum T
    for _, v := range values {
        sum += v
    }
    return sum
}

// Generic function with interface constraint
func Stringify[T fmt.Stringer](value T) string {
    return value.String()
}

// Usage
func demonstrateBasicGenerics() {
    // Max function with different types
    fmt.Println(Max(5, 3))           // 5
    fmt.Println(Max(3.14, 2.71))     // 3.14
    fmt.Println(Max("hello", "world")) // world

    // Pair function
    intStringPair := Pair(42, "answer")
    fmt.Printf("Pair: %+v\n", intStringPair)

    boolFloatPair := Pair(true, 3.14)
    fmt.Printf("Pair: %+v\n", boolFloatPair)

    // Sum function
    intSum := Sum([]int{1, 2, 3, 4, 5})
    fmt.Printf("Int sum: %d\n", intSum)

    floatSum := Sum([]float64{1.1, 2.2, 3.3})
    fmt.Printf("Float sum: %.2f\n", floatSum)
}
```

### Advanced Generic Patterns

```go
// Generic data structures
type Stack[T any] struct {
    items []T
}

func NewStack[T any]() *Stack[T] {
    return &Stack[T]{items: make([]T, 0)}
}

func (s *Stack[T]) Push(item T) {
    s.items = append(s.items, item)
}

func (s *Stack[T]) Pop() (T, bool) {
    if len(s.items) == 0 {
        var zero T
        return zero, false
    }

    index := len(s.items) - 1
    item := s.items[index]
    s.items = s.items[:index]
    return item, true
}

func (s *Stack[T]) IsEmpty() bool {
    return len(s.items) == 0
}

// Generic algorithms
func BinarySearch[T comparable](slice []T, target T, compare func(T, T) int) int {
    left, right := 0, len(slice)-1

    for left <= right {
        mid := (left + right) / 2
        cmp := compare(slice[mid], target)

        if cmp == 0 {
            return mid
        } else if cmp < 0 {
            left = mid + 1
        } else {
            right = mid - 1
        }
    }

    return -1
}

func Sort[T any](slice []T, less func(T, T) bool) {
    // Simple bubble sort for demonstration
    n := len(slice)
    for i := 0; i < n; i++ {
        for j := 0; j < n-i-1; j++ {
            if less(slice[j+1], slice[j]) {
                slice[j], slice[j+1] = slice[j+1], slice[j]
            }
        }
    }
}

// Generic function composition
func Compose[A, B, C any](f func(B) C, g func(A) B) func(A) C {
    return func(a A) C {
        return f(g(a))
    }
}

func Pipe[T any](value T, functions ...func(T) T) T {
    result := value
    for _, fn := range functions {
        result = fn(result)
    }
    return result
}

// Usage
func demonstrateAdvancedGenerics() {
    // Generic stack
    intStack := NewStack[int]()
    intStack.Push(1)
    intStack.Push(2)
    intStack.Push(3)

    for !intStack.IsEmpty() {
        if value, ok := intStack.Pop(); ok {
            fmt.Printf("Popped: %d\n", value)
        }
    }

    // Generic binary search
    numbers := []int{1, 3, 5, 7, 9, 11, 13}
    index := BinarySearch(numbers, 7, func(a, b int) int {
        if a < b {
            return -1
        } else if a > b {
            return 1
        }
        return 0
    })
    fmt.Printf("Found 7 at index: %d\n", index)

    // Generic sort
    words := []string{"banana", "apple", "cherry", "date"}
    Sort(words, func(a, b string) bool {
        return a < b
    })
    fmt.Printf("Sorted words: %v\n", words)

    // Function composition
    addOne := func(x int) int { return x + 1 }
    double := func(x int) int { return x * 2 }
    toString := func(x int) string { return fmt.Sprintf("%d", x) }

    composed := Compose(toString, Compose(double, addOne))
    result := composed(5) // (5 + 1) * 2 = 12, then "12"
    fmt.Printf("Composed result: %s\n", result)

    // Function pipeline
    pipeResult := Pipe(5,
        func(x int) int { return x + 1 },  // 6
        func(x int) int { return x * 2 },  // 12
        func(x int) int { return x - 3 },  // 9
    )
    fmt.Printf("Pipe result: %d\n", pipeResult)
}
```

## Recursive Functions

Functions that call themselves to solve problems.

### Basic Recursion

```go
// Classic recursive functions
func factorial(n int) int {
    if n <= 1 {
        return 1
    }
    return n * factorial(n-1)
}

func fibonacci(n int) int {
    if n <= 1 {
        return n
    }
    return fibonacci(n-1) + fibonacci(n-2)
}

func power(base, exponent int) int {
    if exponent == 0 {
        return 1
    }
    if exponent == 1 {
        return base
    }
    return base * power(base, exponent-1)
}

// Recursive string operations
func reverse(s string) string {
    if len(s) <= 1 {
        return s
    }
    return reverse(s[1:]) + string(s[0])
}

func isPalindrome(s string) bool {
    if len(s) <= 1 {
        return true
    }
    if s[0] != s[len(s)-1] {
        return false
    }
    return isPalindrome(s[1 : len(s)-1])
}

// Usage
func demonstrateBasicRecursion() {
    fmt.Printf("Factorial of 5: %d\n", factorial(5))     // 120
    fmt.Printf("Fibonacci of 8: %d\n", fibonacci(8))     // 21
    fmt.Printf("2^10: %d\n", power(2, 10))               // 1024
    fmt.Printf("Reverse 'hello': %s\n", reverse("hello")) // olleh
    fmt.Printf("Is 'racecar' palindrome: %t\n", isPalindrome("racecar")) // true
}
```

### Advanced Recursive Patterns

```go
// Tail recursion (Go doesn't optimize, but good pattern)
func factorialTail(n, acc int) int {
    if n <= 1 {
        return acc
    }
    return factorialTail(n-1, n*acc)
}

func fibonacciTail(n, a, b int) int {
    if n == 0 {
        return a
    }
    return fibonacciTail(n-1, b, a+b)
}

// Memoized recursion
func fibonacciMemo(n int, memo map[int]int) int {
    if n <= 1 {
        return n
    }

    if result, exists := memo[n]; exists {
        return result
    }

    result := fibonacciMemo(n-1, memo) + fibonacciMemo(n-2, memo)
    memo[n] = result
    return result
}

// Tree traversal
type TreeNode struct {
    Value int
    Left  *TreeNode
    Right *TreeNode
}

func (node *TreeNode) InorderTraversal(visit func(int)) {
    if node == nil {
        return
    }

    node.Left.InorderTraversal(visit)
    visit(node.Value)
    node.Right.InorderTraversal(visit)
}

func (node *TreeNode) Height() int {
    if node == nil {
        return 0
    }

    leftHeight := node.Left.Height()
    rightHeight := node.Right.Height()

    if leftHeight > rightHeight {
        return leftHeight + 1
    }
    return rightHeight + 1
}

// Directory traversal
func walkDirectory(path string, visit func(string)) error {
    entries, err := os.ReadDir(path)
    if err != nil {
        return err
    }

    for _, entry := range entries {
        fullPath := filepath.Join(path, entry.Name())
        visit(fullPath)

        if entry.IsDir() {
            if err := walkDirectory(fullPath, visit); err != nil {
                return err
            }
        }
    }

    return nil
}

// Usage
func demonstrateAdvancedRecursion() {
    // Tail recursion
    fmt.Printf("Factorial tail 5: %d\n", factorialTail(5, 1))
    fmt.Printf("Fibonacci tail 8: %d\n", fibonacciTail(8, 0, 1))

    // Memoized recursion
    memo := make(map[int]int)
    fmt.Printf("Fibonacci memo 40: %d\n", fibonacciMemo(40, memo))

    // Tree operations
    root := &TreeNode{
        Value: 4,
        Left: &TreeNode{
            Value: 2,
            Left:  &TreeNode{Value: 1},
            Right: &TreeNode{Value: 3},
        },
        Right: &TreeNode{
            Value: 6,
            Left:  &TreeNode{Value: 5},
            Right: &TreeNode{Value: 7},
        },
    }

    fmt.Print("Inorder traversal: ")
    root.InorderTraversal(func(value int) {
        fmt.Printf("%d ", value)
    })
    fmt.Println()

    fmt.Printf("Tree height: %d\n", root.Height())
}
```

## Best Practices

Guidelines for writing effective and maintainable functions in Go.

### Function Design Principles

```go
// 1. Single Responsibility Principle
// Bad: Function doing too many things
func badProcessUser(userData string) error {
    // Parse user data
    parts := strings.Split(userData, ",")
    if len(parts) != 3 {
        return errors.New("invalid format")
    }

    // Validate email
    email := parts[1]
    if !strings.Contains(email, "@") {
        return errors.New("invalid email")
    }

    // Save to database
    // ... database code

    // Send welcome email
    // ... email code

    // Log activity
    // ... logging code

    return nil
}

// Good: Separate functions for each responsibility
func parseUserData(userData string) (string, string, int, error) {
    parts := strings.Split(userData, ",")
    if len(parts) != 3 {
        return "", "", 0, errors.New("invalid format")
    }

    name := strings.TrimSpace(parts[0])
    email := strings.TrimSpace(parts[1])
    age, err := strconv.Atoi(strings.TrimSpace(parts[2]))

    return name, email, age, err
}

func validateEmail(email string) error {
    if !strings.Contains(email, "@") {
        return errors.New("invalid email format")
    }
    return nil
}

func saveUser(user User) error {
    // Database save logic
    return nil
}

func sendWelcomeEmail(email string) error {
    // Email sending logic
    return nil
}

func logUserActivity(userID int, activity string) {
    // Logging logic
}

// Composed function
func processUser(userData string) error {
    name, email, age, err := parseUserData(userData)
    if err != nil {
        return fmt.Errorf("parse error: %w", err)
    }

    if err := validateEmail(email); err != nil {
        return fmt.Errorf("validation error: %w", err)
    }

    user := User{Name: name, Email: email, Age: age}
    if err := saveUser(user); err != nil {
        return fmt.Errorf("save error: %w", err)
    }

    if err := sendWelcomeEmail(email); err != nil {
        // Log error but don't fail the whole operation
        logUserActivity(user.ID, "welcome_email_failed")
    }

    logUserActivity(user.ID, "user_created")
    return nil
}
```

### Error Handling Best Practices

```go
// 1. Return errors, don't panic
// Bad: Using panic for normal error conditions
func badDivide(a, b float64) float64 {
    if b == 0 {
        panic("division by zero") // Don't do this
    }
    return a / b
}

// Good: Return error
func goodDivide(a, b float64) (float64, error) {
    if b == 0 {
        return 0, errors.New("division by zero")
    }
    return a / b, nil
}

// 2. Wrap errors with context
func processFile(filename string) error {
    file, err := os.Open(filename)
    if err != nil {
        return fmt.Errorf("failed to open file %s: %w", filename, err)
    }
    defer file.Close()

    data, err := io.ReadAll(file)
    if err != nil {
        return fmt.Errorf("failed to read file %s: %w", filename, err)
    }

    if err := validateData(data); err != nil {
        return fmt.Errorf("invalid data in file %s: %w", filename, err)
    }

    return nil
}

// 3. Use custom error types for specific cases
type ValidationError struct {
    Field   string
    Message string
}

func (e ValidationError) Error() string {
    return fmt.Sprintf("validation failed for field %s: %s", e.Field, e.Message)
}

func validateUser(user User) error {
    if user.Name == "" {
        return ValidationError{Field: "name", Message: "cannot be empty"}
    }

    if user.Age < 0 {
        return ValidationError{Field: "age", Message: "cannot be negative"}
    }

    return nil
}
```

### Function Naming and Documentation

```go
// 1. Use clear, descriptive names
// Bad: Unclear names
func calc(x, y int) int { return x + y }
func proc(data []byte) error { /* ... */ return nil }
func get(id int) interface{} { /* ... */ return nil }

// Good: Clear names
func calculateSum(a, b int) int { return a + b }
func processImageData(imageBytes []byte) error { /* ... */ return nil }
func getUserByID(userID int) (*User, error) { /* ... */ return nil, nil }

// 2. Use consistent naming patterns
// Repository pattern
func (r *UserRepository) CreateUser(user *User) error { /* ... */ return nil }
func (r *UserRepository) GetUserByID(id int) (*User, error) { /* ... */ return nil, nil }
func (r *UserRepository) UpdateUser(user *User) error { /* ... */ return nil }
func (r *UserRepository) DeleteUser(id int) error { /* ... */ return nil }

// Service pattern
func (s *UserService) RegisterUser(userData UserRegistration) (*User, error) { /* ... */ return nil, nil }
func (s *UserService) AuthenticateUser(email, password string) (*User, error) { /* ... */ return nil, nil }
func (s *UserService) DeactivateUser(userID int) error { /* ... */ return nil }

// 3. Document public functions
// CalculateDistance calculates the Euclidean distance between two points.
// It returns the distance as a float64 value.
// If either point is nil, it returns 0.
func CalculateDistance(p1, p2 *Point) float64 {
    if p1 == nil || p2 == nil {
        return 0
    }

    dx := p1.X - p2.X
    dy := p1.Y - p2.Y
    return math.Sqrt(dx*dx + dy*dy)
}

// ProcessBatch processes a batch of items concurrently.
// It returns a slice of results in the same order as the input items.
// The maxConcurrency parameter limits the number of concurrent goroutines.
// If maxConcurrency is 0 or negative, it defaults to runtime.NumCPU().
func ProcessBatch[T, R any](items []T, processor func(T) R, maxConcurrency int) []R {
    if maxConcurrency <= 0 {
        maxConcurrency = runtime.NumCPU()
    }

    // Implementation...
    return nil
}
```

### Function Parameters and Return Values

```go
// 1. Limit number of parameters
// Bad: Too many parameters
func createUser(firstName, lastName, email, phone, address, city, state, zip, country string, age int, isActive bool) *User {
    // Implementation
    return nil
}

// Good: Use struct for multiple related parameters
type UserInfo struct {
    FirstName string
    LastName  string
    Email     string
    Phone     string
    Address   Address
    Age       int
    IsActive  bool
}

type Address struct {
    Street  string
    City    string
    State   string
    Zip     string
    Country string
}

func createUser(info UserInfo) *User {
    // Implementation
    return nil
}

// 2. Use options pattern for optional parameters
type ServerOption func(*ServerConfig)

func WithPort(port int) ServerOption {
    return func(config *ServerConfig) {
        config.Port = port
    }
}

func WithTimeout(timeout time.Duration) ServerOption {
    return func(config *ServerConfig) {
        config.Timeout = timeout
    }
}

func NewServer(options ...ServerOption) *Server {
    config := &ServerConfig{
        Port:    8080,
        Timeout: 30 * time.Second,
    }

    for _, option := range options {
        option(config)
    }

    return &Server{config: config}
}

// Usage
server := NewServer(
    WithPort(9090),
    WithTimeout(60 * time.Second),
)

// 3. Return errors as the last value
// Good: Error as last return value
func fetchUser(id int) (*User, error) {
    // Implementation
    return nil, nil
}

func processData(data []byte) ([]Result, int, error) {
    // Implementation
    return nil, 0, nil
}

// 4. Use named returns for clarity (sparingly)
func divideWithRemainder(dividend, divisor int) (quotient, remainder int, err error) {
    if divisor == 0 {
        err = errors.New("division by zero")
        return
    }

    quotient = dividend / divisor
    remainder = dividend % divisor
    return
}
```

## Performance Considerations

Understanding the performance implications of different function patterns.

### Function Call Overhead

```go
import "testing"

// Direct function call
func directAdd(a, b int) int {
    return a + b
}

// Function variable call
var funcVarAdd = func(a, b int) int {
    return a + b
}

// Interface method call
type Adder interface {
    Add(a, b int) int
}

type SimpleAdder struct{}

func (sa SimpleAdder) Add(a, b int) int {
    return a + b
}

// Benchmarks
func BenchmarkDirectCall(b *testing.B) {
    for i := 0; i < b.N; i++ {
        directAdd(5, 3)
    }
}

func BenchmarkFunctionVariable(b *testing.B) {
    for i := 0; i < b.N; i++ {
        funcVarAdd(5, 3)
    }
}

func BenchmarkInterfaceCall(b *testing.B) {
    var adder Adder = SimpleAdder{}

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        adder.Add(5, 3)
    }
}

// Results show: Direct < Function Variable < Interface
```

### Memory Allocation Patterns

```go
// 1. Avoid unnecessary allocations
// Bad: Creates new slice on each call
func badFilter(numbers []int, predicate func(int) bool) []int {
    var result []int // Starts with zero capacity
    for _, num := range numbers {
        if predicate(num) {
            result = append(result, num) // May cause multiple reallocations
        }
    }
    return result
}

// Good: Pre-allocate with estimated capacity
func goodFilter(numbers []int, predicate func(int) bool) []int {
    result := make([]int, 0, len(numbers)/2) // Estimate capacity
    for _, num := range numbers {
        if predicate(num) {
            result = append(result, num)
        }
    }
    return result
}

// Better: Reuse slice when possible
func filterInPlace(numbers []int, predicate func(int) bool) []int {
    writeIndex := 0
    for _, num := range numbers {
        if predicate(num) {
            numbers[writeIndex] = num
            writeIndex++
        }
    }
    return numbers[:writeIndex]
}

// 2. Use object pools for frequently allocated objects
var bufferPool = sync.Pool{
    New: func() interface{} {
        return make([]byte, 0, 1024)
    },
}

func processWithPool(data []byte) []byte {
    buf := bufferPool.Get().([]byte)
    defer bufferPool.Put(buf[:0]) // Reset length but keep capacity

    // Use buf for processing
    buf = append(buf, data...)
    // ... process buf

    // Return copy since we're returning the buffer to pool
    result := make([]byte, len(buf))
    copy(result, buf)
    return result
}
```

### Closure Performance

```go
// Closure capture can affect performance
func createCounters(n int) []func() int {
    counters := make([]func() int, n)

    // Each closure captures its own variable
    for i := 0; i < n; i++ {
        count := 0 // Each iteration creates new variable
        counters[i] = func() int {
            count++
            return count
        }
    }

    return counters
}

// More memory-efficient approach
type Counter struct {
    count int
}

func (c *Counter) Increment() int {
    c.count++
    return c.count
}

func createCountersEfficient(n int) []*Counter {
    counters := make([]*Counter, n)
    for i := 0; i < n; i++ {
        counters[i] = &Counter{}
    }
    return counters
}

// Benchmark comparison
func BenchmarkClosureCounters(b *testing.B) {
    counters := createCounters(1000)

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        for _, counter := range counters {
            counter()
        }
    }
}

func BenchmarkStructCounters(b *testing.B) {
    counters := createCountersEfficient(1000)

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        for _, counter := range counters {
            counter.Increment()
        }
    }
}
```

### Inlining and Optimization

```go
// Small functions are candidates for inlining
func isEven(n int) bool {
    return n%2 == 0
}

func isOdd(n int) bool {
    return n%2 != 0
}

// Complex functions won't be inlined
func complexCalculation(data []float64) float64 {
    // Many operations, loops, function calls
    sum := 0.0
    for i, v := range data {
        sum += math.Sin(v) * math.Cos(float64(i))
    }
    return sum / float64(len(data))
}

// Use build tags to check inlining
// go build -gcflags="-m" to see inlining decisions

// Hot path optimization
func processHotPath(data []int) int {
    sum := 0

    // Unroll small loops for better performance
    for i := 0; i < len(data); i += 4 {
        if i+3 < len(data) {
            sum += data[i] + data[i+1] + data[i+2] + data[i+3]
        } else {
            // Handle remaining elements
            for j := i; j < len(data); j++ {
                sum += data[j]
            }
            break
        }
    }

    return sum
}
```

This comprehensive guide covers all aspects of functions in Go, from basic syntax to advanced patterns like generics, closures, and performance optimization. Understanding these concepts will help you write more effective, maintainable, and performant Go code.
