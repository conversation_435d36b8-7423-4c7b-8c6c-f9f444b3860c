# Structs in Go

This comprehensive guide covers structs in Go, including their definition, initialization, methods, embedding, and advanced patterns for building robust applications.

## Table of Contents

1. [Overview](#overview)
2. [Struct Definition and Declaration](#struct-definition-and-declaration)
3. [Struct Initialization](#struct-initialization)
4. [Accessing Struct Fields](#accessing-struct-fields)
5. [Methods](#methods)
6. [Struct Embedding](#struct-embedding)
7. [Struct Tags](#struct-tags)
8. [Anonymous Structs](#anonymous-structs)
9. [Struct Comparison](#struct-comparison)
10. [Memory Layout and Alignment](#memory-layout-and-alignment)
11. [Advanced Patterns](#advanced-patterns)
12. [Best Practices](#best-practices)
13. [Common Mistakes](#common-mistakes)
14. [Performance Considerations](#performance-considerations)

## Overview

Structs are Go's way of creating custom types that group related data together. They are the foundation of Go's type system and enable object-oriented programming patterns without classes.

### Key Characteristics

```go
// Structs are value types
// Structs can have methods
// Structs support embedding (composition)
// Structs are comparable if all fields are comparable
// Zero value of struct has zero values for all fields
```

### Structs vs Other Languages

| Feature | Go Structs | Classes (OOP) |
|---------|------------|---------------|
| **Inheritance** | Embedding (composition) | Class inheritance |
| **Encapsulation** | Package-level | Private/public/protected |
| **Polymorphism** | Interfaces | Virtual methods |
| **Memory** | Value or pointer | Usually reference |
| **Methods** | Attached to types | Inside classes |

## Struct Definition and Declaration

Multiple ways to define and declare structs in Go.

### Basic Struct Definition

```go
// Simple struct definition
type Person struct {
    Name string
    Age  int
}

// Struct with various field types
type User struct {
    ID       int
    Username string
    Email    string
    IsActive bool
    Balance  float64
    Tags     []string
    Metadata map[string]interface{}
}

// Empty struct
type Empty struct{}

// Struct with function fields
type Calculator struct {
    Add      func(int, int) int
    Subtract func(int, int) int
}

// Struct with channel fields
type EventHandler struct {
    Events   chan Event
    Errors   chan error
    Done     chan bool
}
```

### Nested Structs

```go
// Struct containing other structs
type Address struct {
    Street  string
    City    string
    Country string
    ZipCode string
}

type Company struct {
    Name    string
    Address Address  // Nested struct
}

type Employee struct {
    ID      int
    Name    string
    Company Company  // Nested struct
    Home    Address  // Another nested struct
}

// Struct with slice of structs
type Team struct {
    Name      string
    Members   []Employee
    Manager   Employee
    Budget    float64
}
```

### Struct with Pointers

```go
// Struct with pointer fields
type Node struct {
    Value int
    Next  *Node  // Pointer to another Node
    Prev  *Node  // Pointer to previous Node
}

// Binary tree node
type TreeNode struct {
    Value int
    Left  *TreeNode
    Right *TreeNode
}

// Struct with pointer to avoid copying
type LargeStruct struct {
    Data [1000]int
}

type Container struct {
    Large *LargeStruct  // Pointer to avoid copying large data
}
```

## Struct Initialization

Various ways to create and initialize struct instances.

### Zero Value Initialization

```go
// Zero value initialization
var p Person
// p.Name = "" (zero value of string)
// p.Age = 0   (zero value of int)

var u User
// All fields get their zero values
fmt.Printf("%+v\n", u)  // {ID:0 Username: Email: IsActive:false Balance:0 Tags:[] Metadata:map[]}

// Zero value is useful
var node Node
// node.Value = 0, node.Next = nil, node.Prev = nil
```

### Struct Literals

```go
// Struct literal with field names
p1 := Person{
    Name: "Alice",
    Age:  30,
}

// Struct literal without field names (positional)
p2 := Person{"Bob", 25}

// Partial initialization
p3 := Person{
    Name: "Charlie",
    // Age will be 0 (zero value)
}

// Complex struct initialization
u1 := User{
    ID:       1,
    Username: "alice",
    Email:    "<EMAIL>",
    IsActive: true,
    Balance:  100.50,
    Tags:     []string{"admin", "user"},
    Metadata: map[string]interface{}{
        "created": "2023-01-01",
        "role":    "administrator",
    },
}
```

### Using new() and make()

```go
// Using new() - returns pointer to zero value
p := new(Person)
// p is *Person pointing to zero-valued Person
p.Name = "David"
p.Age = 35

// Equivalent to:
p2 := &Person{}

// For structs with slices/maps, you might need make()
type Config struct {
    Settings map[string]string
    Values   []int
}

c := &Config{
    Settings: make(map[string]string),
    Values:   make([]int, 0, 10),
}
```

### Constructor Functions

```go
// Constructor function pattern
func NewPerson(name string, age int) *Person {
    return &Person{
        Name: name,
        Age:  age,
    }
}

// Constructor with validation
func NewUser(username, email string) (*User, error) {
    if username == "" {
        return nil, errors.New("username cannot be empty")
    }
    if !isValidEmail(email) {
        return nil, errors.New("invalid email format")
    }

    return &User{
        ID:       generateID(),
        Username: username,
        Email:    email,
        IsActive: true,
        Balance:  0.0,
        Tags:     make([]string, 0),
        Metadata: make(map[string]interface{}),
    }, nil
}

// Constructor with options pattern
type UserOption func(*User)

func WithBalance(balance float64) UserOption {
    return func(u *User) {
        u.Balance = balance
    }
}

func WithTags(tags ...string) UserOption {
    return func(u *User) {
        u.Tags = append(u.Tags, tags...)
    }
}

func NewUserWithOptions(username, email string, opts ...UserOption) (*User, error) {
    user, err := NewUser(username, email)
    if err != nil {
        return nil, err
    }

    for _, opt := range opts {
        opt(user)
    }

    return user, nil
}

// Usage
user, err := NewUserWithOptions(
    "alice",
    "<EMAIL>",
    WithBalance(100.0),
    WithTags("admin", "premium"),
)
```

## Accessing Struct Fields

Different ways to access and modify struct fields.

### Direct Field Access

```go
p := Person{Name: "Alice", Age: 30}

// Read fields
name := p.Name
age := p.Age

// Write fields
p.Name = "Alice Smith"
p.Age = 31

// Access nested fields
emp := Employee{
    Name: "John",
    Company: Company{
        Name: "TechCorp",
        Address: Address{
            City:    "New York",
            Country: "USA",
        },
    },
}

// Access nested fields
companyName := emp.Company.Name
city := emp.Company.Address.City

// Modify nested fields
emp.Company.Address.ZipCode = "10001"
```

### Pointer Field Access

```go
// Automatic dereferencing
p := &Person{Name: "Bob", Age: 25}

// Go automatically dereferences pointers
name := p.Name        // Equivalent to (*p).Name
p.Age = 26           // Equivalent to (*p).Age = 26

// Explicit dereferencing (optional)
name2 := (*p).Name
(*p).Age = 27

// Nil pointer safety
var p2 *Person
// p2.Name = "Charlie"  // Panic: runtime error: invalid memory address
if p2 != nil {
    p2.Name = "Charlie"
}
```

### Field Access Patterns

```go
// Safe field access with nil checks
func GetUserEmail(u *User) string {
    if u == nil {
        return ""
    }
    return u.Email
}

// Field modification with validation
func SetUserAge(u *User, age int) error {
    if u == nil {
        return errors.New("user is nil")
    }
    if age < 0 || age > 150 {
        return errors.New("invalid age")
    }
    u.Age = age
    return nil
}

// Bulk field update
func UpdateUser(u *User, updates map[string]interface{}) error {
    if u == nil {
        return errors.New("user is nil")
    }

    for field, value := range updates {
        switch field {
        case "name":
            if name, ok := value.(string); ok {
                u.Name = name
            }
        case "age":
            if age, ok := value.(int); ok {
                u.Age = age
            }
        case "email":
            if email, ok := value.(string); ok {
                u.Email = email
            }
        default:
            return fmt.Errorf("unknown field: %s", field)
        }
    }

    return nil
}
```

## Methods

Methods are functions with a receiver that can be attached to structs.

### Method Definition

```go
// Method with value receiver
func (p Person) String() string {
    return fmt.Sprintf("%s (%d years old)", p.Name, p.Age)
}

// Method with pointer receiver
func (p *Person) SetAge(age int) {
    p.Age = age
}

func (p *Person) HaveBirthday() {
    p.Age++
}

// Method that returns values
func (p Person) IsAdult() bool {
    return p.Age >= 18
}

func (p Person) GetInfo() (string, int) {
    return p.Name, p.Age
}

// Usage
p := Person{Name: "Alice", Age: 17}
fmt.Println(p.String())     // Alice (17 years old)
fmt.Println(p.IsAdult())    // false

p.SetAge(18)
fmt.Println(p.IsAdult())    // true

p.HaveBirthday()
fmt.Println(p.Age)          // 19
```

### Value vs Pointer Receivers

```go
type Counter struct {
    count int
}

// Value receiver - receives a copy
func (c Counter) GetCount() int {
    return c.count
}

// Value receiver - modification doesn't affect original
func (c Counter) IncrementCopy() {
    c.count++  // Only modifies the copy
}

// Pointer receiver - can modify original
func (c *Counter) Increment() {
    c.count++
}

func (c *Counter) Reset() {
    c.count = 0
}

// Usage
counter := Counter{count: 5}

fmt.Println(counter.GetCount())  // 5

counter.IncrementCopy()
fmt.Println(counter.GetCount())  // Still 5 (copy was modified)

counter.Increment()
fmt.Println(counter.GetCount())  // 6 (original was modified)

// Go automatically handles pointer/value conversion
counterPtr := &Counter{count: 10}
fmt.Println(counterPtr.GetCount())  // Works with pointer
counterPtr.Increment()              // Works with pointer
```

### Method Sets and Interface Implementation

```go
// Interface definition
type Stringer interface {
    String() string
}

type Incrementer interface {
    Increment()
}

// Struct with methods
type Score struct {
    value int
}

func (s Score) String() string {
    return fmt.Sprintf("Score: %d", s.value)
}

func (s *Score) Increment() {
    s.value++
}

// Method sets:
// Score implements Stringer (value receiver)
// *Score implements both Stringer and Incrementer

func demonstrateMethodSets() {
    score := Score{value: 10}
    scorePtr := &Score{value: 20}

    // Both can be used as Stringer
    var s1 Stringer = score
    var s2 Stringer = scorePtr

    fmt.Println(s1.String())  // Score: 10
    fmt.Println(s2.String())  // Score: 20

    // Only pointer can be used as Incrementer
    var inc Incrementer = scorePtr
    inc.Increment()

    // This would not compile:
    // var inc2 Incrementer = score  // Error: Score does not implement Incrementer
}
```

### Advanced Method Patterns

```go
// Method chaining
type Builder struct {
    parts []string
}

func (b *Builder) Add(part string) *Builder {
    b.parts = append(b.parts, part)
    return b
}

func (b *Builder) AddIf(condition bool, part string) *Builder {
    if condition {
        b.parts = append(b.parts, part)
    }
    return b
}

func (b *Builder) Build() string {
    return strings.Join(b.parts, " ")
}

// Usage
result := (&Builder{}).
    Add("Hello").
    Add("World").
    AddIf(true, "!").
    Build()
fmt.Println(result)  // Hello World !

// Method with multiple return values
type Calculator struct {
    history []string
}

func (c *Calculator) Divide(a, b float64) (float64, error) {
    if b == 0 {
        return 0, errors.New("division by zero")
    }

    result := a / b
    c.history = append(c.history, fmt.Sprintf("%.2f / %.2f = %.2f", a, b, result))
    return result, nil
}

func (c Calculator) GetHistory() []string {
    return c.history
}

// Variadic methods
func (c *Calculator) Sum(numbers ...float64) float64 {
    var sum float64
    for _, num := range numbers {
        sum += num
    }

    c.history = append(c.history, fmt.Sprintf("Sum of %v = %.2f", numbers, sum))
    return sum
}
```

## Struct Embedding

Go's composition mechanism that provides inheritance-like behavior.

### Basic Embedding

```go
// Base struct
type Animal struct {
    Name    string
    Species string
    Age     int
}

func (a Animal) Speak() string {
    return fmt.Sprintf("%s makes a sound", a.Name)
}

func (a Animal) GetAge() int {
    return a.Age
}

// Embedded struct
type Dog struct {
    Animal  // Embedded field
    Breed   string
    IsGoodBoy bool
}

func (d Dog) Speak() string {
    return fmt.Sprintf("%s barks", d.Name)
}

func (d Dog) Fetch() string {
    return fmt.Sprintf("%s fetches the ball", d.Name)
}

// Usage
dog := Dog{
    Animal: Animal{
        Name:    "Buddy",
        Species: "Canine",
        Age:     3,
    },
    Breed:     "Golden Retriever",
    IsGoodBoy: true,
}

// Access embedded fields directly
fmt.Println(dog.Name)     // Buddy (from embedded Animal)
fmt.Println(dog.Breed)    // Golden Retriever

// Call embedded methods
fmt.Println(dog.GetAge()) // 3 (from embedded Animal)

// Call overridden methods
fmt.Println(dog.Speak())  // Buddy barks (Dog's method)

// Call embedded method explicitly
fmt.Println(dog.Animal.Speak())  // Buddy makes a sound (Animal's method)

// Call Dog-specific methods
fmt.Println(dog.Fetch())  // Buddy fetches the ball
```

### Multiple Embedding

```go
// Multiple embedded structs
type Flyable struct {
    MaxAltitude int
}

func (f Flyable) Fly() string {
    return fmt.Sprintf("Flying at max altitude %d feet", f.MaxAltitude)
}

type Swimmable struct {
    MaxDepth int
}

func (s Swimmable) Swim() string {
    return fmt.Sprintf("Swimming at max depth %d feet", s.MaxDepth)
}

// Struct with multiple embeddings
type Duck struct {
    Animal
    Flyable
    Swimmable
}

func (d Duck) Speak() string {
    return fmt.Sprintf("%s quacks", d.Name)
}

// Usage
duck := Duck{
    Animal: Animal{
        Name:    "Donald",
        Species: "Duck",
        Age:     2,
    },
    Flyable: Flyable{
        MaxAltitude: 1000,
    },
    Swimmable: Swimmable{
        MaxDepth: 10,
    },
}

fmt.Println(duck.Name)    // Donald
fmt.Println(duck.Speak()) // Donald quacks
fmt.Println(duck.Fly())   // Flying at max altitude 1000 feet
fmt.Println(duck.Swim())  // Swimming at max depth 10 feet
```

### Embedding Interfaces

```go
// Interface embedding
type Reader interface {
    Read([]byte) (int, error)
}

type Writer interface {
    Write([]byte) (int, error)
}

type ReadWriter interface {
    Reader  // Embedded interface
    Writer  // Embedded interface
}

// Struct embedding interfaces
type FileHandler struct {
    ReadWriter  // Embedded interface
    filename    string
}

func NewFileHandler(rw ReadWriter, filename string) *FileHandler {
    return &FileHandler{
        ReadWriter: rw,
        filename:   filename,
    }
}

func (fh *FileHandler) Process() error {
    data := make([]byte, 1024)
    n, err := fh.Read(data)  // Uses embedded ReadWriter
    if err != nil {
        return err
    }

    _, err = fh.Write(data[:n])  // Uses embedded ReadWriter
    return err
}
```

### Embedding Gotchas

```go
// Name conflicts
type A struct {
    Name string
}

func (a A) Method() string {
    return "A's method"
}

type B struct {
    Name string
}

func (b B) Method() string {
    return "B's method"
}

type C struct {
    A
    B
}

func demonstrateConflicts() {
    c := C{
        A: A{Name: "A's name"},
        B: B{Name: "B's name"},
    }

    // Ambiguous access - won't compile
    // fmt.Println(c.Name)    // Error: ambiguous selector c.Name
    // fmt.Println(c.Method()) // Error: ambiguous selector c.Method

    // Must be explicit
    fmt.Println(c.A.Name)    // A's name
    fmt.Println(c.B.Name)    // B's name
    fmt.Println(c.A.Method()) // A's method
    fmt.Println(c.B.Method()) // B's method
}

// Promoted fields and methods
type Base struct {
    ID   int
    Name string
}

func (b Base) String() string {
    return fmt.Sprintf("Base{ID: %d, Name: %s}", b.ID, b.Name)
}

type Derived struct {
    Base
    Extra string
}

func demonstratePromotion() {
    d := Derived{
        Base: Base{
            ID:   1,
            Name: "test",
        },
        Extra: "extra",
    }

    // Promoted fields
    fmt.Println(d.ID)   // 1 (promoted from Base)
    fmt.Println(d.Name) // test (promoted from Base)

    // Promoted methods
    fmt.Println(d.String()) // Base{ID: 1, Name: test} (promoted from Base)
}
```

## Struct Tags

Struct tags provide metadata about struct fields, commonly used for serialization and validation.

### Basic Struct Tags

```go
import (
    "encoding/json"
    "encoding/xml"
    "fmt"
)

// Struct with JSON tags
type User struct {
    ID       int    `json:"id"`
    Name     string `json:"name"`
    Email    string `json:"email"`
    Password string `json:"-"`              // Excluded from JSON
    IsActive bool   `json:"is_active"`
    Balance  float64 `json:"balance,omitempty"` // Omit if zero value
}

// Multiple tag formats
type Product struct {
    ID          int     `json:"id" xml:"id" db:"product_id"`
    Name        string  `json:"name" xml:"name" db:"product_name"`
    Price       float64 `json:"price" xml:"price" db:"price"`
    Description string  `json:"description,omitempty" xml:"description,omitempty" db:"description"`
}

// Usage
func demonstrateJSONTags() {
    user := User{
        ID:       1,
        Name:     "Alice",
        Email:    "<EMAIL>",
        Password: "secret123",
        IsActive: true,
        Balance:  0.0, // Will be omitted due to omitempty
    }

    jsonData, err := json.Marshal(user)
    if err != nil {
        panic(err)
    }

    fmt.Println(string(jsonData))
    // Output: {"id":1,"name":"Alice","email":"<EMAIL>","is_active":true}
    // Note: Password is excluded, Balance is omitted
}
```

### Advanced Struct Tags

```go
// Custom validation tags
type RegisterRequest struct {
    Username string `json:"username" validate:"required,min=3,max=20"`
    Email    string `json:"email" validate:"required,email"`
    Password string `json:"password" validate:"required,min=8"`
    Age      int    `json:"age" validate:"min=18,max=120"`
}

// Database tags
type UserModel struct {
    ID        int       `db:"id" json:"id"`
    Username  string    `db:"username" json:"username"`
    Email     string    `db:"email" json:"email"`
    CreatedAt time.Time `db:"created_at" json:"created_at"`
    UpdatedAt time.Time `db:"updated_at" json:"updated_at"`
}

// Form tags for HTML forms
type ContactForm struct {
    Name    string `form:"name" binding:"required"`
    Email   string `form:"email" binding:"required,email"`
    Message string `form:"message" binding:"required,min=10"`
}

// Custom tags for your own purposes
type APIField struct {
    Name        string `api:"name" permission:"read"`
    Description string `api:"description" permission:"read"`
    Secret      string `api:"-" permission:"admin"`
    Value       int    `api:"value" permission:"write"`
}
```

### Reading Struct Tags with Reflection

```go
import (
    "reflect"
    "strings"
)

// Function to read struct tags
func getJSONFieldName(field reflect.StructField) string {
    tag := field.Tag.Get("json")
    if tag == "" {
        return field.Name
    }

    // Handle omitempty and other options
    parts := strings.Split(tag, ",")
    if parts[0] == "-" {
        return "" // Field should be ignored
    }

    return parts[0]
}

// Analyze struct tags
func analyzeStruct(v interface{}) {
    t := reflect.TypeOf(v)
    if t.Kind() == reflect.Ptr {
        t = t.Elem()
    }

    for i := 0; i < t.NumField(); i++ {
        field := t.Field(i)

        fmt.Printf("Field: %s\n", field.Name)
        fmt.Printf("  JSON tag: %s\n", field.Tag.Get("json"))
        fmt.Printf("  DB tag: %s\n", field.Tag.Get("db"))
        fmt.Printf("  Validate tag: %s\n", field.Tag.Get("validate"))
        fmt.Println()
    }
}

// Usage
func demonstrateTagReflection() {
    user := User{}
    analyzeStruct(user)
}
```

## Anonymous Structs

Structs without explicit type names, useful for temporary data structures.

### Basic Anonymous Structs

```go
// Anonymous struct variable
func demonstrateAnonymousStructs() {
    // Declare and initialize anonymous struct
    person := struct {
        Name string
        Age  int
    }{
        Name: "Alice",
        Age:  30,
    }

    fmt.Printf("%+v\n", person) // {Name:Alice Age:30}

    // Anonymous struct as function parameter
    processData(struct {
        ID    int
        Value string
    }{
        ID:    1,
        Value: "test",
    })
}

func processData(data struct {
    ID    int
    Value string
}) {
    fmt.Printf("Processing ID: %d, Value: %s\n", data.ID, data.Value)
}
```

### Anonymous Structs in Slices and Maps

```go
// Slice of anonymous structs
func demonstrateAnonymousCollections() {
    // Slice of anonymous structs
    users := []struct {
        Name  string
        Email string
    }{
        {"Alice", "<EMAIL>"},
        {"Bob", "<EMAIL>"},
        {"Charlie", "<EMAIL>"},
    }

    for _, user := range users {
        fmt.Printf("User: %s <%s>\n", user.Name, user.Email)
    }

    // Map with anonymous struct values
    config := map[string]struct {
        Value       interface{}
        Description string
    }{
        "timeout": {
            Value:       30,
            Description: "Request timeout in seconds",
        },
        "retries": {
            Value:       3,
            Description: "Number of retry attempts",
        },
    }

    for key, cfg := range config {
        fmt.Printf("%s: %v (%s)\n", key, cfg.Value, cfg.Description)
    }
}
```

### Anonymous Structs for JSON

```go
// Anonymous structs for API responses
func createAPIResponse() {
    response := struct {
        Status  string      `json:"status"`
        Message string      `json:"message"`
        Data    interface{} `json:"data"`
        Meta    struct {
            Page  int `json:"page"`
            Limit int `json:"limit"`
            Total int `json:"total"`
        } `json:"meta"`
    }{
        Status:  "success",
        Message: "Data retrieved successfully",
        Data: []struct {
            ID   int    `json:"id"`
            Name string `json:"name"`
        }{
            {1, "Alice"},
            {2, "Bob"},
        },
        Meta: struct {
            Page  int `json:"page"`
            Limit int `json:"limit"`
            Total int `json:"total"`
        }{
            Page:  1,
            Limit: 10,
            Total: 2,
        },
    }

    jsonData, _ := json.Marshal(response)
    fmt.Println(string(jsonData))
}
```

## Struct Comparison

Understanding when and how structs can be compared.

### Comparable Structs

```go
// Comparable struct (all fields are comparable)
type Point struct {
    X, Y int
}

type Person struct {
    Name string
    Age  int
}

func demonstrateComparison() {
    p1 := Point{X: 1, Y: 2}
    p2 := Point{X: 1, Y: 2}
    p3 := Point{X: 2, Y: 3}

    fmt.Println(p1 == p2) // true
    fmt.Println(p1 == p3) // false
    fmt.Println(p1 != p3) // true

    // Struct comparison compares all fields
    person1 := Person{Name: "Alice", Age: 30}
    person2 := Person{Name: "Alice", Age: 30}
    person3 := Person{Name: "Alice", Age: 31}

    fmt.Println(person1 == person2) // true
    fmt.Println(person1 == person3) // false
}
```

### Non-Comparable Structs

```go
// Non-comparable struct (contains slice)
type User struct {
    Name string
    Tags []string // Slice makes struct non-comparable
}

// Non-comparable struct (contains map)
type Config struct {
    Name     string
    Settings map[string]string // Map makes struct non-comparable
}

func demonstrateNonComparable() {
    user1 := User{Name: "Alice", Tags: []string{"admin"}}
    user2 := User{Name: "Alice", Tags: []string{"admin"}}

    // This would not compile:
    // fmt.Println(user1 == user2) // Error: invalid operation

    // Must compare manually
    fmt.Println(compareUsers(user1, user2))
}

func compareUsers(u1, u2 User) bool {
    if u1.Name != u2.Name {
        return false
    }

    if len(u1.Tags) != len(u2.Tags) {
        return false
    }

    for i, tag := range u1.Tags {
        if tag != u2.Tags[i] {
            return false
        }
    }

    return true
}
```

### Custom Comparison Methods

```go
// Struct with custom comparison
type Rectangle struct {
    Width, Height float64
}

func (r Rectangle) Equals(other Rectangle) bool {
    return r.Width == other.Width && r.Height == other.Height
}

func (r Rectangle) Area() float64 {
    return r.Width * r.Height
}

func (r Rectangle) SameArea(other Rectangle) bool {
    return r.Area() == other.Area()
}

// Usage
func demonstrateCustomComparison() {
    r1 := Rectangle{Width: 3, Height: 4}
    r2 := Rectangle{Width: 3, Height: 4}
    r3 := Rectangle{Width: 2, Height: 6}

    fmt.Println(r1.Equals(r2))   // true
    fmt.Println(r1.Equals(r3))   // false
    fmt.Println(r1.SameArea(r3)) // true (both have area 12)
}
```

## Memory Layout and Alignment

Understanding how structs are laid out in memory.

### Memory Alignment

```go
import "unsafe"

// Struct with different field sizes
type Example1 struct {
    A bool   // 1 byte
    B int64  // 8 bytes
    C bool   // 1 byte
}

type Example2 struct {
    A bool   // 1 byte
    C bool   // 1 byte
    B int64  // 8 bytes
}

func demonstrateAlignment() {
    var e1 Example1
    var e2 Example2

    fmt.Printf("Example1 size: %d bytes\n", unsafe.Sizeof(e1)) // Likely 24 bytes
    fmt.Printf("Example2 size: %d bytes\n", unsafe.Sizeof(e2)) // Likely 16 bytes

    // Field offsets
    fmt.Printf("Example1.A offset: %d\n", unsafe.Offsetof(e1.A)) // 0
    fmt.Printf("Example1.B offset: %d\n", unsafe.Offsetof(e1.B)) // 8 (padded)
    fmt.Printf("Example1.C offset: %d\n", unsafe.Offsetof(e1.C)) // 16

    fmt.Printf("Example2.A offset: %d\n", unsafe.Offsetof(e2.A)) // 0
    fmt.Printf("Example2.C offset: %d\n", unsafe.Offsetof(e2.C)) // 1
    fmt.Printf("Example2.B offset: %d\n", unsafe.Offsetof(e2.B)) // 8
}
```

### Optimizing Struct Layout

```go
// Poor layout (lots of padding)
type BadLayout struct {
    A bool    // 1 byte + 7 bytes padding
    B int64   // 8 bytes
    C bool    // 1 byte + 7 bytes padding
    D int64   // 8 bytes
    E bool    // 1 byte + 7 bytes padding
}

// Good layout (minimal padding)
type GoodLayout struct {
    B int64   // 8 bytes
    D int64   // 8 bytes
    A bool    // 1 byte
    C bool    // 1 byte
    E bool    // 1 byte + 5 bytes padding
}

func compareLayouts() {
    var bad BadLayout
    var good GoodLayout

    fmt.Printf("Bad layout size: %d bytes\n", unsafe.Sizeof(bad))   // Likely 40 bytes
    fmt.Printf("Good layout size: %d bytes\n", unsafe.Sizeof(good)) // Likely 24 bytes
}

// Empty struct has zero size
type Empty struct{}

func demonstrateEmptyStruct() {
    var empty Empty
    fmt.Printf("Empty struct size: %d bytes\n", unsafe.Sizeof(empty)) // 0 bytes

    // Array of empty structs
    var emptyArray [1000]Empty
    fmt.Printf("Array of 1000 empty structs: %d bytes\n", unsafe.Sizeof(emptyArray)) // 0 bytes
}
```

## Advanced Patterns

Sophisticated patterns for working with structs in Go.

### Builder Pattern

```go
// Builder pattern for complex struct construction
type HTTPRequest struct {
    Method  string
    URL     string
    Headers map[string]string
    Body    []byte
    Timeout time.Duration
}

type HTTPRequestBuilder struct {
    request HTTPRequest
}

func NewHTTPRequestBuilder() *HTTPRequestBuilder {
    return &HTTPRequestBuilder{
        request: HTTPRequest{
            Method:  "GET",
            Headers: make(map[string]string),
            Timeout: 30 * time.Second,
        },
    }
}

func (b *HTTPRequestBuilder) Method(method string) *HTTPRequestBuilder {
    b.request.Method = method
    return b
}

func (b *HTTPRequestBuilder) URL(url string) *HTTPRequestBuilder {
    b.request.URL = url
    return b
}

func (b *HTTPRequestBuilder) Header(key, value string) *HTTPRequestBuilder {
    b.request.Headers[key] = value
    return b
}

func (b *HTTPRequestBuilder) Body(body []byte) *HTTPRequestBuilder {
    b.request.Body = body
    return b
}

func (b *HTTPRequestBuilder) Timeout(timeout time.Duration) *HTTPRequestBuilder {
    b.request.Timeout = timeout
    return b
}

func (b *HTTPRequestBuilder) Build() HTTPRequest {
    return b.request
}

// Usage
func demonstrateBuilder() {
    request := NewHTTPRequestBuilder().
        Method("POST").
        URL("https://api.example.com/users").
        Header("Content-Type", "application/json").
        Header("Authorization", "Bearer token123").
        Body([]byte(`{"name":"Alice"}`)).
        Timeout(10 * time.Second).
        Build()

    fmt.Printf("%+v\n", request)
}
```

### State Machine Pattern

```go
// State machine using structs and interfaces
type State interface {
    Handle(*Context) State
    String() string
}

type Context struct {
    Data    map[string]interface{}
    Current State
}

func (c *Context) SetState(state State) {
    c.Current = state
}

func (c *Context) Process() {
    if c.Current != nil {
        nextState := c.Current.Handle(c)
        if nextState != nil {
            c.SetState(nextState)
        }
    }
}

// Concrete states
type IdleState struct{}

func (s *IdleState) Handle(ctx *Context) State {
    fmt.Println("Processing in idle state")
    if ctx.Data["start"] == true {
        return &ProcessingState{}
    }
    return nil
}

func (s *IdleState) String() string {
    return "Idle"
}

type ProcessingState struct{}

func (s *ProcessingState) Handle(ctx *Context) State {
    fmt.Println("Processing in processing state")
    if ctx.Data["complete"] == true {
        return &CompletedState{}
    }
    if ctx.Data["error"] == true {
        return &ErrorState{}
    }
    return nil
}

func (s *ProcessingState) String() string {
    return "Processing"
}

type CompletedState struct{}

func (s *CompletedState) Handle(ctx *Context) State {
    fmt.Println("Processing completed")
    return &IdleState{}
}

func (s *CompletedState) String() string {
    return "Completed"
}

type ErrorState struct{}

func (s *ErrorState) Handle(ctx *Context) State {
    fmt.Println("Error occurred")
    return &IdleState{}
}

func (s *ErrorState) String() string {
    return "Error"
}
```

### Observer Pattern

```go
// Observer pattern with structs
type Observer interface {
    Update(event Event)
}

type Event struct {
    Type string
    Data interface{}
}

type Subject struct {
    observers []Observer
}

func (s *Subject) Attach(observer Observer) {
    s.observers = append(s.observers, observer)
}

func (s *Subject) Detach(observer Observer) {
    for i, obs := range s.observers {
        if obs == observer {
            s.observers = append(s.observers[:i], s.observers[i+1:]...)
            break
        }
    }
}

func (s *Subject) Notify(event Event) {
    for _, observer := range s.observers {
        observer.Update(event)
    }
}

// Concrete observer
type Logger struct {
    Name string
}

func (l *Logger) Update(event Event) {
    fmt.Printf("[%s] Received event: %s - %v\n", l.Name, event.Type, event.Data)
}

// Usage
func demonstrateObserver() {
    subject := &Subject{}

    logger1 := &Logger{Name: "FileLogger"}
    logger2 := &Logger{Name: "ConsoleLogger"}

    subject.Attach(logger1)
    subject.Attach(logger2)

    subject.Notify(Event{Type: "UserCreated", Data: "Alice"})
    subject.Notify(Event{Type: "UserDeleted", Data: "Bob"})
}
```

### Functional Options Pattern

```go
// Functional options pattern
type Server struct {
    Host         string
    Port         int
    ReadTimeout  time.Duration
    WriteTimeout time.Duration
    MaxConns     int
    TLS          bool
}

type ServerOption func(*Server)

func WithHost(host string) ServerOption {
    return func(s *Server) {
        s.Host = host
    }
}

func WithPort(port int) ServerOption {
    return func(s *Server) {
        s.Port = port
    }
}

func WithTimeouts(read, write time.Duration) ServerOption {
    return func(s *Server) {
        s.ReadTimeout = read
        s.WriteTimeout = write
    }
}

func WithMaxConnections(max int) ServerOption {
    return func(s *Server) {
        s.MaxConns = max
    }
}

func WithTLS() ServerOption {
    return func(s *Server) {
        s.TLS = true
    }
}

func NewServer(opts ...ServerOption) *Server {
    // Default values
    server := &Server{
        Host:         "localhost",
        Port:         8080,
        ReadTimeout:  30 * time.Second,
        WriteTimeout: 30 * time.Second,
        MaxConns:     100,
        TLS:          false,
    }

    // Apply options
    for _, opt := range opts {
        opt(server)
    }

    return server
}

// Usage
func demonstrateFunctionalOptions() {
    server := NewServer(
        WithHost("0.0.0.0"),
        WithPort(443),
        WithTLS(),
        WithMaxConnections(1000),
        WithTimeouts(10*time.Second, 15*time.Second),
    )

    fmt.Printf("%+v\n", server)
}
```

## Best Practices

Guidelines for effective struct usage in Go.

### Struct Design Principles

```go
// 1. Keep structs focused and cohesive
// Good: Single responsibility
type User struct {
    ID       int
    Username string
    Email    string
}

type UserProfile struct {
    UserID    int
    FirstName string
    LastName  string
    Bio       string
    Avatar    string
}

// Avoid: Too many responsibilities
type BadUser struct {
    ID          int
    Username    string
    Email       string
    FirstName   string
    LastName    string
    Bio         string
    Avatar      string
    Orders      []Order
    Preferences map[string]interface{}
    LoginHistory []LoginEvent
}

// 2. Use composition over inheritance
type Timestamped struct {
    CreatedAt time.Time
    UpdatedAt time.Time
}

type Auditable struct {
    CreatedBy string
    UpdatedBy string
}

type Article struct {
    Timestamped
    Auditable
    ID      int
    Title   string
    Content string
}

// 3. Make zero values useful
type Config struct {
    Host    string        // Default: ""
    Port    int           // Default: 0
    Timeout time.Duration // Default: 0
    Debug   bool          // Default: false
}

func (c *Config) GetHost() string {
    if c.Host == "" {
        return "localhost"
    }
    return c.Host
}

func (c *Config) GetPort() int {
    if c.Port == 0 {
        return 8080
    }
    return c.Port
}
```

### Constructor and Validation Patterns

```go
// Constructor with validation
type Email string

func NewEmail(email string) (Email, error) {
    if !isValidEmail(email) {
        return "", fmt.Errorf("invalid email: %s", email)
    }
    return Email(email), nil
}

func (e Email) String() string {
    return string(e)
}

type User struct {
    ID    int
    Name  string
    Email Email
}

func NewUser(name string, email string) (*User, error) {
    if name == "" {
        return nil, errors.New("name cannot be empty")
    }

    validEmail, err := NewEmail(email)
    if err != nil {
        return nil, err
    }

    return &User{
        ID:    generateID(),
        Name:  name,
        Email: validEmail,
    }, nil
}

// Validation methods
func (u *User) Validate() error {
    if u.Name == "" {
        return errors.New("name is required")
    }
    if u.Email == "" {
        return errors.New("email is required")
    }
    return nil
}

// Update methods with validation
func (u *User) SetEmail(email string) error {
    validEmail, err := NewEmail(email)
    if err != nil {
        return err
    }
    u.Email = validEmail
    return nil
}
```

### Interface Implementation

```go
// Design structs to implement interfaces
type Serializable interface {
    Serialize() ([]byte, error)
    Deserialize([]byte) error
}

type Validatable interface {
    Validate() error
}

type Identifiable interface {
    GetID() int
}

// Struct implementing multiple interfaces
type Product struct {
    ID          int     `json:"id"`
    Name        string  `json:"name"`
    Price       float64 `json:"price"`
    Description string  `json:"description"`
}

func (p *Product) GetID() int {
    return p.ID
}

func (p *Product) Validate() error {
    if p.Name == "" {
        return errors.New("name is required")
    }
    if p.Price < 0 {
        return errors.New("price cannot be negative")
    }
    return nil
}

func (p *Product) Serialize() ([]byte, error) {
    return json.Marshal(p)
}

func (p *Product) Deserialize(data []byte) error {
    return json.Unmarshal(data, p)
}

// Generic function working with interfaces
func ProcessEntity[T Identifiable](entity T) {
    fmt.Printf("Processing entity with ID: %d\n", entity.GetID())
}
```

## Common Mistakes

Avoiding common pitfalls when working with structs.

### Mistake 1: Pointer vs Value Confusion

```go
// Wrong: Inconsistent receiver types
type Counter struct {
    count int
}

func (c Counter) GetCount() int {    // Value receiver
    return c.count
}

func (c *Counter) Increment() {     // Pointer receiver
    c.count++
}

func (c Counter) Reset() {          // Value receiver - won't work as expected
    c.count = 0  // Only modifies copy
}

// Correct: Consistent receiver types
type BetterCounter struct {
    count int
}

func (c *BetterCounter) GetCount() int {
    return c.count
}

func (c *BetterCounter) Increment() {
    c.count++
}

func (c *BetterCounter) Reset() {
    c.count = 0
}
```

### Mistake 2: Forgetting to Initialize Maps and Slices

```go
// Wrong: Using nil maps/slices
type BadConfig struct {
    Settings map[string]string
    Values   []int
}

func (c *BadConfig) AddSetting(key, value string) {
    c.Settings[key] = value  // Panic: assignment to entry in nil map
}

// Correct: Initialize in constructor
type GoodConfig struct {
    Settings map[string]string
    Values   []int
}

func NewGoodConfig() *GoodConfig {
    return &GoodConfig{
        Settings: make(map[string]string),
        Values:   make([]int, 0),
    }
}

func (c *GoodConfig) AddSetting(key, value string) {
    if c.Settings == nil {
        c.Settings = make(map[string]string)
    }
    c.Settings[key] = value
}
```

### Mistake 3: Struct Copying Issues

```go
// Wrong: Copying structs with pointers/references
type BadStruct struct {
    Data   []int
    Config *Config
    mutex  sync.Mutex
}

func (b BadStruct) Copy() BadStruct {
    return b  // Shallow copy - shares Data slice and Config pointer
}

// Correct: Deep copy when needed
type GoodStruct struct {
    Data   []int
    Config *Config
    mutex  sync.Mutex
}

func (g *GoodStruct) Copy() *GoodStruct {
    // Deep copy
    dataCopy := make([]int, len(g.Data))
    copy(dataCopy, g.Data)

    var configCopy *Config
    if g.Config != nil {
        configCopy = &Config{} // Assuming Config has appropriate copy method
        *configCopy = *g.Config
    }

    return &GoodStruct{
        Data:   dataCopy,
        Config: configCopy,
        // Don't copy mutex - create new one
    }
}
```

## Performance Considerations

Optimizing struct usage for better performance.

### Memory Layout Optimization

```go
// Poor memory layout
type BadStruct struct {
    A bool    // 1 byte + 7 padding
    B int64   // 8 bytes
    C bool    // 1 byte + 7 padding
    D int32   // 4 bytes + 4 padding
    E bool    // 1 byte + 7 padding
}

// Optimized memory layout
type GoodStruct struct {
    B int64   // 8 bytes
    D int32   // 4 bytes
    A bool    // 1 byte
    C bool    // 1 byte
    E bool    // 1 byte + 1 padding
}

// Benchmark the difference
func BenchmarkBadStruct(b *testing.B) {
    structs := make([]BadStruct, 1000)
    b.ResetTimer()

    for i := 0; i < b.N; i++ {
        for j := range structs {
            structs[j].A = true
        }
    }
}

func BenchmarkGoodStruct(b *testing.B) {
    structs := make([]GoodStruct, 1000)
    b.ResetTimer()

    for i := 0; i < b.N; i++ {
        for j := range structs {
            structs[j].A = true
        }
    }
}
```

### Value vs Pointer Performance

```go
// Small structs: value receivers might be faster
type SmallStruct struct {
    X, Y int
}

func (s SmallStruct) ValueMethod() int {
    return s.X + s.Y
}

func (s *SmallStruct) PointerMethod() int {
    return s.X + s.Y
}

// Large structs: pointer receivers are usually faster
type LargeStruct struct {
    Data [1000]int
}

func (s LargeStruct) ValueMethod() int {  // Copies 8000 bytes
    return s.Data[0]
}

func (s *LargeStruct) PointerMethod() int {  // Copies 8 bytes
    return s.Data[0]
}

// Benchmark to compare
func BenchmarkSmallStructValue(b *testing.B) {
    s := SmallStruct{X: 1, Y: 2}
    for i := 0; i < b.N; i++ {
        _ = s.ValueMethod()
    }
}

func BenchmarkSmallStructPointer(b *testing.B) {
    s := &SmallStruct{X: 1, Y: 2}
    for i := 0; i < b.N; i++ {
        _ = s.PointerMethod()
    }
}
```

### Pool Pattern for Struct Reuse

```go
import "sync"

// Object pool for expensive structs
type ExpensiveStruct struct {
    Buffer []byte
    Data   map[string]interface{}
}

var expensivePool = sync.Pool{
    New: func() interface{} {
        return &ExpensiveStruct{
            Buffer: make([]byte, 0, 1024),
            Data:   make(map[string]interface{}),
        }
    },
}

func GetExpensiveStruct() *ExpensiveStruct {
    return expensivePool.Get().(*ExpensiveStruct)
}

func PutExpensiveStruct(s *ExpensiveStruct) {
    // Reset struct state
    s.Buffer = s.Buffer[:0]
    for k := range s.Data {
        delete(s.Data, k)
    }

    expensivePool.Put(s)
}

// Usage
func useExpensiveStruct() {
    s := GetExpensiveStruct()
    defer PutExpensiveStruct(s)

    // Use struct
    s.Buffer = append(s.Buffer, []byte("data")...)
    s.Data["key"] = "value"
}
```

This comprehensive guide covers all aspects of structs in Go, from basic usage to advanced patterns and performance optimization. Understanding these concepts will help you design and implement effective struct-based solutions in your Go applications.
