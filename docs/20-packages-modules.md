# Packages and Modules in Go

This comprehensive guide covers packages and modules in Go, including package organization, imports, module system, dependency management, versioning, and best practices for building maintainable Go projects.

## Table of Contents

1. [Overview](#overview)
2. [Package Basics](#package-basics)
3. [Package Organization](#package-organization)
4. [Import System](#import-system)
5. [Module System](#module-system)
6. [go.mod File](#gomod-file)
7. [Dependency Management](#dependency-management)
8. [Versioning](#versioning)
9. [Module Commands](#module-commands)
10. [Package Documentation](#package-documentation)
11. [Testing Packages](#testing-packages)
12. [Best Practices](#best-practices)
13. [Common Patterns](#common-patterns)
14. [Troubleshooting](#troubleshooting)

## Overview

Go's package and module system provides a robust foundation for code organization, dependency management, and distribution.

### Key Concepts

```go
// Package: A collection of Go source files in the same directory
// Module: A collection of packages with shared dependencies
// Import path: Unique identifier for a package
// go.mod: Module definition file
// Semantic versioning: Version numbering scheme (v1.2.3)
```

### Package vs Module

| Aspect | Package | Module |
|--------|---------|---------|
| **Scope** | Single directory | Multiple packages |
| **Purpose** | Code organization | Dependency management |
| **Versioning** | No direct versioning | Semantic versioning |
| **Distribution** | Part of module | Unit of distribution |
| **Dependencies** | Import other packages | Declare module dependencies |

## Package Basics

Understanding the fundamental concepts of Go packages.

### Package Declaration

```go
// Every Go file must start with a package declaration
package main // Executable package

package utils // Library package

package mypackage // Custom package name

// Package name rules:
// - Must be lowercase
// - Should be short and descriptive
// - Should match directory name (convention)
// - Cannot contain spaces or special characters
```

### Package Structure

```
myproject/
├── main.go                 // package main
├── utils/
│   ├── string.go          // package utils
│   ├── math.go            // package utils
│   └── helper.go          // package utils
├── models/
│   ├── user.go            // package models
│   └── product.go         // package models
└── handlers/
    ├── auth.go            // package handlers
    └── api.go             // package handlers
```

### Visibility Rules

```go
// In package utils/string.go
package utils

import "strings"

// Exported function (starts with capital letter)
func ToUpperCase(s string) string {
    return strings.ToUpper(s)
}

// Exported variable
var DefaultSeparator = ","

// Exported constant
const MaxLength = 100

// Exported type
type StringProcessor struct {
    prefix string // unexported field
    Suffix string // exported field
}

// Exported method
func (sp *StringProcessor) Process(input string) string {
    return sp.prefix + input + sp.Suffix
}

// unexported function (starts with lowercase letter)
func internalHelper(s string) string {
    return strings.TrimSpace(s)
}

// unexported variable
var internalCounter int

// unexported constant
const internalBufferSize = 1024
```

### Package Initialization

```go
// Package initialization order
package mypackage

import (
    "fmt"
    "log"
)

// Package-level variables are initialized first
var (
    config = loadConfig()
    logger = log.New(os.Stdout, "mypackage: ", log.LstdFlags)
)

// init functions run after variable initialization
func init() {
    fmt.Println("First init function")
    validateConfig()
}

func init() {
    fmt.Println("Second init function")
    setupDefaults()
}

// Multiple init functions are allowed and run in order
func init() {
    fmt.Println("Third init function")
}

func loadConfig() map[string]string {
    fmt.Println("Loading configuration")
    return map[string]string{
        "env": "development",
    }
}

func validateConfig() {
    fmt.Println("Validating configuration")
}

func setupDefaults() {
    fmt.Println("Setting up defaults")
}
```

## Package Organization

Best practices for organizing packages in Go projects.

### Project Structure Patterns

```
// Standard Go project layout
myproject/
├── cmd/                    // Main applications
│   ├── server/
│   │   └── main.go
│   └── client/
│       └── main.go
├── internal/               // Private packages
│   ├── auth/
│   ├── database/
│   └── config/
├── pkg/                    // Public packages
│   ├── api/
│   ├── models/
│   └── utils/
├── api/                    // API definitions
│   └── openapi.yaml
├── web/                    // Web assets
│   ├── static/
│   └── templates/
├── scripts/                // Build and deployment scripts
├── docs/                   // Documentation
├── test/                   // Integration tests
├── go.mod
├── go.sum
├── README.md
└── Makefile
```

### Package Naming Conventions

```go
// Good package names
package user        // Clear, concise
package http        // Standard library style
package json        // Abbreviation is well-known
package auth        // Common abbreviation
package database    // Descriptive

// Avoid these patterns
package userutils   // Don't use "utils" suffix
package helpers     // Too generic
package common      // Too generic
package lib         // Too generic
package mycompany   // Company names

// Package organization by feature
myapp/
├── user/           // User-related functionality
│   ├── user.go     // User model
│   ├── service.go  // User service
│   └── handler.go  // HTTP handlers
├── product/        // Product-related functionality
│   ├── product.go
│   ├── service.go
│   └── handler.go
└── order/          // Order-related functionality
    ├── order.go
    ├── service.go
    └── handler.go
```

### Internal Packages

```go
// internal/ packages are only accessible within the module
mymodule/
├── internal/
│   ├── auth/           // Only accessible within mymodule
│   │   └── jwt.go
│   └── database/       // Only accessible within mymodule
│       └── connection.go
├── public/
│   └── api.go          // Accessible to external modules
└── main.go

// In main.go
package main

import (
    "mymodule/internal/auth"     // OK - same module
    "mymodule/internal/database" // OK - same module
    "mymodule/public"            // OK - public package
)

// External modules cannot import:
// import "mymodule/internal/auth" // Error: use of internal package not allowed
```

## Import System

Understanding Go's import system and import patterns.

### Basic Imports

```go
package main

import (
    // Standard library imports
    "fmt"
    "net/http"
    "encoding/json"

    // Third-party imports
    "github.com/gorilla/mux"
    "github.com/lib/pq"

    // Local imports (same module)
    "myproject/internal/auth"
    "myproject/pkg/utils"
)

// Single import
import "fmt"

// Multiple imports (preferred style)
import (
    "fmt"
    "log"
)
```

### Import Aliases

```go
package main

import (
    // Alias import
    "database/sql"
    pg "github.com/lib/pq"           // Alias to avoid conflicts

    // Dot import (use sparingly)
    . "fmt"                          // Import into current namespace

    // Blank import (for side effects only)
    _ "github.com/lib/pq"            // Initialize driver

    // Rename to avoid conflicts
    goContext "context"
    myContext "myproject/pkg/context"
)

func main() {
    // Using aliases
    db, err := sql.Open("postgres", "...")
    if err != nil {
        log.Fatal(err)
    }

    // Using dot import (Println instead of fmt.Println)
    Println("Hello, World!")

    // Using renamed imports
    ctx := goContext.Background()
    myCtx := myContext.New()
}
```

### Import Organization

```go
package main

import (
    // Standard library (first group)
    "context"
    "fmt"
    "log"
    "net/http"
    "time"

    // Third-party packages (second group)
    "github.com/gorilla/mux"
    "github.com/lib/pq"
    "go.uber.org/zap"

    // Local packages (third group)
    "myproject/internal/auth"
    "myproject/internal/database"
    "myproject/pkg/utils"
)

// Groups are separated by blank lines
// Within each group, imports are sorted alphabetically
```

### Conditional Imports

```go
// Build tags for conditional compilation
//go:build linux
// +build linux

package platform

import "syscall"

func GetPlatformInfo() string {
    return "Linux platform"
}

// In another file: platform_windows.go
//go:build windows
// +build windows

package platform

import "golang.org/x/sys/windows"

func GetPlatformInfo() string {
    return "Windows platform"
}
```

## Module System

Understanding Go modules and module management.

### Module Basics

```go
// A module is defined by a go.mod file
// Module path serves as import path prefix for packages
// Example module structure:

github.com/myuser/myproject/     // Module root
├── go.mod                       // Module definition
├── go.sum                       // Dependency checksums
├── main.go                      // package main
├── auth/                        // package github.com/myuser/myproject/auth
│   └── auth.go
└── utils/                       // package github.com/myuser/myproject/utils
    └── utils.go

// Import paths within the module:
// import "github.com/myuser/myproject/auth"
// import "github.com/myuser/myproject/utils"
```

### Module Creation

```bash
# Initialize a new module
go mod init github.com/myuser/myproject

# This creates go.mod file:
module github.com/myuser/myproject

go 1.21

# Add dependencies
go get github.com/gorilla/mux
go get github.com/lib/pq@v1.10.7

# Updated go.mod:
module github.com/myuser/myproject

go 1.21

require (
    github.com/gorilla/mux v1.8.0
    github.com/lib/pq v1.10.7
)
```

### Module Proxy and Checksums

```go
// Go module proxy (GOPROXY)
// Default: https://proxy.golang.org,direct

// Module checksum database (GOSUMDB)
// Default: sum.golang.org

// go.sum file contains cryptographic checksums
github.com/gorilla/mux v1.8.0 h1:i40aqfkR1h2SlN9hojwV5ZA91wcXFOvkdNIeFDP5koI=
github.com/gorilla/mux v1.8.0/go.mod h1:DVbg23sWSpFRCP0SfiEN6jmj59UnW/n46BH5rLB71So=

// Environment variables for module behavior
export GOPROXY=https://proxy.golang.org,direct
export GOSUMDB=sum.golang.org
export GOPRIVATE=github.com/mycompany/*
export GONOPROXY=github.com/mycompany/*
export GONOSUMDB=github.com/mycompany/*
```

## go.mod File

Understanding the go.mod file structure and directives.

### Basic go.mod Structure

```go
// go.mod file syntax
module github.com/myuser/myproject

go 1.21

require (
    github.com/gorilla/mux v1.8.0
    github.com/lib/pq v1.10.7
    golang.org/x/crypto v0.14.0
)

require (
    // Indirect dependencies (automatically managed)
    github.com/gorilla/context v1.1.1 // indirect
    golang.org/x/sys v0.13.0 // indirect
)

exclude (
    // Exclude specific versions
    github.com/old/package v1.0.0
)

replace (
    // Replace dependencies
    github.com/old/package => github.com/new/package v2.0.0
    github.com/local/package => ./local/package
)

retract (
    // Retract published versions
    v1.0.0 // Contains security vulnerability
    [v1.1.0, v1.2.0] // Range retraction
)
```

### Module Directives

```go
// module directive - defines module path
module example.com/mymodule

// go directive - minimum Go version
go 1.21

// require directive - dependencies
require (
    github.com/pkg/errors v0.9.1
    golang.org/x/net v0.17.0
)

// exclude directive - exclude specific versions
exclude github.com/broken/package v1.5.0

// replace directive - replace dependencies
replace (
    // Replace with different module
    github.com/old/package => github.com/new/package v2.0.0

    // Replace with local directory
    github.com/mycompany/internal => ./internal

    // Replace with specific version
    golang.org/x/net => golang.org/x/net v0.16.0
)

// retract directive - retract published versions
retract (
    v1.0.1 // Published accidentally
    v1.0.2 // Contains bug
)
```

### Version Selection

```go
// Semantic versioning in go.mod
require (
    github.com/pkg/errors v0.9.1          // Exact version
    golang.org/x/crypto v0.14.0           // Exact version
    github.com/gorilla/mux v1.8.0         // Exact version
)

// Version formats
v1.2.3          // Release version
v1.2.3-pre      // Pre-release
v1.2.3+meta     // Metadata
v0.0.0-20231101120000-abcdef123456  // Pseudo-version

// Version queries
go get github.com/pkg/errors@latest    // Latest version
go get github.com/pkg/errors@v0.9.1    // Specific version
go get github.com/pkg/errors@master    // Specific branch
go get github.com/pkg/errors@none      // Remove dependency
```

### Workspace Mode

```go
// go.work file for multi-module development
go 1.21

use (
    ./module1
    ./module2
    ./shared
)

replace github.com/external/dep => ./local/dep

// Directory structure:
myworkspace/
├── go.work
├── module1/
│   ├── go.mod
│   └── main.go
├── module2/
│   ├── go.mod
│   └── main.go
└── shared/
    ├── go.mod
    └── utils.go
```

## Dependency Management

Managing dependencies effectively in Go modules.

### Adding Dependencies

```bash
# Add latest version
go get github.com/gorilla/mux

# Add specific version
go get github.com/lib/pq@v1.10.7

# Add from specific commit
go get github.com/pkg/errors@abc123

# Add from branch
go get github.com/dev/package@main

# Add and upgrade
go get -u github.com/gorilla/mux

# Add all dependencies from imports
go mod tidy
```

### Upgrading Dependencies

```bash
# Upgrade to latest minor/patch version
go get -u github.com/gorilla/mux

# Upgrade to latest patch version only
go get -u=patch github.com/gorilla/mux

# Upgrade all dependencies
go get -u ./...

# Upgrade all dependencies (patch only)
go get -u=patch ./...

# Check for available upgrades
go list -u -m all
```

### Removing Dependencies

```bash
# Remove dependency
go get github.com/unused/package@none

# Clean up unused dependencies
go mod tidy

# Remove and clean
go get github.com/unused/package@none && go mod tidy
```

### Dependency Conflicts

```go
// go.mod with version conflicts
module myproject

require (
    github.com/pkg/errors v0.9.1
    github.com/other/package v1.0.0
)

// github.com/other/package requires github.com/pkg/errors v0.8.1
// Go will choose the higher version (v0.9.1)

// Manual resolution with replace
replace github.com/pkg/errors => github.com/pkg/errors v0.8.1

// Or exclude problematic versions
exclude github.com/pkg/errors v0.9.1
```

### Private Modules

```bash
# Configure private module access
export GOPRIVATE=github.com/mycompany/*
export GONOPROXY=github.com/mycompany/*
export GONOSUMDB=github.com/mycompany/*

# Git configuration for private repos
git config --global url."**************:".insteadOf "https://github.com/"

# Using SSH for private modules
<NAME_EMAIL>:mycompany/private-repo.git
```

## Module Commands

Essential Go module commands and their usage.

### Basic Commands

```bash
# Initialize module
go mod init [module-path]

# Add missing dependencies and remove unused ones
go mod tidy

# Download dependencies
go mod download

# Verify dependencies
go mod verify

# Show module information
go list -m all

# Show dependency graph
go mod graph

# Show why a dependency is needed
go mod why github.com/pkg/errors

# Edit go.mod file
go mod edit -require=github.com/pkg/errors@v0.9.1
```

### Advanced Commands

```bash
# Vendor dependencies
go mod vendor

# Build with vendor
go build -mod=vendor

# Check module cache
go clean -modcache

# Show module cache location
go env GOMODCACHE

# Download specific module
go mod download github.com/gorilla/mux@v1.8.0

# List available versions
go list -m -versions github.com/gorilla/mux

# Show module dependencies
go list -m -json all

# Find modules
go list -m -json github.com/gorilla/...
```

### Module Editing

```bash
# Add requirement
go mod edit -require=github.com/pkg/errors@v0.9.1

# Drop requirement
go mod edit -droprequire=github.com/pkg/errors

# Add replace
go mod edit -replace=github.com/old/package=github.com/new/package@v2.0.0

# Drop replace
go mod edit -dropreplace=github.com/old/package

# Add exclude
go mod edit -exclude=github.com/broken/package@v1.0.0

# Drop exclude
go mod edit -dropexclude=github.com/broken/package@v1.0.0

# Set Go version
go mod edit -go=1.21
```

## Package Documentation

Writing and generating documentation for packages.

### Package Documentation

```go
// Package math provides basic mathematical operations.
//
// This package implements common mathematical functions
// and utilities for numeric computations.
//
// Example usage:
//
//	result := math.Add(5, 3)
//	fmt.Println(result) // Output: 8
//
// For more complex operations, see the Advanced function.
package math

import "fmt"

// Add returns the sum of two integers.
//
// This function performs basic addition and returns
// the result as an integer.
//
// Example:
//
//	sum := Add(10, 20)
//	fmt.Println(sum) // Output: 30
func Add(a, b int) int {
    return a + b
}

// Calculator represents a mathematical calculator.
//
// It maintains state for complex calculations and
// provides methods for various operations.
//
// Example:
//
//	calc := &Calculator{precision: 2}
//	result := calc.Divide(10, 3)
//	fmt.Printf("%.2f", result) // Output: 3.33
type Calculator struct {
    // precision specifies the number of decimal places
    precision int
}

// NewCalculator creates a new Calculator with default precision.
//
// The default precision is set to 2 decimal places.
// Use SetPrecision to change this value.
func NewCalculator() *Calculator {
    return &Calculator{precision: 2}
}

// SetPrecision sets the calculation precision.
//
// Precision must be between 0 and 10. Values outside
// this range will be clamped to the nearest valid value.
func (c *Calculator) SetPrecision(precision int) {
    if precision < 0 {
        precision = 0
    } else if precision > 10 {
        precision = 10
    }
    c.precision = precision
}
```

### Documentation Generation

```bash
# Generate documentation
go doc math
go doc math.Add
go doc math.Calculator
go doc math.Calculator.SetPrecision

# Start documentation server
godoc -http=:6060

# Generate documentation website
go install golang.org/x/tools/cmd/godoc@latest
godoc -http=:6060

# View documentation in browser
# http://localhost:6060/pkg/your-module-path/
```

### Documentation Best Practices

```go
// Good documentation examples

// Package user provides user management functionality.
//
// This package handles user authentication, authorization,
// and profile management for the application.
//
// Basic usage:
//
//	user, err := user.Authenticate("username", "password")
//	if err != nil {
//	    log.Fatal(err)
//	}
//	fmt.Printf("Welcome, %s!", user.Name)
package user

// User represents a system user.
//
// Each user has a unique ID, username, and associated
// profile information. Users can be authenticated and
// authorized for various operations.
type User struct {
    ID       int    `json:"id"`
    Username string `json:"username"`
    Email    string `json:"email"`
    Name     string `json:"name"`
}

// Authenticate verifies user credentials.
//
// It takes a username and password, validates them against
// the user database, and returns the authenticated user.
//
// Returns an error if:
//   - Username is empty
//   - Password is empty
//   - Credentials are invalid
//   - Database connection fails
//
// Example:
//
//	user, err := Authenticate("john", "secret123")
//	if err != nil {
//	    return fmt.Errorf("authentication failed: %w", err)
//	}
func Authenticate(username, password string) (*User, error) {
    if username == "" {
        return nil, fmt.Errorf("username cannot be empty")
    }
    if password == "" {
        return nil, fmt.Errorf("password cannot be empty")
    }

    // Implementation...
    return &User{}, nil
}

// Deprecated: Use AuthenticateWithContext instead.
//
// This function will be removed in v2.0.0.
func AuthenticateOld(username, password string) (*User, error) {
    return Authenticate(username, password)
}
```

## Testing Packages

Testing strategies for packages and modules.

### Package Testing Structure

```
mypackage/
├── calculator.go
├── calculator_test.go
├── internal/
│   ├── helper.go
│   └── helper_test.go
├── testdata/
│   ├── input.json
│   └── expected.json
└── example_test.go
```

### Basic Package Tests

```go
// calculator_test.go
package math

import (
    "testing"
)

func TestAdd(t *testing.T) {
    tests := []struct {
        name     string
        a, b     int
        expected int
    }{
        {"positive numbers", 2, 3, 5},
        {"negative numbers", -2, -3, -5},
        {"mixed numbers", -2, 3, 1},
        {"zero values", 0, 0, 0},
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            result := Add(tt.a, tt.b)
            if result != tt.expected {
                t.Errorf("Add(%d, %d) = %d; want %d", tt.a, tt.b, result, tt.expected)
            }
        })
    }
}

func TestCalculator(t *testing.T) {
    calc := NewCalculator()

    // Test default precision
    if calc.precision != 2 {
        t.Errorf("Expected default precision 2, got %d", calc.precision)
    }

    // Test precision setting
    calc.SetPrecision(5)
    if calc.precision != 5 {
        t.Errorf("Expected precision 5, got %d", calc.precision)
    }

    // Test precision bounds
    calc.SetPrecision(-1)
    if calc.precision != 0 {
        t.Errorf("Expected precision 0 (clamped), got %d", calc.precision)
    }

    calc.SetPrecision(15)
    if calc.precision != 10 {
        t.Errorf("Expected precision 10 (clamped), got %d", calc.precision)
    }
}
```

### Example Tests

```go
// example_test.go
package math_test

import (
    "fmt"
    "mymodule/math"
)

func ExampleAdd() {
    result := math.Add(2, 3)
    fmt.Println(result)
    // Output: 5
}

func ExampleCalculator() {
    calc := math.NewCalculator()
    calc.SetPrecision(3)

    fmt.Printf("Calculator created with precision: %d", 3)
    // Output: Calculator created with precision: 3
}

func ExampleCalculator_SetPrecision() {
    calc := math.NewCalculator()
    calc.SetPrecision(4)

    // Precision is now set to 4 decimal places
    fmt.Println("Precision set successfully")
    // Output: Precision set successfully
}
```

### Integration Tests

```go
// integration_test.go
//go:build integration
// +build integration

package math_test

import (
    "testing"
    "mymodule/math"
    "mymodule/database"
)

func TestCalculatorWithDatabase(t *testing.T) {
    // This test requires a database connection
    db, err := database.Connect("test_db")
    if err != nil {
        t.Skipf("Database not available: %v", err)
    }
    defer db.Close()

    calc := math.NewCalculator()
    // Test calculator with database operations
}

// Run integration tests with:
// go test -tags=integration
```

## Best Practices

Guidelines for effective package and module management.

### Package Design Principles

```go
// Good: Single responsibility
package user

// Handles only user-related operations
type User struct {
    ID   int
    Name string
}

func (u *User) Validate() error { /* ... */ }
func (u *User) Save() error { /* ... */ }

// Good: Clear interfaces
type UserRepository interface {
    Create(user *User) error
    GetByID(id int) (*User, error)
    Update(user *User) error
    Delete(id int) error
}

// Avoid: God packages
package utils

// Too many unrelated functions
func StringToInt(s string) int { /* ... */ }
func HashPassword(password string) string { /* ... */ }
func SendEmail(to, subject, body string) error { /* ... */ }
func ConnectDatabase() (*sql.DB, error) { /* ... */ }
```

### Module Organization

```go
// Good: Logical module structure
myproject/
├── go.mod
├── cmd/                    // Applications
│   ├── server/
│   └── cli/
├── internal/               // Private packages
│   ├── auth/
│   ├── database/
│   └── config/
├── pkg/                    // Public packages
│   ├── api/
│   └── client/
├── api/                    // API definitions
├── docs/                   // Documentation
└── scripts/                // Build scripts

// Avoid: Flat structure for large projects
myproject/
├── go.mod
├── main.go
├── user.go
├── product.go
├── order.go
├── auth.go
├── database.go
└── utils.go
```

### Dependency Management Best Practices

```go
// Good: Minimal dependencies
module myproject

require (
    github.com/gorilla/mux v1.8.0
    github.com/lib/pq v1.10.7
)

// Avoid: Too many dependencies
module myproject

require (
    github.com/pkg1/... v1.0.0
    github.com/pkg2/... v2.0.0
    github.com/pkg3/... v3.0.0
    // ... 50+ dependencies
)

// Good: Pin to specific versions
require github.com/critical/package v1.2.3

// Avoid: Using latest for critical dependencies
require github.com/critical/package latest
```

### Versioning Best Practices

```go
// Good: Semantic versioning
v1.0.0  // Initial release
v1.0.1  // Bug fix
v1.1.0  // New feature (backward compatible)
v2.0.0  // Breaking change

// Good: Pre-release versions
v1.0.0-alpha.1
v1.0.0-beta.1
v1.0.0-rc.1

// Good: Version tags in git
git tag v1.0.0
git tag v1.0.1
git push origin --tags
```

## Common Patterns

Frequently used patterns in Go packages and modules.

### Factory Pattern

```go
// Package config
package config

type Config struct {
    Database DatabaseConfig
    Server   ServerConfig
    Logger   LoggerConfig
}

type DatabaseConfig struct {
    Host     string
    Port     int
    Username string
    Password string
}

// Factory function
func New() *Config {
    return &Config{
        Database: DatabaseConfig{
            Host: "localhost",
            Port: 5432,
        },
        Server: ServerConfig{
            Port: 8080,
        },
        Logger: LoggerConfig{
            Level: "info",
        },
    }
}

// Factory with options
func NewWithOptions(opts ...Option) *Config {
    config := New()
    for _, opt := range opts {
        opt(config)
    }
    return config
}

type Option func(*Config)

func WithDatabaseHost(host string) Option {
    return func(c *Config) {
        c.Database.Host = host
    }
}

func WithServerPort(port int) Option {
    return func(c *Config) {
        c.Server.Port = port
    }
}
```

### Registry Pattern

```go
// Package registry
package registry

import (
    "fmt"
    "sync"
)

type Handler interface {
    Handle(data interface{}) error
}

type Registry struct {
    mu       sync.RWMutex
    handlers map[string]Handler
}

var defaultRegistry = &Registry{
    handlers: make(map[string]Handler),
}

func Register(name string, handler Handler) {
    defaultRegistry.Register(name, handler)
}

func Get(name string) (Handler, error) {
    return defaultRegistry.Get(name)
}

func (r *Registry) Register(name string, handler Handler) {
    r.mu.Lock()
    defer r.mu.Unlock()
    r.handlers[name] = handler
}

func (r *Registry) Get(name string) (Handler, error) {
    r.mu.RLock()
    defer r.mu.RUnlock()

    handler, exists := r.handlers[name]
    if !exists {
        return nil, fmt.Errorf("handler %s not found", name)
    }

    return handler, nil
}
```

### Plugin Pattern

```go
// Package plugin
package plugin

import "plugin"

type Plugin interface {
    Name() string
    Version() string
    Execute(args map[string]interface{}) (interface{}, error)
}

type Manager struct {
    plugins map[string]Plugin
}

func NewManager() *Manager {
    return &Manager{
        plugins: make(map[string]Plugin),
    }
}

func (m *Manager) LoadPlugin(path string) error {
    p, err := plugin.Open(path)
    if err != nil {
        return err
    }

    symbol, err := p.Lookup("Plugin")
    if err != nil {
        return err
    }

    plugin, ok := symbol.(Plugin)
    if !ok {
        return fmt.Errorf("invalid plugin interface")
    }

    m.plugins[plugin.Name()] = plugin
    return nil
}

func (m *Manager) GetPlugin(name string) (Plugin, error) {
    plugin, exists := m.plugins[name]
    if !exists {
        return nil, fmt.Errorf("plugin %s not found", name)
    }
    return plugin, nil
}
```

## Troubleshooting

Common issues and solutions for packages and modules.

### Module Issues

```bash
# Issue: Module not found
go: module example.com/mymodule: reading https://proxy.golang.org/example.com/mymodule/@v/list: 404 Not Found

# Solutions:
# 1. Check module path spelling
# 2. Ensure module is published
# 3. Check GOPROXY settings
export GOPROXY=direct

# Issue: Version conflicts
go: example.com/pkg@v1.0.0 requires example.com/dep@v2.0.0, but example.com/other@v1.0.0 requires example.com/dep@v1.0.0

# Solutions:
# 1. Use replace directive
replace example.com/dep => example.com/dep v2.0.0

# 2. Exclude problematic version
exclude example.com/dep v1.0.0

# Issue: Private module access
go: module example.com/private/repo: reading https://proxy.golang.org/example.com/private/repo/@v/list: 410 Gone

# Solutions:
export GOPRIVATE=example.com/private/*
export GONOPROXY=example.com/private/*
export GONOSUMDB=example.com/private/*
```

### Import Issues

```go
// Issue: Circular imports
package a
import "mymodule/b"

package b
import "mymodule/a"  // Error: import cycle

// Solutions:
// 1. Extract common functionality to separate package
package common
// Shared types and functions

package a
import "mymodule/common"

package b
import "mymodule/common"

// 2. Use interfaces to break dependency
package a
type BInterface interface {
    DoSomething()
}

// 3. Restructure packages
```

### Build Issues

```bash
# Issue: Build constraints not working
//go:build linux
package platform

# Solutions:
# 1. Check build tag syntax
# 2. Use correct file naming
# platform_linux.go
# platform_windows.go

# Issue: Missing dependencies
go: cannot find module providing package example.com/missing

# Solutions:
go mod tidy
go get example.com/missing

# Issue: Version mismatch
go: example.com/pkg@v1.0.0: parsing go.mod: module declares its path as: example.com/different

# Solutions:
# 1. Check module path in go.mod
# 2. Update import paths
# 3. Use replace directive if necessary
```

This comprehensive guide covers all aspects of packages and modules in Go, from basic concepts to advanced patterns and troubleshooting. Understanding these concepts will help you organize and manage Go projects effectively.
