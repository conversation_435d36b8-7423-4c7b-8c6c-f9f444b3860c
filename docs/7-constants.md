# Constants in Go

This comprehensive guide covers everything you need to know about constants in Go, from basic declarations to advanced patterns and performance implications.

## Table of Contents

1. [What are Constants](#what-are-constants)
2. [Constants vs Variables](#constants-vs-variables)
3. [Constant Declaration](#constant-declaration)
4. [Compile-time Evaluation](#compile-time-evaluation)
5. [Typed vs Untyped Constants](#typed-vs-untyped-constants)
6. [iota - Constant Generator](#iota---constant-generator)
7. [Constant Expressions](#constant-expressions)
8. [Performance Implications](#performance-implications)
9. [Best Practices](#best-practices)
10. [Common Patterns](#common-patterns)
11. [Advanced Usage](#advanced-usage)

## What are Constants

Constants in Go are **immutable values** that are determined at **compile time**. Unlike variables, constants cannot be changed after declaration and do not consume memory at runtime.

### Key Characteristics

```go
// Constants are compile-time values
const pi = 3.14159
const greeting = "Hello, <PERSON>!"
const maxRetries = 3

// Constants cannot be changed
// pi = 3.14  // Error: cannot assign to pi

// Constants must be compile-time computable
const now = time.Now()    // Error: not a compile-time constant
const size = len("hello") // OK: len of string literal is compile-time
```

### Fundamental Differences from Variables

| Aspect | Constants | Variables |
|--------|-----------|-----------|
| **Mutability** | Immutable | Mutable |
| **Evaluation** | Compile-time | Runtime |
| **Memory** | No allocation | Stack/heap allocation |
| **Assignment** | Once, at declaration | Multiple times |
| **Scope** | Same as variables | Same as constants |
| **Type flexibility** | Untyped constants | Strict typing |

## Constants vs Variables

Understanding the fundamental differences helps choose the right approach:

### Memory and Performance

```go
// Constants - no memory allocation
const bufferSize = 1024
const appName = "MyApp"
const pi = 3.14159

// Variables - memory allocation at runtime
var bufferSizeVar = 1024      // Memory allocated
var appNameVar = "MyApp"      // Memory allocated
var piVar = 3.14159          // Memory allocated

// Constants are embedded directly in code
func calculateArea(radius float64) float64 {
    return pi * radius * radius  // pi value embedded in machine code
}

// Variables require memory access
func calculateAreaVar(radius float64) float64 {
    return piVar * radius * radius  // Memory access required
}
```

### Compile-time vs Runtime

```go
// Constants must be known at compile time
const (
    maxUsers = 1000
    timeout  = 30 * time.Second  // OK: time.Second is a constant
    version  = "1.0.0"
)

// Variables can be set at runtime
var (
    currentUsers = getUserCount()     // Runtime function call
    startTime    = time.Now()         // Runtime value
    config       = loadConfig()      // Runtime initialization
)
```

### Type Flexibility

```go
// Untyped constants are flexible
const number = 42

var int8Val int8 = number      // OK: 42 fits in int8
var int64Val int64 = number    // OK: 42 fits in int64
var float32Val float32 = number // OK: 42 can be float32
var complexVal complex64 = number // OK: 42 can be complex

// Variables require explicit conversion
var numberVar int = 42
// var int8Val int8 = numberVar     // Error: cannot use int as int8
var int8Val int8 = int8(numberVar)  // OK: explicit conversion
```

## Constant Declaration

Go provides several ways to declare constants:

### Basic Declaration

```go
// Single constant
const pi = 3.14159
const greeting = "Hello"
const maxRetries = 3

// Multiple constants
const x, y, z = 1, 2, 3
const name, age = "John", 30
```

### Grouped Declaration

```go
// Grouped constants
const (
    statusOK       = 200
    statusNotFound = 404
    statusError    = 500
)

// Mixed types in group
const (
    appName    = "MyApp"
    version    = "1.0.0"
    maxUsers   = 1000
    debugMode  = true
)
```

### Typed Constants

```go
// Explicitly typed constants
const pi float64 = 3.14159
const greeting string = "Hello"
const maxRetries int = 3

// Type affects usage
const typedNumber int = 42
const untypedNumber = 42

var float32Val float32 = untypedNumber  // OK: untyped can convert
// var float32Val float32 = typedNumber // Error: cannot use int as float32
```

## Compile-time Evaluation

Constants are evaluated at compile time, which has important implications:

### Valid Constant Expressions

```go
// Arithmetic operations
const (
    a = 10
    b = 20
    sum = a + b           // 30
    product = a * b       // 200
    quotient = b / a      // 2
)

// String operations
const (
    firstName = "John"
    lastName  = "Doe"
    fullName  = firstName + " " + lastName  // "John Doe"
)

// Boolean operations
const (
    isProduction = false
    isDebug      = !isProduction  // true
    canEdit      = isProduction && isDebug  // false
)

// Built-in functions that work at compile time
const (
    messageLength = len("Hello, World!")  // 13
    realPart      = real(1 + 2i)         // 1
    imagPart      = imag(1 + 2i)         // 2
)
```

### Invalid Constant Expressions

```go
// Runtime functions cannot be used
const now = time.Now()              // Error: not compile-time
const randomNum = rand.Intn(100)    // Error: not compile-time
const fileSize = getFileSize()      // Error: not compile-time

// Variable values cannot be used
var x = 10
const y = x                         // Error: x is not constant

// Array/slice length from variables
var arr = [5]int{}
const arrLen = len(arr)             // Error: arr is not constant
```

## Typed vs Untyped Constants

One of Go's most powerful features is untyped constants, which provide flexibility while maintaining type safety.

### Untyped Constants

```go
// Untyped constants
const number = 42
const pi = 3.14159
const message = "hello"
const flag = true

// Untyped constants can be used with compatible types
var int8Val int8 = number        // OK: 42 fits in int8
var int64Val int64 = number      // OK: 42 fits in int64
var float32Val float32 = pi      // OK: 3.14159 fits in float32
var float64Val float64 = pi      // OK: 3.14159 fits in float64

// Automatic conversion based on context
func processInt(x int) { /* ... */ }
func processFloat(x float64) { /* ... */ }

processInt(number)    // number becomes int
processFloat(number)  // number becomes float64
```

### Typed Constants

```go
// Typed constants
const typedInt int = 42
const typedFloat float64 = 3.14159
const typedString string = "hello"

// Typed constants require explicit conversion
var int8Val int8 = int8(typedInt)      // Explicit conversion required
var float32Val float32 = float32(typedFloat) // Explicit conversion required

// Cannot use typed constant with incompatible type
func processFloat(x float64) { /* ... */ }
// processFloat(typedInt)  // Error: cannot use int as float64
processFloat(float64(typedInt))  // OK: explicit conversion
```

### Untyped Constant Precision

```go
// Untyped constants have arbitrary precision
const hugePi = 3.1415926535897932384626433832795028841971693993751058209749445923

// When assigned to typed variable, precision is limited by type
var float32Pi float32 = hugePi  // Limited to float32 precision
var float64Pi float64 = hugePi  // Limited to float64 precision

// Compile-time arithmetic maintains high precision
const result = hugePi * hugePi * hugePi  // High precision calculation
```

## iota - Constant Generator

`iota` is Go's constant generator that creates sequential integer constants.

### Basic iota Usage

```go
// Basic iota sequence
const (
    Sunday = iota    // 0
    Monday           // 1
    Tuesday          // 2
    Wednesday        // 3
    Thursday         // 4
    Friday           // 5
    Saturday         // 6
)

// iota resets in each const block
const (
    January = iota   // 0
    February         // 1
    March            // 2
)

const (
    Spring = iota    // 0 (resets)
    Summer           // 1
    Fall             // 2
    Winter           // 3
)
```

### iota with Expressions

```go
// iota with arithmetic
const (
    KB = 1 << (10 * iota)  // 1 << (10 * 0) = 1
    MB                     // 1 << (10 * 1) = 1024
    GB                     // 1 << (10 * 2) = 1048576
    TB                     // 1 << (10 * 3) = 1073741824
)

// iota with multiplication
const (
    _   = iota * 10  // 0 * 10 = 0 (ignored)
    Ten              // 1 * 10 = 10
    Twenty           // 2 * 10 = 20
    Thirty           // 3 * 10 = 30
)

// iota with custom expressions
const (
    First  = iota + 1  // 0 + 1 = 1
    Second             // 1 + 1 = 2
    Third              // 2 + 1 = 3
)
```

### Advanced iota Patterns

```go
// Bit flags using iota
const (
    FlagA = 1 << iota  // 1 << 0 = 1
    FlagB              // 1 << 1 = 2
    FlagC              // 1 << 2 = 4
    FlagD              // 1 << 3 = 8
    FlagE              // 1 << 4 = 16
)

// Skipping values
const (
    _      = iota      // 0 (ignored)
    First              // 1
    _                  // 2 (ignored)
    Third              // 3
    Fourth             // 4
)

// Multiple constants per line
const (
    a, b = iota, iota + 10  // a = 0, b = 10
    c, d                    // c = 1, d = 11
    e, f                    // e = 2, f = 12
)

// Complex expressions
const (
    _           = iota                    // 0
    StatusOK    = 200 + iota             // 200 + 1 = 201
    StatusFound = 300 + iota             // 300 + 2 = 302
    StatusError = 400 + iota             // 400 + 3 = 403
)
```

### iota Best Practices

```go
// Good: Start with meaningful value
const (
    StatusUnknown = iota  // 0
    StatusPending         // 1
    StatusRunning         // 2
    StatusComplete        // 3
    StatusFailed          // 4
)

// Good: Use blank identifier for zero value
const (
    _             = iota  // 0 (skip)
    PriorityLow           // 1
    PriorityMedium        // 2
    PriorityHigh          // 3
)

// Good: Group related constants
const (
    // HTTP Status Codes
    StatusOK                   = 200
    StatusCreated              = 201
    StatusAccepted             = 202
    StatusNoContent            = 204

    // Error Status Codes
    StatusBadRequest           = 400
    StatusUnauthorized         = 401
    StatusForbidden            = 403
    StatusNotFound             = 404
    StatusInternalServerError  = 500
)
```

## Constant Expressions

Constants can be created using various expressions that are evaluated at compile time.

### Arithmetic Expressions

```go
const (
    // Basic arithmetic
    sum        = 10 + 20        // 30
    difference = 50 - 30        // 20
    product    = 6 * 7          // 42
    quotient   = 84 / 2         // 42
    remainder  = 17 % 5         // 2

    // Floating-point arithmetic
    pi         = 3.14159
    radius     = 5.0
    area       = pi * radius * radius  // 78.53975

    // Integer division
    intDiv     = 7 / 2          // 3 (integer division)
    floatDiv   = 7.0 / 2.0      // 3.5 (floating-point division)
)
```

### Bitwise Operations

```go
const (
    // Bitwise operations
    a = 0b1010  // 10 in binary
    b = 0b1100  // 12 in binary

    bitwiseAnd = a & b   // 0b1000 = 8
    bitwiseOr  = a | b   // 0b1110 = 14
    bitwiseXor = a ^ b   // 0b0110 = 6
    bitwiseNot = ^a      // Bitwise NOT

    // Bit shifting
    leftShift  = a << 2  // 0b101000 = 40
    rightShift = a >> 1  // 0b0101 = 5
)
```

### String Operations

```go
const (
    // String concatenation
    firstName = "John"
    lastName  = "Doe"
    fullName  = firstName + " " + lastName  // "John Doe"

    // String with escape sequences
    message   = "Hello\nWorld"
    path      = "C:\\Users\\<USER>\d+\.\d+`
    multiLine = `This is a
multi-line
string`
)
```

### Boolean Expressions

```go
const (
    // Boolean constants
    isProduction = false
    isDebug      = true

    // Boolean operations
    canEdit      = isProduction && isDebug  // false
    shouldLog    = isProduction || isDebug  // true
    isDisabled   = !isProduction            // true

    // Comparison operations (with constants)
    maxSize      = 1000
    minSize      = 100
    isValidRange = maxSize > minSize        // true
)
```

## Performance Implications

Constants offer significant performance benefits over variables due to their compile-time nature.

### Memory Usage

```go
// Constants - no memory allocation
const (
    bufferSize = 1024
    maxRetries = 3
    timeout    = 30 * time.Second
    appName    = "MyApplication"
)

// Variables - memory allocation required
var (
    bufferSizeVar = 1024              // 8 bytes (int)
    maxRetriesVar = 3                 // 8 bytes (int)
    timeoutVar    = 30 * time.Second  // 8 bytes (Duration)
    appNameVar    = "MyApplication"   // 16 bytes (string header) + string data
)

// Constants are embedded in machine code
func processBuffer() {
    buffer := make([]byte, bufferSize)  // bufferSize (1024) embedded in code
    // vs
    buffer := make([]byte, bufferSizeVar) // Memory access to get value
}
```

### Compilation Optimizations

```go
// Constants enable compile-time optimizations
const multiplier = 8

func optimizedCalculation(x int) int {
    return x * multiplier  // Compiler can optimize this to x << 3 (bit shift)
}

// Variables prevent some optimizations
var multiplierVar = 8

func unoptimizedCalculation(x int) int {
    return x * multiplierVar  // Compiler must perform multiplication
}
```

### Inlining Benefits

```go
// Constants are perfect for inlining
const (
    pi     = 3.14159
    radius = 5.0
)

func calculateArea() float64 {
    // Entire calculation can be computed at compile time
    return pi * radius * radius  // Result: 78.53975 (compile-time constant)
}

// Variables require runtime calculation
var (
    piVar     = 3.14159
    radiusVar = 5.0
)

func calculateAreaVar() float64 {
    // Must be calculated at runtime
    return piVar * radiusVar * radiusVar
}
```

## Best Practices

Following best practices for constants improves code readability and maintainability.

### Naming Conventions

```go
// Good: Descriptive names
const (
    DefaultTimeout     = 30 * time.Second
    MaxRetryAttempts   = 3
    DatabaseDriverName = "postgres"
    APIVersion         = "v1"
)

// Avoid: Abbreviated or unclear names
const (
    TO  = 30  // Unclear
    MAX = 3   // Too generic
    DB  = "postgres"  // Abbreviated
)

// Good: Group related constants
const (
    // HTTP Status Codes
    StatusOK                  = 200
    StatusCreated             = 201
    StatusBadRequest          = 400
    StatusInternalServerError = 500
)

// Good: Use meaningful prefixes for enums
const (
    OrderStatusPending   = "pending"
    OrderStatusProcessing = "processing"
    OrderStatusCompleted  = "completed"
    OrderStatusCancelled  = "cancelled"
)
```

### Organization Patterns

```go
// Pattern 1: Group by functionality
const (
    // Database configuration
    DBHost     = "localhost"
    DBPort     = 5432
    DBName     = "myapp"
    DBTimeout  = 30 * time.Second

    // Cache configuration
    CacheHost    = "localhost"
    CachePort    = 6379
    CacheTTL     = 1 * time.Hour

    // API configuration
    APIPort      = 8080
    APITimeout   = 10 * time.Second
    APIRateLimit = 1000
)

// Pattern 2: Separate files for different domains
// constants/database.go
const (
    DefaultDBHost = "localhost"
    DefaultDBPort = 5432
)

// constants/api.go
const (
    DefaultAPIPort = 8080
    APIVersion     = "v1"
)
```

### Error Constants

```go
// Good: Define error constants
const (
    ErrMsgUserNotFound     = "user not found"
    ErrMsgInvalidPassword  = "invalid password"
    ErrMsgDatabaseError    = "database connection failed"
    ErrMsgUnauthorized     = "unauthorized access"
)

// Usage with error wrapping
func authenticateUser(username, password string) error {
    user, err := findUser(username)
    if err != nil {
        return fmt.Errorf("%s: %w", ErrMsgUserNotFound, err)
    }

    if !validatePassword(user, password) {
        return errors.New(ErrMsgInvalidPassword)
    }

    return nil
}
```

### Configuration Constants

```go
// Good: Environment-specific constants
const (
    // Development environment
    DevDBHost     = "localhost"
    DevDBPort     = 5432
    DevLogLevel   = "debug"
    DevCacheSize  = 100

    // Production environment
    ProdDBHost    = "prod-db.example.com"
    ProdDBPort    = 5432
    ProdLogLevel  = "info"
    ProdCacheSize = 10000
)

// Good: Build-time constants
// +build dev
const Environment = "development"

// +build prod
const Environment = "production"
```

## Common Patterns

Constants are commonly used in specific patterns that solve recurring problems.

### Enum Pattern

```go
// Status enum using iota
type Status int

const (
    StatusUnknown Status = iota
    StatusPending
    StatusRunning
    StatusCompleted
    StatusFailed
)

// String method for enum
func (s Status) String() string {
    switch s {
    case StatusPending:
        return "pending"
    case StatusRunning:
        return "running"
    case StatusCompleted:
        return "completed"
    case StatusFailed:
        return "failed"
    default:
        return "unknown"
    }
}

// Validation method
func (s Status) IsValid() bool {
    return s >= StatusPending && s <= StatusFailed
}
```

### Bit Flag Pattern

```go
// Permission flags
type Permission int

const (
    PermissionRead Permission = 1 << iota  // 1
    PermissionWrite                        // 2
    PermissionExecute                      // 4
    PermissionDelete                       // 8
    PermissionAdmin                        // 16
)

// Helper methods
func (p Permission) Has(permission Permission) bool {
    return p&permission != 0
}

func (p Permission) Add(permission Permission) Permission {
    return p | permission
}

func (p Permission) Remove(permission Permission) Permission {
    return p &^ permission
}

// Usage
var userPerms Permission = PermissionRead | PermissionWrite
if userPerms.Has(PermissionWrite) {
    // User can write
}
```

### Configuration Pattern

```go
// Application configuration
const (
    // Server configuration
    DefaultPort        = 8080
    DefaultHost        = "localhost"
    DefaultTimeout     = 30 * time.Second
    DefaultMaxConns    = 1000

    // Database configuration
    DefaultDBMaxConns  = 25
    DefaultDBTimeout   = 5 * time.Second
    DefaultDBRetries   = 3

    // Cache configuration
    DefaultCacheTTL    = 1 * time.Hour
    DefaultCacheSize   = 1000
)

// Config struct with defaults
type Config struct {
    Port     int
    Host     string
    Timeout  time.Duration
    MaxConns int
}

func NewConfig() *Config {
    return &Config{
        Port:     DefaultPort,
        Host:     DefaultHost,
        Timeout:  DefaultTimeout,
        MaxConns: DefaultMaxConns,
    }
}
```

## Advanced Usage

Advanced constant patterns for complex scenarios.

### Mathematical Constants

```go
// Mathematical constants with high precision
const (
    Pi  = 3.1415926535897932384626433832795028841971693993751058209749445923
    E   = 2.7182818284590452353602874713526624977572470936999595749669676277
    Phi = 1.6180339887498948482045868343656381177203091798057628621354486227

    // Derived constants
    TwoPi      = 2 * Pi
    PiOver2    = Pi / 2
    PiOver4    = Pi / 4
    SqrtPi     = 1.7724538509055160272981674833411451827975494561223871282138
    Sqrt2      = 1.4142135623730950488016887242096980785696718753769480731766
)

// Physical constants
const (
    SpeedOfLight      = 299792458        // m/s
    PlanckConstant    = 6.62607015e-34   // J⋅s
    AvogadroConstant  = 6.02214076e23    // mol⁻¹
    BoltzmannConstant = 1.380649e-23     // J/K
)
```

### Complex iota Patterns

```go
// Multi-dimensional iota
const (
    _         = iota                    // 0
    KB        = 1 << (10 * iota)       // 1024
    MB                                 // 1048576
    GB                                 // 1073741824
    TB                                 // 1099511627776
    PB                                 // 1125899906842624
)

// Time-based constants
const (
    _              = iota
    Nanosecond     = 1
    Microsecond    = 1000 * Nanosecond
    Millisecond    = 1000 * Microsecond
    Second         = 1000 * Millisecond
    Minute         = 60 * Second
    Hour           = 60 * Minute
    Day            = 24 * Hour
    Week           = 7 * Day
)

// Protocol constants
const (
    ProtocolVersion1 = 1 + iota  // 1
    ProtocolVersion2             // 2
    ProtocolVersion3             // 3

    // Skip version 4 for compatibility
    _                            // 4

    ProtocolVersion5             // 5
    ProtocolVersionLatest = ProtocolVersion5
)
```

### Type-Safe Constants

```go
// Type-safe constant pattern
type DatabaseType string

const (
    DatabaseMySQL      DatabaseType = "mysql"
    DatabasePostgreSQL DatabaseType = "postgresql"
    DatabaseSQLite     DatabaseType = "sqlite"
    DatabaseMongoDB    DatabaseType = "mongodb"
)

// Validation
func (dt DatabaseType) IsValid() bool {
    switch dt {
    case DatabaseMySQL, DatabasePostgreSQL, DatabaseSQLite, DatabaseMongoDB:
        return true
    default:
        return false
    }
}

// Usage ensures type safety
func ConnectDatabase(dbType DatabaseType) error {
    if !dbType.IsValid() {
        return fmt.Errorf("invalid database type: %s", dbType)
    }
    // Connect to database
    return nil
}

// Compile-time safety
ConnectDatabase(DatabaseMySQL)     // OK
// ConnectDatabase("invalid")      // Compile error
```

This comprehensive guide covers all aspects of constants in Go, from basic declarations to advanced patterns. Constants are a powerful feature that provides type safety, performance benefits, and code clarity when used properly.
