# Go CLI Commands Reference

This comprehensive guide covers all Go command-line interface (CLI) commands, their options, and practical usage examples. The Go toolchain provides a rich set of commands for building, testing, and managing Go projects.

## Table of Contents

1. [Basic Commands](#basic-commands)
2. [Module Management](#module-management)
3. [Testing and Quality](#testing-and-quality)
4. [Information and Help](#information-and-help)
5. [Advanced Commands](#advanced-commands)
6. [Build and Compilation](#build-and-compilation)
7. [Package Management](#package-management)
8. [Development Tools](#development-tools)
9. [Cross-Platform Development](#cross-platform-development)
10. [Command Flags Reference](#command-flags-reference)

## Basic Commands

### `go run`

Compiles and runs Go programs directly without creating an executable file.

**Syntax:**
```bash
go run [build flags] [-exec xprog] package [arguments...]
```

**Examples:**
```bash
# Run a single file
go run main.go

# Run with arguments
go run main.go arg1 arg2

# Run multiple files
go run main.go utils.go

# Run with build flags
go run -race main.go

# Run from a different directory
go run ./cmd/server

# Run with custom execution
go run -exec echo main.go
```

**Common Flags:**
- `-race`: Enable race detector
- `-work`: Print temporary work directory
- `-x`: Print commands being executed
- `-v`: Verbose output

### `go build`

Compiles packages and dependencies into executable files or package archives.

**Syntax:**
```bash
go build [-o output] [build flags] [packages]
```

**Examples:**
```bash
# Build current package
go build

# Build with custom output name
go build -o myapp

# Build specific package
go build ./cmd/server

# Build for different OS/architecture
GOOS=linux GOARCH=amd64 go build

# Build with optimization disabled
go build -gcflags="-N -l"

# Build with custom linker flags
go build -ldflags="-X main.version=1.0.0"

# Build multiple packages
go build ./cmd/...
```

**Common Flags:**
- `-o output`: Specify output file name
- `-race`: Enable race detector
- `-v`: Verbose output
- `-x`: Print commands
- `-ldflags`: Pass flags to linker
- `-gcflags`: Pass flags to compiler
- `-tags`: Build tags
- `-trimpath`: Remove file system paths from executable

### `go install`

Compiles and installs packages and dependencies.

**Syntax:**
```bash
go install [build flags] [packages]
```

**Examples:**
```bash
# Install current package
go install

# Install specific package
go install ./cmd/server

# Install from remote repository
go install github.com/user/repo@latest

# Install specific version
go install github.com/user/repo@v1.2.3

# Install to custom location
GOBIN=/custom/path go install

# Install all executables in project
go install ./cmd/...
```

**Key Points:**
- Installs to `$GOPATH/bin` or `$GOBIN`
- Creates executable files
- Downloads dependencies if needed

### `go clean`

Removes object files and cached files.

**Syntax:**
```bash
go clean [clean flags] [packages]
```

**Examples:**
```bash
# Clean current package
go clean

# Clean with cache
go clean -cache

# Clean module cache
go clean -modcache

# Clean test cache
go clean -testcache

# Clean everything
go clean -cache -modcache -testcache

# Clean specific package
go clean ./cmd/server

# Verbose clean
go clean -x
```

**Common Flags:**
- `-cache`: Remove build cache
- `-modcache`: Remove module cache
- `-testcache`: Remove test cache
- `-x`: Print remove commands
- `-n`: Print commands without executing

## Module Management

### `go mod init`

Initializes a new module in the current directory.

**Syntax:**
```bash
go mod init [module-path]
```

**Examples:**
```bash
# Initialize with module path
go mod init example.com/myproject

# Initialize with GitHub path
go mod init github.com/username/repo

# Initialize in existing project
go mod init myproject
```

### `go mod tidy`

Adds missing modules and removes unused modules.

**Syntax:**
```bash
go mod tidy [-e] [-v] [-x] [-go=version]
```

**Examples:**
```bash
# Basic tidy
go mod tidy

# Verbose tidy
go mod tidy -v

# Continue on errors
go mod tidy -e

# Set Go version
go mod tidy -go=1.21
```

### `go mod download`

Downloads modules to local cache.

**Syntax:**
```bash
go mod download [-x] [-json] [modules]
```

**Examples:**
```bash
# Download all dependencies
go mod download

# Download specific module
go mod download github.com/gorilla/mux

# Download with JSON output
go mod download -json

# Download and print commands
go mod download -x
```

### `go mod verify`

Verifies dependencies have expected content.

**Syntax:**
```bash
go mod verify
```

**Examples:**
```bash
# Verify all modules
go mod verify

# Use in CI/CD
go mod verify || exit 1
```

### `go mod graph`

Prints module requirement graph.

**Syntax:**
```bash
go mod graph
```

**Examples:**
```bash
# Show dependency graph
go mod graph

# Filter specific module
go mod graph | grep github.com/specific/module
```

### `go mod why`

Explains why packages or modules are needed.

**Syntax:**
```bash
go mod why [-m] [-vendor] packages...
```

**Examples:**
```bash
# Why is package needed
go mod why github.com/gorilla/mux

# Why is module needed
go mod why -m github.com/gorilla/mux

# Check multiple packages
go mod why package1 package2
```

### `go mod edit`

Edits go.mod from tools or scripts.

**Syntax:**
```bash
go mod edit [editing flags] [go.mod]
```

**Examples:**
```bash
# Add requirement
go mod edit -require=github.com/gorilla/mux@v1.8.0

# Remove requirement
go mod edit -droprequire=github.com/old/package

# Set Go version
go mod edit -go=1.21

# Add replace directive
go mod edit -replace=old=new

# Format go.mod
go mod edit -fmt
```

**Common Flags:**
- `-require=path@version`: Add requirement
- `-droprequire=path`: Remove requirement
- `-replace=old[@v]=new[@v]`: Add replacement
- `-dropreplace=old[@v]`: Remove replacement
- `-go=version`: Set Go version
- `-fmt`: Format go.mod file

## Testing and Quality

### `go test`

Runs tests for packages.

**Syntax:**
```bash
go test [build/test flags] [packages] [build/test flags & test binary flags]
```

**Examples:**
```bash
# Run tests in current package
go test

# Run tests with verbose output
go test -v

# Run specific test
go test -run TestFunctionName

# Run tests with coverage
go test -cover

# Run tests with detailed coverage
go test -coverprofile=coverage.out
go tool cover -html=coverage.out

# Run tests with race detection
go test -race

# Run benchmarks
go test -bench=.

# Run specific benchmark
go test -bench=BenchmarkFunction

# Run tests in all subdirectories
go test ./...

# Run tests with timeout
go test -timeout 30s

# Run tests multiple times
go test -count=5

# Run tests in parallel
go test -parallel 4

# Run short tests only
go test -short

# Generate test coverage report
go test -coverprofile=coverage.out ./...
go tool cover -func=coverage.out
```

**Common Flags:**
- `-v`: Verbose output
- `-run regexp`: Run specific tests matching regexp
- `-bench regexp`: Run benchmarks matching regexp
- `-cover`: Enable coverage analysis
- `-coverprofile file`: Write coverage profile to file
- `-race`: Enable race detector
- `-timeout duration`: Test timeout
- `-count n`: Run tests n times
- `-parallel n`: Run tests in parallel
- `-short`: Run short tests
- `-failfast`: Stop on first test failure

### `go fmt`

Formats Go source code according to Go standards.

**Syntax:**
```bash
go fmt [packages]
```

**Examples:**
```bash
# Format current package
go fmt

# Format specific file
go fmt main.go

# Format all packages recursively
go fmt ./...

# Format and show differences
gofmt -d .

# Format with custom options
gofmt -w -s .
```

**Key Points:**
- Automatically fixes formatting
- Uses `gofmt` tool internally
- Standard formatting across all Go code

### `go vet`

Examines Go source code and reports suspicious constructs.

**Syntax:**
```bash
go vet [build flags] [vet flags] [packages]
```

**Examples:**
```bash
# Vet current package
go vet

# Vet all packages
go vet ./...

# Vet with specific checks
go vet -printf=false

# Vet with all checks
go vet -all

# Vet and show progress
go vet -x ./...
```

**Common Checks:**
- Unreachable code
- Incorrect printf format strings
- Incorrect struct tags
- Suspicious constructs
- Potential bugs

### `go doc`

Shows documentation for packages, types, functions, and methods.

**Syntax:**
```bash
go doc [doc flags] [package|[package.]symbol[.methodOrField]]
```

**Examples:**
```bash
# Show package documentation
go doc fmt

# Show function documentation
go doc fmt.Println

# Show type documentation
go doc http.Server

# Show method documentation
go doc http.Server.ListenAndServe

# Show current package documentation
go doc

# Show all documentation
go doc -all fmt

# Show unexported symbols
go doc -u fmt

# Show source code
go doc -src fmt.Println
```

**Common Flags:**
- `-all`: Show all documentation
- `-c`: Respect case in symbol matching
- `-cmd`: Show commands as well as packages
- `-short`: One-line representation for each symbol
- `-src`: Show source code
- `-u`: Show unexported symbols

## Information and Help

### `go version`

Prints Go version information.

**Syntax:**
```bash
go version [-m] [-v] [file ...]
```

**Examples:**
```bash
# Show Go version
go version

# Show module information in binary
go version -m ./myapp

# Verbose version information
go version -v
```

### `go env`

Prints Go environment information.

**Syntax:**
```bash
go env [-json] [-u] [-w] [var ...]
```

**Examples:**
```bash
# Show all environment variables
go env

# Show specific variables
go env GOPATH GOROOT

# Show in JSON format
go env -json

# Set environment variable
go env -w GOPROXY=direct

# Unset environment variable
go env -u GOPROXY

# Show changed variables only
go env -changed
```

**Common Variables:**
- `GOROOT`: Go installation directory
- `GOPATH`: Workspace directory
- `GOOS`: Target operating system
- `GOARCH`: Target architecture
- `GO111MODULE`: Module mode
- `GOPROXY`: Module proxy
- `GOSUMDB`: Checksum database

### `go list`

Lists packages or modules.

**Syntax:**
```bash
go list [-f format] [-json] [-m] [list flags] [packages]
```

**Examples:**
```bash
# List current package
go list

# List all packages in module
go list ./...

# List with JSON output
go list -json

# List modules
go list -m all

# List available updates
go list -m -u all

# List with custom format
go list -f '{{.ImportPath}} {{.Dir}}'

# List dependencies
go list -deps

# List test files
go list -test

# List compiled packages
go list -compiled
```

**Common Flags:**
- `-json`: JSON output
- `-m`: List modules instead of packages
- `-u`: Show available updates
- `-f format`: Custom output format
- `-deps`: List dependencies
- `-test`: Include test packages

### `go help`

Shows help information for Go commands.

**Syntax:**
```bash
go help [command]
```

**Examples:**
```bash
# General help
go help

# Help for specific command
go help build

# Help for module commands
go help mod

# Help for environment variables
go help environment

# Help for build constraints
go help buildconstraint

# Help for file types
go help filetype
```

## Advanced Commands

### `go generate`

Runs commands described by directives within existing files.

**Syntax:**
```bash
go generate [-run regexp] [-n] [-v] [-x] [build flags] [file.go... | packages]
```

**Examples:**
```bash
# Generate for current package
go generate

# Generate for all packages
go generate ./...

# Generate with specific pattern
go generate -run="mockgen"

# Dry run (show commands without executing)
go generate -n

# Verbose output
go generate -v

# Show commands being executed
go generate -x
```

**Usage in Code:**
```go
//go:generate stringer -type=Status
//go:generate mockgen -source=interface.go -destination=mock.go
//go:generate protoc --go_out=. *.proto
```

### `go work`

Manages Go workspaces (Go 1.18+).

**Syntax:**
```bash
go work <command> [arguments]
```

**Commands:**
```bash
# Initialize workspace
go work init ./module1 ./module2

# Add modules to workspace
go work use ./module3

# Remove modules from workspace
go work use -r ./module3

# Edit workspace file
go work edit -go=1.21

# Sync workspace
go work sync
```

### `go tool`

Runs specified go tool.

**Syntax:**
```bash
go tool [-n] command [args...]
```

**Examples:**
```bash
# List available tools
go tool

# Run specific tool
go tool compile main.go
go tool link main.o

# Coverage tools
go tool cover -html=coverage.out
go tool cover -func=coverage.out

# Profiling tools
go tool pprof cpu.prof
go tool pprof mem.prof

# Assembly
go tool objdump binary
go tool nm binary

# Documentation
go tool doc fmt.Println
```

### `go get`

Downloads and installs packages and dependencies.

**Syntax:**
```bash
go get [-d] [-f] [-t] [-u] [-v] [-fix] [-insecure] [build flags] [packages]
```

**Examples:**
```bash
# Get latest version
go get github.com/gorilla/mux

# Get specific version
go get github.com/gorilla/mux@v1.8.0

# Get specific commit
go get github.com/gorilla/mux@abc123

# Get and update dependencies
go get -u ./...

# Get without building
go get -d github.com/gorilla/mux

# Get test dependencies
go get -t ./...

# Get and fix imports
go get -fix ./...

# Remove dependency
go get github.com/old/package@none
```

**Common Flags:**
- `-u`: Update to latest version
- `-d`: Download only, don't install
- `-t`: Consider test dependencies
- `-f`: Force get
- `-fix`: Run fix tool on downloaded packages
- `-insecure`: Allow insecure connections

## Package Management

### `go mod vendor`

Creates a vendor directory with dependencies.

**Syntax:**
```bash
go mod vendor [-e] [-v] [-o outdir]
```

**Examples:**
```bash
# Create vendor directory
go mod vendor

# Vendor with verbose output
go mod vendor -v

# Vendor to custom directory
go mod vendor -o ./third_party

# Continue on errors
go mod vendor -e
```

### `go mod replace`

Replaces module versions in go.mod.

**Examples:**
```bash
# Replace with local path
go mod edit -replace=github.com/old/module=./local/path

# Replace with different version
go mod edit -replace=github.com/old/module@v1.0.0=github.com/new/module@v2.0.0

# Remove replacement
go mod edit -dropreplace=github.com/old/module
```

## Development Tools

### `go fix`

Updates packages to use new APIs.

**Syntax:**
```bash
go fix [packages]
```

**Examples:**
```bash
# Fix current package
go fix

# Fix all packages
go fix ./...

# Fix specific package
go fix ./cmd/server
```

### `go imports`

Updates Go import lines, adding missing ones and removing unreferenced ones.

**Installation and Usage:**
```bash
# Install goimports
go install golang.org/x/tools/cmd/goimports@latest

# Format and fix imports
goimports -w .

# Show differences
goimports -d .

# Format specific file
goimports -w main.go
```

### `go lint`

Reports style mistakes in Go code.

**Installation and Usage:**
```bash
# Install golint
go install golang.org/x/lint/golint@latest

# Lint current package
golint

# Lint all packages
golint ./...

# Lint specific file
golint main.go
```

## Cross-Platform Development

### Cross-Compilation

Build for different operating systems and architectures.

**Syntax:**
```bash
GOOS=target_os GOARCH=target_arch go build
```

**Examples:**
```bash
# Build for Linux 64-bit
GOOS=linux GOARCH=amd64 go build -o myapp-linux

# Build for Windows 64-bit
GOOS=windows GOARCH=amd64 go build -o myapp.exe

# Build for macOS 64-bit
GOOS=darwin GOARCH=amd64 go build -o myapp-mac

# Build for ARM64 (Apple Silicon)
GOOS=darwin GOARCH=arm64 go build -o myapp-mac-arm64

# Build for Raspberry Pi
GOOS=linux GOARCH=arm GOARM=7 go build -o myapp-pi

# Build for multiple platforms
for GOOS in darwin linux windows; do
    for GOARCH in 386 amd64; do
        go build -o myapp-$GOOS-$GOARCH
    done
done
```

**Supported Platforms:**
```bash
# List all supported platforms
go tool dist list

# Common combinations:
# linux/386, linux/amd64, linux/arm, linux/arm64
# darwin/amd64, darwin/arm64
# windows/386, windows/amd64
# freebsd/386, freebsd/amd64
# openbsd/386, openbsd/amd64
```

### Build Constraints

Control compilation with build tags.

**Examples:**
```go
// +build linux darwin
// +build !windows

package main

// This file only compiles on Linux or macOS, but not Windows
```

**Usage:**
```bash
# Build with specific tags
go build -tags="production,mysql"

# Build without certain tags
go build -tags="!debug"

# Multiple tags
go build -tags="linux,amd64,production"
```

## Command Flags Reference

### Global Flags

These flags work with most Go commands:

- `-a`: Force rebuilding of packages
- `-n`: Print commands without executing
- `-p n`: Number of programs to run in parallel
- `-race`: Enable data race detection
- `-msan`: Enable interoperation with memory sanitizer
- `-v`: Print names of packages being compiled
- `-work`: Print temporary work directory name
- `-x`: Print commands being executed

### Build Flags

Specific to build-related commands:

- `-o output`: Output file or directory
- `-i`: Install packages that are dependencies
- `-ldflags 'flag list'`: Arguments to pass to linker
- `-gcflags 'flag list'`: Arguments to pass to compiler
- `-asmflags 'flag list'`: Arguments to pass to assembler
- `-gccgoflags 'flag list'`: Arguments to pass to gccgo
- `-tags 'tag list'`: Build tags
- `-trimpath`: Remove file system paths from executable

### Test Flags

Specific to testing:

- `-bench regexp`: Run benchmarks matching regexp
- `-benchmem`: Print memory allocation statistics
- `-benchtime t`: Run benchmarks for duration t
- `-blockprofile file`: Write goroutine blocking profile
- `-blockprofilerate rate`: Set blocking profile rate
- `-count n`: Run tests and benchmarks n times
- `-cover`: Enable coverage analysis
- `-covermode set,count,atomic`: Set coverage mode
- `-coverpkg pattern`: Apply coverage analysis to packages
- `-coverprofile file`: Write coverage profile
- `-cpu 1,2,4`: Specify GOMAXPROCS values
- `-cpuprofile file`: Write CPU profile
- `-failfast`: Stop after first test failure
- `-memprofile file`: Write memory profile
- `-memprofilerate rate`: Set memory profiling rate
- `-mutexprofile file`: Write mutex contention profile
- `-outputdir directory`: Place output files in directory
- `-parallel n`: Allow parallel execution of test functions
- `-run regexp`: Run tests matching regexp
- `-short`: Tell long-running tests to shorten their run time
- `-timeout t`: Panic test binary after duration t
- `-trace file`: Write execution trace
- `-v`: Verbose output

### Module Flags

Specific to module commands:

- `-e`: Continue with errors
- `-json`: Print in JSON format
- `-m`: Module mode
- `-u`: Check for updates
- `-vendor`: Use vendor directory

## Best Practices

### Development Workflow

```bash
# 1. Initialize project
go mod init myproject

# 2. Add dependencies
go get github.com/gorilla/mux

# 3. Format code
go fmt ./...

# 4. Check for issues
go vet ./...

# 5. Run tests
go test ./...

# 6. Build application
go build -o myapp

# 7. Clean up
go mod tidy
```

### CI/CD Pipeline

```bash
# Typical CI/CD commands
go mod download
go mod verify
go fmt ./...
go vet ./...
go test -race -coverprofile=coverage.out ./...
go build -ldflags="-X main.version=$VERSION"
```

### Performance Analysis

```bash
# CPU profiling
go test -cpuprofile=cpu.prof -bench=.
go tool pprof cpu.prof

# Memory profiling
go test -memprofile=mem.prof -bench=.
go tool pprof mem.prof

# Race detection
go test -race ./...
go build -race
```

This comprehensive reference covers all major Go CLI commands and their usage patterns. Use it as a quick reference for your Go development workflow.