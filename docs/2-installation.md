# Go Installation Guide

This guide covers multiple ways to install Go on different operating systems. Choose the method that best suits your needs.

## System Requirements

Before installing Go, ensure your system meets the following requirements:

- **Operating System**: Linux, macOS, Windows, FreeBSD, or OpenBSD
- **Architecture**: amd64, 386, arm, arm64, ppc64le, s390x
- **Disk Space**: At least 500MB of available disk space
- **Memory**: Minimum 1GB RAM (2GB recommended for development)

## Installation Methods

### Method 1: Official Binary Installation (Recommended)

This is the most straightforward method and works on all supported platforms.

#### Step 1: Download Go

Visit the [official Go downloads page](https://go.dev/dl/) and download the appropriate package for your system:

- **Linux**: `go1.21.x.linux-amd64.tar.gz`
- **macOS**: `go1.21.x.darwin-amd64.pkg` (Intel) or `go1.21.x.darwin-arm64.pkg` (Apple Silicon)
- **Windows**: `go1.21.x.windows-amd64.msi`

#### Step 2: Platform-Specific Installation

## macOS Installation

### Option A: Using the Official Installer (Recommended)

1. **Download the Installer**
   - Download the `.pkg` file from [go.dev/dl](https://go.dev/dl/)
   - Choose the appropriate version for your Mac:
     - Intel Macs: `darwin-amd64.pkg`
     - Apple Silicon Macs (M1/M2): `darwin-arm64.pkg`

2. **Install Go**
   - Double-click the downloaded `.pkg` file
   - Follow the installation wizard prompts
   - The installer will place Go in `/usr/local/go`
   - The installer automatically adds `/usr/local/go/bin` to your PATH

3. **Verify Installation**
   ```bash
   # Open Terminal and run:
   go version
   ```

   You should see output like:
   ```
   go version go1.21.x darwin/amd64
   ```

### Option B: Using Homebrew

If you prefer using Homebrew package manager:

```bash
# Install Homebrew if you haven't already
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install Go
brew install go

# Verify installation
go version
```

### Option C: Manual Installation

1. **Download and Extract**
   ```bash
   # Download (replace with latest version)
   curl -LO https://go.dev/dl/go1.21.x.darwin-amd64.tar.gz

   # Remove any previous installation
   sudo rm -rf /usr/local/go

   # Extract to /usr/local
   sudo tar -C /usr/local -xzf go1.21.x.darwin-amd64.tar.gz
   ```

2. **Set up PATH**
   ```bash
   # Add to your shell profile (~/.zshrc for zsh or ~/.bash_profile for bash)
   echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.zshrc

   # Reload your shell configuration
   source ~/.zshrc
   ```

## Linux Installation

### Option A: Manual Installation (Recommended)

1. **Remove Previous Installation**
   ```bash
   # Remove any existing Go installation
   sudo rm -rf /usr/local/go
   ```

2. **Download and Extract**
   ```bash
   # Download the latest version (replace with current version)
   wget https://go.dev/dl/go1.21.x.linux-amd64.tar.gz

   # Extract to /usr/local
   sudo tar -C /usr/local -xzf go1.21.x.linux-amd64.tar.gz
   ```

3. **Set up PATH**
   ```bash
   # Add Go to PATH in your profile
   echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc

   # For system-wide installation, add to /etc/profile instead:
   # echo 'export PATH=$PATH:/usr/local/go/bin' | sudo tee -a /etc/profile

   # Reload your shell configuration
   source ~/.bashrc
   ```

4. **Verify Installation**
   ```bash
   go version
   ```

### Option B: Using Package Managers

#### Ubuntu/Debian (APT)
```bash
# Update package list
sudo apt update

# Install Go
sudo apt install golang-go

# Verify installation
go version
```

**Note**: Package manager versions may be older than the latest release.

#### CentOS/RHEL/Fedora (YUM/DNF)
```bash
# For CentOS/RHEL
sudo yum install golang

# For Fedora
sudo dnf install golang

# Verify installation
go version
```

#### Arch Linux (Pacman)
```bash
# Install Go
sudo pacman -S go

# Verify installation
go version
```

## Windows Installation

### Option A: Using the Official Installer (Recommended)

1. **Download the Installer**
   - Download the `.msi` file from [go.dev/dl](https://go.dev/dl/)
   - Choose `go1.21.x.windows-amd64.msi`

2. **Install Go**
   - Double-click the downloaded `.msi` file
   - Follow the installation wizard
   - Go will be installed to `C:\Program Files\Go`
   - The installer automatically adds `C:\Program Files\Go\bin` to your PATH

3. **Verify Installation**
   - Open Command Prompt or PowerShell
   - Run: `go version`

### Option B: Using Chocolatey

If you have Chocolatey package manager installed:

```powershell
# Install Go
choco install golang

# Verify installation
go version
```

### Option C: Using Scoop

If you prefer Scoop package manager:

```powershell
# Install Go
scoop install go

# Verify installation
go version
```

## Post-Installation Setup

### Setting up GOPATH and GOROOT (Optional)

Modern Go (1.11+) uses modules and doesn't require GOPATH for most development. However, you might want to set these for specific workflows:

```bash
# Add to your shell profile
export GOROOT=/usr/local/go           # Where Go is installed
export GOPATH=$HOME/go                # Your workspace
export PATH=$PATH:$GOROOT/bin:$GOPATH/bin
```

### Verify Your Installation

Run these commands to ensure everything is working correctly:

```bash
# Check Go version
go version

# Check Go environment
go env

# Check GOROOT (where Go is installed)
go env GOROOT

# Check GOPATH (your workspace)
go env GOPATH
```

### Create Your First Go Program

1. **Create a project directory**
   ```bash
   mkdir hello-world
   cd hello-world
   ```

2. **Initialize a Go module**
   ```bash
   go mod init hello-world
   ```

3. **Create main.go**
   ```go
   package main

   import "fmt"

   func main() {
       fmt.Println("Hello, World!")
   }
   ```

4. **Run your program**
   ```bash
   go run main.go
   ```

## Troubleshooting

### Common Issues

1. **"go: command not found"**
   - Ensure Go's bin directory is in your PATH
   - Restart your terminal after installation
   - Check if Go is properly installed: `which go` (Unix) or `where go` (Windows)

2. **Permission denied errors**
   - Use `sudo` for system-wide installations on Unix systems
   - Ensure you have write permissions to the installation directory

3. **Old version showing**
   - Remove old Go installations before installing new version
   - Check if multiple Go versions are installed
   - Verify PATH order to ensure correct version is found first

### Getting Help

- **Official Documentation**: [go.dev/doc](https://go.dev/doc)
- **Go Community**: [golang.org/help](https://golang.org/help)
- **Stack Overflow**: Tag your questions with `go` or `golang`

## Next Steps

After successful installation, you're ready to:
1. Learn Go syntax and basic concepts
2. Explore the Go standard library
3. Build your first Go application
4. Set up your development environment (IDE/editor)