# Reflection and Metaprogramming in Go

This comprehensive guide covers reflection and metaprogramming in Go, including runtime type inspection, dynamic method calls, struct manipulation, and advanced patterns for building flexible and dynamic Go applications.

## Table of Contents

1. [Overview](#overview)
2. [Basic Reflection](#basic-reflection)
3. [Type Information](#type-information)
4. [Value Manipulation](#value-manipulation)
5. [Struct Reflection](#struct-reflection)
6. [Method Reflection](#method-reflection)
7. [Interface Reflection](#interface-reflection)
8. [Dynamic Function Calls](#dynamic-function-calls)
9. [Creating Types Dynamically](#creating-types-dynamically)
10. [Reflection Patterns](#reflection-patterns)
11. [Performance Considerations](#performance-considerations)
12. [Best Practices](#best-practices)
13. [Common Use Cases](#common-use-cases)
14. [Advanced Techniques](#advanced-techniques)

## Overview

Reflection in Go allows programs to examine their own structure and behavior at runtime. It's implemented through the `reflect` package.

### Key Concepts

```go
import "reflect"

// Two main types in reflection:
// reflect.Type - represents a Go type
// reflect.Value - represents a Go value

// Getting type and value information
var x int = 42
t := reflect.TypeOf(x)  // reflect.Type
v := reflect.ValueOf(x) // reflect.Value

fmt.Printf("Type: %v\n", t)     // Type: int
fmt.Printf("Value: %v\n", v)    // Value: 42
fmt.Printf("Kind: %v\n", t.Kind()) // Kind: int
```

### When to Use Reflection

| Use Case | Example | Alternative |
|----------|---------|-------------|
| **Serialization** | JSON marshaling | Code generation |
| **Generic algorithms** | Deep copy, comparison | Generics (Go 1.18+) |
| **Framework development** | ORM, dependency injection | Interfaces |
| **Configuration** | Struct tag parsing | Manual parsing |
| **Testing utilities** | Deep equality | Custom comparers |

### Reflection Laws

1. **Reflection goes from interface value to reflection object**
2. **Reflection goes from reflection object to interface value**
3. **To modify a reflection object, the value must be settable**

## Basic Reflection

Understanding the fundamentals of Go's reflection system.

### Type and Value Basics

```go
package main

import (
    "fmt"
    "reflect"
)

func basicReflection() {
    // Basic types
    var i int = 42
    var s string = "hello"
    var f float64 = 3.14

    examineValue(i)
    examineValue(s)
    examineValue(f)

    // Complex types
    slice := []int{1, 2, 3}
    m := map[string]int{"a": 1, "b": 2}

    examineValue(slice)
    examineValue(m)
}

func examineValue(x interface{}) {
    v := reflect.ValueOf(x)
    t := reflect.TypeOf(x)

    fmt.Printf("Value: %v\n", v)
    fmt.Printf("Type: %v\n", t)
    fmt.Printf("Kind: %v\n", v.Kind())
    fmt.Printf("Can Set: %v\n", v.CanSet())
    fmt.Println("---")
}

// Output:
// Value: 42
// Type: int
// Kind: int
// Can Set: false
// ---
// Value: hello
// Type: string
// Kind: string
// Can Set: false
// ---
```

### Reflection Laws in Practice

```go
func reflectionLaws() {
    // Law 1: Interface value to reflection object
    var x float64 = 3.4
    v := reflect.ValueOf(x)
    fmt.Printf("Type: %s\n", v.Type())
    fmt.Printf("Kind: %s\n", v.Kind())
    fmt.Printf("Value: %v\n", v.Float())

    // Law 2: Reflection object to interface value
    y := v.Interface().(float64)
    fmt.Printf("Recovered value: %f\n", y)

    // Law 3: Settability
    fmt.Printf("Settability of v: %v\n", v.CanSet())

    // To modify, we need a pointer
    p := reflect.ValueOf(&x)
    fmt.Printf("Type of p: %s\n", p.Type())
    fmt.Printf("Settability of p: %v\n", p.CanSet())

    // Get the element that p points to
    elem := p.Elem()
    fmt.Printf("Settability of elem: %v\n", elem.CanSet())

    // Now we can set the value
    elem.SetFloat(7.1)
    fmt.Printf("Modified x: %f\n", x)
}
```

### Kind vs Type

```go
func kindVsType() {
    type MyInt int
    type MyString string

    var i MyInt = 42
    var s MyString = "hello"

    // Type is the exact type
    fmt.Printf("Type of i: %v\n", reflect.TypeOf(i))     // main.MyInt
    fmt.Printf("Type of s: %v\n", reflect.TypeOf(s))     // main.MyString

    // Kind is the underlying kind
    fmt.Printf("Kind of i: %v\n", reflect.ValueOf(i).Kind()) // int
    fmt.Printf("Kind of s: %v\n", reflect.ValueOf(s).Kind()) // string

    // Useful for generic operations
    v := reflect.ValueOf(i)
    switch v.Kind() {
    case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
        fmt.Printf("Integer value: %d\n", v.Int())
    case reflect.String:
        fmt.Printf("String value: %s\n", v.String())
    }
}
```

## Type Information

Extracting detailed type information using reflection.

### Basic Type Information

```go
func typeInformation() {
    var x struct {
        Name string `json:"name" validate:"required"`
        Age  int    `json:"age" validate:"min=0,max=120"`
    }

    t := reflect.TypeOf(x)

    fmt.Printf("Type name: %s\n", t.Name())
    fmt.Printf("Type string: %s\n", t.String())
    fmt.Printf("Package path: %s\n", t.PkgPath())
    fmt.Printf("Kind: %s\n", t.Kind())
    fmt.Printf("Size: %d bytes\n", t.Size())
    fmt.Printf("Alignment: %d\n", t.Align())

    // For structs, get field information
    if t.Kind() == reflect.Struct {
        fmt.Printf("Number of fields: %d\n", t.NumField())

        for i := 0; i < t.NumField(); i++ {
            field := t.Field(i)
            fmt.Printf("Field %d: %s (type: %s)\n", i, field.Name, field.Type)

            // Get struct tags
            jsonTag := field.Tag.Get("json")
            validateTag := field.Tag.Get("validate")
            fmt.Printf("  JSON tag: %s\n", jsonTag)
            fmt.Printf("  Validate tag: %s\n", validateTag)
        }
    }
}
```

### Complex Type Analysis

```go
func analyzeComplexTypes() {
    // Slice type
    slice := []map[string]interface{}{
        {"name": "John", "age": 30},
        {"name": "Jane", "age": 25},
    }

    analyzeType(reflect.TypeOf(slice))

    // Channel type
    ch := make(chan int, 10)
    analyzeType(reflect.TypeOf(ch))

    // Function type
    fn := func(int, string) (bool, error) { return true, nil }
    analyzeType(reflect.TypeOf(fn))
}

func analyzeType(t reflect.Type) {
    fmt.Printf("\n=== Analyzing %s ===\n", t)
    fmt.Printf("Kind: %s\n", t.Kind())

    switch t.Kind() {
    case reflect.Slice, reflect.Array:
        fmt.Printf("Element type: %s\n", t.Elem())
        if t.Kind() == reflect.Array {
            fmt.Printf("Length: %d\n", t.Len())
        }

    case reflect.Map:
        fmt.Printf("Key type: %s\n", t.Key())
        fmt.Printf("Value type: %s\n", t.Elem())

    case reflect.Chan:
        fmt.Printf("Element type: %s\n", t.Elem())
        fmt.Printf("Direction: %s\n", t.ChanDir())

    case reflect.Func:
        fmt.Printf("Number of inputs: %d\n", t.NumIn())
        for i := 0; i < t.NumIn(); i++ {
            fmt.Printf("  Input %d: %s\n", i, t.In(i))
        }

        fmt.Printf("Number of outputs: %d\n", t.NumOut())
        for i := 0; i < t.NumOut(); i++ {
            fmt.Printf("  Output %d: %s\n", i, t.Out(i))
        }

        fmt.Printf("Is variadic: %v\n", t.IsVariadic())

    case reflect.Ptr:
        fmt.Printf("Points to: %s\n", t.Elem())

    case reflect.Interface:
        fmt.Printf("Number of methods: %d\n", t.NumMethod())
        for i := 0; i < t.NumMethod(); i++ {
            method := t.Method(i)
            fmt.Printf("  Method %d: %s\n", i, method.Name)
        }
    }
}
```

### Type Comparison and Conversion

```go
func typeComparison() {
    var i int = 42
    var j int32 = 42
    var k MyInt = 42

    iType := reflect.TypeOf(i)
    jType := reflect.TypeOf(j)
    kType := reflect.TypeOf(k)

    // Type equality
    fmt.Printf("int == int32: %v\n", iType == jType)         // false
    fmt.Printf("int == MyInt: %v\n", iType == kType)         // false

    // Kind equality
    fmt.Printf("int kind == int32 kind: %v\n", iType.Kind() == jType.Kind()) // false
    fmt.Printf("int kind == MyInt kind: %v\n", iType.Kind() == kType.Kind()) // true

    // Assignability
    fmt.Printf("int assignable to int32: %v\n", iType.AssignableTo(jType)) // false
    fmt.Printf("int assignable to MyInt: %v\n", iType.AssignableTo(kType)) // false

    // Convertibility
    fmt.Printf("int convertible to int32: %v\n", iType.ConvertibleTo(jType)) // true
    fmt.Printf("int convertible to MyInt: %v\n", iType.ConvertibleTo(kType)) // true

    // Implementing interfaces
    var w io.Writer
    writerType := reflect.TypeOf((*io.Writer)(nil)).Elem()
    bufferType := reflect.TypeOf(&bytes.Buffer{})

    fmt.Printf("*bytes.Buffer implements io.Writer: %v\n",
        bufferType.Implements(writerType)) // true
}

type MyInt int
```

## Value Manipulation

Working with reflect.Value to read and modify values at runtime.

### Basic Value Operations

```go
func valueOperations() {
    // Reading values
    var i int = 42
    var s string = "hello"
    var f float64 = 3.14
    var b bool = true

    readValue(i)
    readValue(s)
    readValue(f)
    readValue(b)

    // Modifying values
    modifyValue(&i)
    modifyValue(&s)
    modifyValue(&f)
    modifyValue(&b)

    fmt.Printf("Modified values: %d, %s, %f, %v\n", i, s, f, b)
}

func readValue(x interface{}) {
    v := reflect.ValueOf(x)

    switch v.Kind() {
    case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
        fmt.Printf("Integer: %d\n", v.Int())
    case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
        fmt.Printf("Unsigned: %d\n", v.Uint())
    case reflect.Float32, reflect.Float64:
        fmt.Printf("Float: %f\n", v.Float())
    case reflect.String:
        fmt.Printf("String: %s\n", v.String())
    case reflect.Bool:
        fmt.Printf("Bool: %v\n", v.Bool())
    default:
        fmt.Printf("Other: %v\n", v.Interface())
    }
}

func modifyValue(x interface{}) {
    v := reflect.ValueOf(x)

    // Must be a pointer to be settable
    if v.Kind() != reflect.Ptr {
        fmt.Printf("Cannot modify non-pointer value\n")
        return
    }

    // Get the element the pointer points to
    elem := v.Elem()

    if !elem.CanSet() {
        fmt.Printf("Cannot set value\n")
        return
    }

    switch elem.Kind() {
    case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
        elem.SetInt(elem.Int() * 2)
    case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
        elem.SetUint(elem.Uint() * 2)
    case reflect.Float32, reflect.Float64:
        elem.SetFloat(elem.Float() * 2)
    case reflect.String:
        elem.SetString(elem.String() + " modified")
    case reflect.Bool:
        elem.SetBool(!elem.Bool())
    }
}
```

### Collection Manipulation

```go
func collectionManipulation() {
    // Slice manipulation
    slice := []int{1, 2, 3, 4, 5}
    manipulateSlice(&slice)
    fmt.Printf("Modified slice: %v\n", slice)

    // Map manipulation
    m := map[string]int{"a": 1, "b": 2, "c": 3}
    manipulateMap(m)
    fmt.Printf("Modified map: %v\n", m)

    // Array manipulation
    arr := [5]int{1, 2, 3, 4, 5}
    manipulateArray(&arr)
    fmt.Printf("Modified array: %v\n", arr)
}

func manipulateSlice(slice interface{}) {
    v := reflect.ValueOf(slice).Elem() // Get the slice value

    if v.Kind() != reflect.Slice {
        fmt.Printf("Not a slice\n")
        return
    }

    fmt.Printf("Slice length: %d, capacity: %d\n", v.Len(), v.Cap())

    // Modify existing elements
    for i := 0; i < v.Len(); i++ {
        elem := v.Index(i)
        if elem.CanSet() && elem.Kind() == reflect.Int {
            elem.SetInt(elem.Int() * 10)
        }
    }

    // Append new element
    newElem := reflect.ValueOf(100)
    newSlice := reflect.Append(v, newElem)
    v.Set(newSlice)
}

func manipulateMap(m interface{}) {
    v := reflect.ValueOf(m)

    if v.Kind() != reflect.Map {
        fmt.Printf("Not a map\n")
        return
    }

    // Iterate over map
    for _, key := range v.MapKeys() {
        value := v.MapIndex(key)
        fmt.Printf("Key: %v, Value: %v\n", key.Interface(), value.Interface())

        // Modify map values
        if value.Kind() == reflect.Int {
            newValue := reflect.ValueOf(value.Int() * 100)
            v.SetMapIndex(key, newValue)
        }
    }

    // Add new key-value pair
    newKey := reflect.ValueOf("d")
    newValue := reflect.ValueOf(400)
    v.SetMapIndex(newKey, newValue)
}

func manipulateArray(arr interface{}) {
    v := reflect.ValueOf(arr).Elem()

    if v.Kind() != reflect.Array {
        fmt.Printf("Not an array\n")
        return
    }

    // Modify array elements
    for i := 0; i < v.Len(); i++ {
        elem := v.Index(i)
        if elem.CanSet() && elem.Kind() == reflect.Int {
            elem.SetInt(elem.Int() + 1000)
        }
    }
}
```

## Struct Reflection

Deep dive into struct manipulation using reflection.

### Struct Field Access

```go
type Person struct {
    Name    string `json:"name" validate:"required"`
    Age     int    `json:"age" validate:"min=0,max=120"`
    Email   string `json:"email" validate:"email"`
    private string // unexported field
}

func structReflection() {
    p := Person{
        Name:    "John Doe",
        Age:     30,
        Email:   "<EMAIL>",
        private: "secret",
    }

    examineStruct(p)
    modifyStruct(&p)
    fmt.Printf("Modified person: %+v\n", p)
}

func examineStruct(s interface{}) {
    v := reflect.ValueOf(s)
    t := reflect.TypeOf(s)

    if v.Kind() != reflect.Struct {
        fmt.Printf("Not a struct\n")
        return
    }

    fmt.Printf("Struct: %s\n", t.Name())
    fmt.Printf("Number of fields: %d\n", v.NumField())

    for i := 0; i < v.NumField(); i++ {
        field := v.Field(i)
        fieldType := t.Field(i)

        fmt.Printf("Field %d:\n", i)
        fmt.Printf("  Name: %s\n", fieldType.Name)
        fmt.Printf("  Type: %s\n", fieldType.Type)
        fmt.Printf("  Value: %v\n", field.Interface())
        fmt.Printf("  Tag: %s\n", fieldType.Tag)
        fmt.Printf("  Exported: %v\n", fieldType.IsExported())
        fmt.Printf("  Can Set: %v\n", field.CanSet())

        // Parse specific tags
        if jsonTag := fieldType.Tag.Get("json"); jsonTag != "" {
            fmt.Printf("  JSON tag: %s\n", jsonTag)
        }
        if validateTag := fieldType.Tag.Get("validate"); validateTag != "" {
            fmt.Printf("  Validate tag: %s\n", validateTag)
        }

        fmt.Println()
    }
}

func modifyStruct(s interface{}) {
    v := reflect.ValueOf(s)

    // Must be a pointer to struct
    if v.Kind() != reflect.Ptr || v.Elem().Kind() != reflect.Struct {
        fmt.Printf("Must be a pointer to struct\n")
        return
    }

    structValue := v.Elem()
    structType := structValue.Type()

    for i := 0; i < structValue.NumField(); i++ {
        field := structValue.Field(i)
        fieldType := structType.Field(i)

        // Skip unexported fields
        if !field.CanSet() {
            continue
        }

        switch field.Kind() {
        case reflect.String:
            if fieldType.Name == "Name" {
                field.SetString("Modified " + field.String())
            } else if fieldType.Name == "Email" {
                field.SetString("modified." + field.String())
            }
        case reflect.Int:
            if fieldType.Name == "Age" {
                field.SetInt(field.Int() + 1)
            }
        }
    }
}
```

### Dynamic Struct Creation

```go
func createStructDynamically() {
    // Define struct fields
    fields := []reflect.StructField{
        {
            Name: "ID",
            Type: reflect.TypeOf(int(0)),
            Tag:  `json:"id"`,
        },
        {
            Name: "Name",
            Type: reflect.TypeOf(""),
            Tag:  `json:"name" validate:"required"`,
        },
        {
            Name: "Active",
            Type: reflect.TypeOf(true),
            Tag:  `json:"active"`,
        },
    }

    // Create struct type
    structType := reflect.StructOf(fields)

    // Create instance
    structValue := reflect.New(structType).Elem()

    // Set field values
    structValue.FieldByName("ID").SetInt(123)
    structValue.FieldByName("Name").SetString("Dynamic Struct")
    structValue.FieldByName("Active").SetBool(true)

    // Get the interface value
    instance := structValue.Interface()
    fmt.Printf("Dynamic struct: %+v\n", instance)
    fmt.Printf("Type: %T\n", instance)

    // Access fields by name
    nameField := structValue.FieldByName("Name")
    fmt.Printf("Name field value: %s\n", nameField.String())
}
```

### Struct Tag Processing

```go
func processStructTags() {
    type User struct {
        ID       int    `db:"id" json:"id" validate:"required"`
        Username string `db:"username" json:"username" validate:"required,min=3,max=20"`
        Email    string `db:"email" json:"email" validate:"required,email"`
        Password string `db:"password" json:"-" validate:"required,min=8"`
        IsActive bool   `db:"is_active" json:"is_active" default:"true"`
    }

    t := reflect.TypeOf(User{})

    fmt.Printf("Processing tags for %s:\n", t.Name())

    for i := 0; i < t.NumField(); i++ {
        field := t.Field(i)

        fmt.Printf("\nField: %s\n", field.Name)

        // Parse different tag types
        if dbTag := field.Tag.Get("db"); dbTag != "" {
            fmt.Printf("  Database column: %s\n", dbTag)
        }

        if jsonTag := field.Tag.Get("json"); jsonTag != "" {
            fmt.Printf("  JSON field: %s\n", jsonTag)
        }

        if validateTag := field.Tag.Get("validate"); validateTag != "" {
            fmt.Printf("  Validation rules: %s\n", validateTag)
            parseValidationRules(validateTag)
        }

        if defaultTag := field.Tag.Get("default"); defaultTag != "" {
            fmt.Printf("  Default value: %s\n", defaultTag)
        }
    }
}

func parseValidationRules(rules string) {
    // Simple validation rule parser
    parts := strings.Split(rules, ",")
    for _, part := range parts {
        part = strings.TrimSpace(part)
        if strings.Contains(part, "=") {
            kv := strings.SplitN(part, "=", 2)
            fmt.Printf("    Rule: %s, Value: %s\n", kv[0], kv[1])
        } else {
            fmt.Printf("    Rule: %s\n", part)
        }
    }
}
```

## Method Reflection

Working with methods through reflection for dynamic method calls.

### Method Discovery and Invocation

```go
type Calculator struct {
    value float64
}

func (c *Calculator) Add(x float64) float64 {
    c.value += x
    return c.value
}

func (c *Calculator) Multiply(x float64) float64 {
    c.value *= x
    return c.value
}

func (c *Calculator) GetValue() float64 {
    return c.value
}

func (c *Calculator) Reset() {
    c.value = 0
}

func (c *Calculator) SetValue(x float64) {
    c.value = x
}

func methodReflection() {
    calc := &Calculator{value: 10}

    // Examine methods
    examineMethods(calc)

    // Call methods dynamically
    callMethodDynamically(calc, "Add", 5.0)
    callMethodDynamically(calc, "Multiply", 2.0)
    callMethodDynamically(calc, "GetValue")
    callMethodDynamically(calc, "Reset")
    callMethodDynamically(calc, "GetValue")
}

func examineMethods(obj interface{}) {
    v := reflect.ValueOf(obj)
    t := reflect.TypeOf(obj)

    fmt.Printf("Object type: %s\n", t)
    fmt.Printf("Number of methods: %d\n", v.NumMethod())

    for i := 0; i < v.NumMethod(); i++ {
        method := v.Method(i)
        methodType := t.Method(i)

        fmt.Printf("Method %d:\n", i)
        fmt.Printf("  Name: %s\n", methodType.Name)
        fmt.Printf("  Type: %s\n", method.Type())
        fmt.Printf("  Number of inputs: %d\n", method.Type().NumIn())
        fmt.Printf("  Number of outputs: %d\n", method.Type().NumOut())

        // Print input types
        for j := 0; j < method.Type().NumIn(); j++ {
            fmt.Printf("    Input %d: %s\n", j, method.Type().In(j))
        }

        // Print output types
        for j := 0; j < method.Type().NumOut(); j++ {
            fmt.Printf("    Output %d: %s\n", j, method.Type().Out(j))
        }

        fmt.Println()
    }
}

func callMethodDynamically(obj interface{}, methodName string, args ...interface{}) {
    v := reflect.ValueOf(obj)
    method := v.MethodByName(methodName)

    if !method.IsValid() {
        fmt.Printf("Method %s not found\n", methodName)
        return
    }

    // Prepare arguments
    var reflectArgs []reflect.Value
    for _, arg := range args {
        reflectArgs = append(reflectArgs, reflect.ValueOf(arg))
    }

    // Call method
    results := method.Call(reflectArgs)

    fmt.Printf("Called %s(", methodName)
    for i, arg := range args {
        if i > 0 {
            fmt.Printf(", ")
        }
        fmt.Printf("%v", arg)
    }
    fmt.Printf(")")

    if len(results) > 0 {
        fmt.Printf(" -> ")
        for i, result := range results {
            if i > 0 {
                fmt.Printf(", ")
            }
            fmt.Printf("%v", result.Interface())
        }
    }
    fmt.Println()
}
```

### Interface Method Reflection

```go
type Shape interface {
    Area() float64
    Perimeter() float64
}

type Rectangle struct {
    Width, Height float64
}

func (r Rectangle) Area() float64 {
    return r.Width * r.Height
}

func (r Rectangle) Perimeter() float64 {
    return 2 * (r.Width + r.Height)
}

type Circle struct {
    Radius float64
}

func (c Circle) Area() float64 {
    return math.Pi * c.Radius * c.Radius
}

func (c Circle) Perimeter() float64 {
    return 2 * math.Pi * c.Radius
}

func interfaceMethodReflection() {
    shapes := []Shape{
        Rectangle{Width: 5, Height: 3},
        Circle{Radius: 2},
    }

    for _, shape := range shapes {
        processShape(shape)
    }
}

func processShape(shape Shape) {
    v := reflect.ValueOf(shape)
    t := reflect.TypeOf(shape)

    fmt.Printf("Shape: %T\n", shape)

    // Call interface methods through reflection
    areaMethod := v.MethodByName("Area")
    perimeterMethod := v.MethodByName("Perimeter")

    if areaMethod.IsValid() {
        area := areaMethod.Call(nil)[0].Float()
        fmt.Printf("  Area: %.2f\n", area)
    }

    if perimeterMethod.IsValid() {
        perimeter := perimeterMethod.Call(nil)[0].Float()
        fmt.Printf("  Perimeter: %.2f\n", perimeter)
    }

    // Check if type implements interface
    shapeInterface := reflect.TypeOf((*Shape)(nil)).Elem()
    fmt.Printf("  Implements Shape: %v\n", t.Implements(shapeInterface))

    fmt.Println()
}
```

## Dynamic Function Calls

Creating and calling functions dynamically using reflection.

### Function Value Manipulation

```go
func dynamicFunctionCalls() {
    // Function variables
    add := func(a, b int) int { return a + b }
    multiply := func(a, b int) int { return a * b }
    greet := func(name string) string { return "Hello, " + name }

    // Call functions through reflection
    callFunction(add, 5, 3)
    callFunction(multiply, 4, 7)
    callFunction(greet, "World")

    // Create function dynamically
    dynamicFunc := createAdder(10)
    callFunction(dynamicFunc, 5)
}

func callFunction(fn interface{}, args ...interface{}) {
    v := reflect.ValueOf(fn)
    t := reflect.TypeOf(fn)

    if v.Kind() != reflect.Func {
        fmt.Printf("Not a function: %T\n", fn)
        return
    }

    fmt.Printf("Calling function: %s\n", t)

    // Check argument count
    if len(args) != t.NumIn() {
        fmt.Printf("Expected %d arguments, got %d\n", t.NumIn(), len(args))
        return
    }

    // Prepare arguments
    var reflectArgs []reflect.Value
    for i, arg := range args {
        argValue := reflect.ValueOf(arg)
        expectedType := t.In(i)

        // Type checking
        if !argValue.Type().AssignableTo(expectedType) {
            fmt.Printf("Argument %d: cannot assign %s to %s\n",
                i, argValue.Type(), expectedType)
            return
        }

        reflectArgs = append(reflectArgs, argValue)
    }

    // Call function
    results := v.Call(reflectArgs)

    // Print results
    fmt.Printf("Arguments: %v\n", args)
    if len(results) > 0 {
        fmt.Printf("Results: ")
        for i, result := range results {
            if i > 0 {
                fmt.Printf(", ")
            }
            fmt.Printf("%v", result.Interface())
        }
        fmt.Println()
    }
    fmt.Println()
}

func createAdder(base int) interface{} {
    return func(x int) int {
        return base + x
    }
}
```

### Function Type Creation

```go
func createFunctionType() {
    // Create function type: func(int, string) (bool, error)
    inputTypes := []reflect.Type{
        reflect.TypeOf(int(0)),
        reflect.TypeOf(""),
    }
    outputTypes := []reflect.Type{
        reflect.TypeOf(true),
        reflect.TypeOf((*error)(nil)).Elem(),
    }

    funcType := reflect.FuncOf(inputTypes, outputTypes, false)
    fmt.Printf("Created function type: %s\n", funcType)

    // Create a function value of this type
    funcValue := reflect.MakeFunc(funcType, func(args []reflect.Value) []reflect.Value {
        num := args[0].Int()
        str := args[1].String()

        // Simple logic: return true if number is positive and string is not empty
        success := num > 0 && str != ""
        var err error
        if !success {
            err = fmt.Errorf("validation failed: num=%d, str=%s", num, str)
        }

        return []reflect.Value{
            reflect.ValueOf(success),
            reflect.ValueOf(err),
        }
    })

    // Call the dynamically created function
    results := funcValue.Call([]reflect.Value{
        reflect.ValueOf(42),
        reflect.ValueOf("hello"),
    })

    fmt.Printf("Result: success=%v, error=%v\n",
        results[0].Interface(), results[1].Interface())

    // Call with invalid arguments
    results = funcValue.Call([]reflect.Value{
        reflect.ValueOf(-1),
        reflect.ValueOf(""),
    })

    fmt.Printf("Result: success=%v, error=%v\n",
        results[0].Interface(), results[1].Interface())
}
```

## Reflection Patterns

Common patterns and utilities using reflection.

### Deep Copy Implementation

```go
func deepCopy(src interface{}) interface{} {
    srcValue := reflect.ValueOf(src)
    return deepCopyValue(srcValue).Interface()
}

func deepCopyValue(src reflect.Value) reflect.Value {
    switch src.Kind() {
    case reflect.Ptr:
        if src.IsNil() {
            return reflect.Zero(src.Type())
        }
        dst := reflect.New(src.Type().Elem())
        dst.Elem().Set(deepCopyValue(src.Elem()))
        return dst

    case reflect.Slice:
        if src.IsNil() {
            return reflect.Zero(src.Type())
        }
        dst := reflect.MakeSlice(src.Type(), src.Len(), src.Cap())
        for i := 0; i < src.Len(); i++ {
            dst.Index(i).Set(deepCopyValue(src.Index(i)))
        }
        return dst

    case reflect.Map:
        if src.IsNil() {
            return reflect.Zero(src.Type())
        }
        dst := reflect.MakeMap(src.Type())
        for _, key := range src.MapKeys() {
            dst.SetMapIndex(key, deepCopyValue(src.MapIndex(key)))
        }
        return dst

    case reflect.Struct:
        dst := reflect.New(src.Type()).Elem()
        for i := 0; i < src.NumField(); i++ {
            if dst.Field(i).CanSet() {
                dst.Field(i).Set(deepCopyValue(src.Field(i)))
            }
        }
        return dst

    case reflect.Array:
        dst := reflect.New(src.Type()).Elem()
        for i := 0; i < src.Len(); i++ {
            dst.Index(i).Set(deepCopyValue(src.Index(i)))
        }
        return dst

    default:
        return src
    }
}

func demonstrateDeepCopy() {
    type Person struct {
        Name    string
        Age     int
        Friends []string
        Scores  map[string]int
    }

    original := Person{
        Name:    "John",
        Age:     30,
        Friends: []string{"Alice", "Bob"},
        Scores:  map[string]int{"math": 95, "science": 87},
    }

    // Deep copy
    copied := deepCopy(original).(Person)

    // Modify original
    original.Name = "Modified John"
    original.Friends[0] = "Modified Alice"
    original.Scores["math"] = 100

    fmt.Printf("Original: %+v\n", original)
    fmt.Printf("Copied: %+v\n", copied)
}
```

### Generic Equality Checker

```go
func deepEqual(a, b interface{}) bool {
    return deepEqualValue(reflect.ValueOf(a), reflect.ValueOf(b))
}

func deepEqualValue(a, b reflect.Value) bool {
    if !a.IsValid() || !b.IsValid() {
        return a.IsValid() == b.IsValid()
    }

    if a.Type() != b.Type() {
        return false
    }

    switch a.Kind() {
    case reflect.Slice:
        if a.IsNil() != b.IsNil() {
            return false
        }
        if a.Len() != b.Len() {
            return false
        }
        for i := 0; i < a.Len(); i++ {
            if !deepEqualValue(a.Index(i), b.Index(i)) {
                return false
            }
        }
        return true

    case reflect.Map:
        if a.IsNil() != b.IsNil() {
            return false
        }
        if a.Len() != b.Len() {
            return false
        }
        for _, key := range a.MapKeys() {
            aVal := a.MapIndex(key)
            bVal := b.MapIndex(key)
            if !bVal.IsValid() || !deepEqualValue(aVal, bVal) {
                return false
            }
        }
        return true

    case reflect.Struct:
        for i := 0; i < a.NumField(); i++ {
            if !deepEqualValue(a.Field(i), b.Field(i)) {
                return false
            }
        }
        return true

    case reflect.Ptr:
        if a.IsNil() || b.IsNil() {
            return a.IsNil() == b.IsNil()
        }
        return deepEqualValue(a.Elem(), b.Elem())

    case reflect.Array:
        for i := 0; i < a.Len(); i++ {
            if !deepEqualValue(a.Index(i), b.Index(i)) {
                return false
            }
        }
        return true

    default:
        return a.Interface() == b.Interface()
    }
}

func demonstrateDeepEqual() {
    type Data struct {
        Numbers []int
        Mapping map[string]int
    }

    a := Data{
        Numbers: []int{1, 2, 3},
        Mapping: map[string]int{"a": 1, "b": 2},
    }

    b := Data{
        Numbers: []int{1, 2, 3},
        Mapping: map[string]int{"a": 1, "b": 2},
    }

    c := Data{
        Numbers: []int{1, 2, 4}, // Different
        Mapping: map[string]int{"a": 1, "b": 2},
    }

    fmt.Printf("a == b: %v\n", deepEqual(a, b)) // true
    fmt.Printf("a == c: %v\n", deepEqual(a, c)) // false
}
```

## Performance Considerations

Understanding the performance implications of reflection.

### Reflection Performance

```go
import (
    "reflect"
    "testing"
    "time"
)

// Benchmark reflection vs direct access
func BenchmarkDirectAccess(b *testing.B) {
    type Person struct {
        Name string
        Age  int
    }

    p := Person{Name: "John", Age: 30}

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        _ = p.Name
        _ = p.Age
    }
}

func BenchmarkReflectionAccess(b *testing.B) {
    type Person struct {
        Name string
        Age  int
    }

    p := Person{Name: "John", Age: 30}
    v := reflect.ValueOf(p)

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        _ = v.FieldByName("Name").String()
        _ = v.FieldByName("Age").Int()
    }
}

func BenchmarkReflectionAccessCached(b *testing.B) {
    type Person struct {
        Name string
        Age  int
    }

    p := Person{Name: "John", Age: 30}
    v := reflect.ValueOf(p)
    nameField := v.FieldByName("Name")
    ageField := v.FieldByName("Age")

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        _ = nameField.String()
        _ = ageField.Int()
    }
}

// Performance optimization techniques
func optimizedReflection() {
    type Person struct {
        Name string `json:"name"`
        Age  int    `json:"age"`
    }

    // Cache type information
    personType := reflect.TypeOf(Person{})
    fieldCache := make(map[string]int)

    for i := 0; i < personType.NumField(); i++ {
        field := personType.Field(i)
        fieldCache[field.Name] = i
    }

    // Use cached information
    p := Person{Name: "John", Age: 30}
    v := reflect.ValueOf(p)

    // Fast field access using index
    if idx, ok := fieldCache["Name"]; ok {
        nameField := v.Field(idx)
        fmt.Printf("Name: %s\n", nameField.String())
    }

    if idx, ok := fieldCache["Age"]; ok {
        ageField := v.Field(idx)
        fmt.Printf("Age: %d\n", ageField.Int())
    }
}
```

### Reflection Caching

```go
// Type cache for better performance
type TypeCache struct {
    mu     sync.RWMutex
    types  map[reflect.Type]*TypeInfo
}

type TypeInfo struct {
    Type       reflect.Type
    Fields     map[string]*FieldInfo
    Methods    map[string]*MethodInfo
    FieldOrder []string
}

type FieldInfo struct {
    Index int
    Type  reflect.Type
    Tag   reflect.StructTag
}

type MethodInfo struct {
    Index  int
    Type   reflect.Type
    Method reflect.Method
}

var globalTypeCache = &TypeCache{
    types: make(map[reflect.Type]*TypeInfo),
}

func (tc *TypeCache) GetTypeInfo(t reflect.Type) *TypeInfo {
    tc.mu.RLock()
    info, exists := tc.types[t]
    tc.mu.RUnlock()

    if exists {
        return info
    }

    tc.mu.Lock()
    defer tc.mu.Unlock()

    // Double-check after acquiring write lock
    if info, exists := tc.types[t]; exists {
        return info
    }

    // Build type info
    info = &TypeInfo{
        Type:    t,
        Fields:  make(map[string]*FieldInfo),
        Methods: make(map[string]*MethodInfo),
    }

    // Cache struct fields
    if t.Kind() == reflect.Struct {
        for i := 0; i < t.NumField(); i++ {
            field := t.Field(i)
            info.Fields[field.Name] = &FieldInfo{
                Index: i,
                Type:  field.Type,
                Tag:   field.Tag,
            }
            info.FieldOrder = append(info.FieldOrder, field.Name)
        }
    }

    // Cache methods
    for i := 0; i < t.NumMethod(); i++ {
        method := t.Method(i)
        info.Methods[method.Name] = &MethodInfo{
            Index:  i,
            Type:   method.Type,
            Method: method,
        }
    }

    tc.types[t] = info
    return info
}

func demonstrateTypeCache() {
    type User struct {
        ID   int    `json:"id"`
        Name string `json:"name"`
    }

    user := User{ID: 1, Name: "John"}
    userType := reflect.TypeOf(user)

    // Get cached type info
    info := globalTypeCache.GetTypeInfo(userType)

    fmt.Printf("Type: %s\n", info.Type.Name())
    fmt.Printf("Fields: %v\n", info.FieldOrder)

    // Fast field access using cached info
    v := reflect.ValueOf(user)
    if fieldInfo, ok := info.Fields["Name"]; ok {
        field := v.Field(fieldInfo.Index)
        fmt.Printf("Name: %s\n", field.String())
    }
}
```

## Best Practices

Guidelines for effective and safe use of reflection.

### When to Use Reflection

```go
// Good use cases for reflection:

// 1. Serialization/Deserialization
func marshalStruct(v interface{}) (map[string]interface{}, error) {
    result := make(map[string]interface{})
    rv := reflect.ValueOf(v)
    rt := reflect.TypeOf(v)

    if rv.Kind() == reflect.Ptr {
        rv = rv.Elem()
        rt = rt.Elem()
    }

    if rv.Kind() != reflect.Struct {
        return nil, fmt.Errorf("expected struct, got %s", rv.Kind())
    }

    for i := 0; i < rv.NumField(); i++ {
        field := rv.Field(i)
        fieldType := rt.Field(i)

        if !fieldType.IsExported() {
            continue
        }

        jsonTag := fieldType.Tag.Get("json")
        if jsonTag == "-" {
            continue
        }

        fieldName := fieldType.Name
        if jsonTag != "" {
            fieldName = strings.Split(jsonTag, ",")[0]
        }

        result[fieldName] = field.Interface()
    }

    return result, nil
}

// 2. Generic algorithms (before Go 1.18)
func isZero(v interface{}) bool {
    rv := reflect.ValueOf(v)
    return rv.IsZero()
}

// 3. Framework development
func validateStruct(v interface{}) []error {
    var errors []error
    rv := reflect.ValueOf(v)
    rt := reflect.TypeOf(v)

    if rv.Kind() == reflect.Ptr {
        rv = rv.Elem()
        rt = rt.Elem()
    }

    for i := 0; i < rv.NumField(); i++ {
        field := rv.Field(i)
        fieldType := rt.Field(i)

        validateTag := fieldType.Tag.Get("validate")
        if validateTag == "" {
            continue
        }

        if err := validateField(field, fieldType.Name, validateTag); err != nil {
            errors = append(errors, err)
        }
    }

    return errors
}

func validateField(field reflect.Value, fieldName, rules string) error {
    if strings.Contains(rules, "required") && field.IsZero() {
        return fmt.Errorf("field %s is required", fieldName)
    }
    // Add more validation rules...
    return nil
}
```

### Reflection Safety

```go
// Safe reflection practices
func safeReflection() {
    var data interface{} = "hello"

    // Always check if value is valid
    v := reflect.ValueOf(data)
    if !v.IsValid() {
        fmt.Println("Invalid value")
        return
    }

    // Check for nil pointers
    if v.Kind() == reflect.Ptr && v.IsNil() {
        fmt.Println("Nil pointer")
        return
    }

    // Check if value can be set before setting
    if v.CanSet() {
        // Safe to set
        if v.Kind() == reflect.String {
            v.SetString("modified")
        }
    }

    // Use type assertions when possible
    if str, ok := data.(string); ok {
        fmt.Printf("String value: %s\n", str)
    }
}

// Error handling in reflection
func safeFieldAccess(obj interface{}, fieldName string) (interface{}, error) {
    v := reflect.ValueOf(obj)
    if !v.IsValid() {
        return nil, fmt.Errorf("invalid value")
    }

    if v.Kind() == reflect.Ptr {
        if v.IsNil() {
            return nil, fmt.Errorf("nil pointer")
        }
        v = v.Elem()
    }

    if v.Kind() != reflect.Struct {
        return nil, fmt.Errorf("not a struct")
    }

    field := v.FieldByName(fieldName)
    if !field.IsValid() {
        return nil, fmt.Errorf("field %s not found", fieldName)
    }

    if !field.CanInterface() {
        return nil, fmt.Errorf("field %s cannot be accessed", fieldName)
    }

    return field.Interface(), nil
}
```

### Performance Best Practices

```go
// Cache reflection objects
var (
    typeCache   = make(map[reflect.Type]*TypeInfo)
    typeCacheMu sync.RWMutex
)

func getCachedTypeInfo(t reflect.Type) *TypeInfo {
    typeCacheMu.RLock()
    info, exists := typeCache[t]
    typeCacheMu.RUnlock()

    if exists {
        return info
    }

    typeCacheMu.Lock()
    defer typeCacheMu.Unlock()

    // Double-check
    if info, exists := typeCache[t]; exists {
        return info
    }

    // Build and cache
    info = buildTypeInfo(t)
    typeCache[t] = info
    return info
}

func buildTypeInfo(t reflect.Type) *TypeInfo {
    // Implementation...
    return &TypeInfo{}
}

// Avoid reflection in hot paths
func processItems(items []interface{}) {
    // Bad: reflection in loop
    for _, item := range items {
        v := reflect.ValueOf(item)
        // ... reflection operations
    }

    // Better: group by type
    typeGroups := make(map[reflect.Type][]interface{})
    for _, item := range items {
        t := reflect.TypeOf(item)
        typeGroups[t] = append(typeGroups[t], item)
    }

    // Process each type group
    for t, group := range typeGroups {
        processTypeGroup(t, group)
    }
}

func processTypeGroup(t reflect.Type, items []interface{}) {
    // Reflection setup once per type
    info := getCachedTypeInfo(t)

    // Process all items of this type
    for _, item := range items {
        processItemWithInfo(item, info)
    }
}

func processItemWithInfo(item interface{}, info *TypeInfo) {
    // Use cached type info
}
```

## Common Use Cases

Real-world applications of reflection.

### JSON-like Serialization

```go
func toMap(v interface{}) (map[string]interface{}, error) {
    result := make(map[string]interface{})

    rv := reflect.ValueOf(v)
    rt := reflect.TypeOf(v)

    // Handle pointers
    if rv.Kind() == reflect.Ptr {
        if rv.IsNil() {
            return nil, nil
        }
        rv = rv.Elem()
        rt = rt.Elem()
    }

    if rv.Kind() != reflect.Struct {
        return nil, fmt.Errorf("expected struct, got %s", rv.Kind())
    }

    for i := 0; i < rv.NumField(); i++ {
        field := rv.Field(i)
        fieldType := rt.Field(i)

        // Skip unexported fields
        if !fieldType.IsExported() {
            continue
        }

        // Get field name from tag or use field name
        name := fieldType.Name
        if tag := fieldType.Tag.Get("json"); tag != "" {
            if tag == "-" {
                continue
            }
            if idx := strings.Index(tag, ","); idx != -1 {
                name = tag[:idx]
            } else {
                name = tag
            }
        }

        // Convert field value
        value, err := convertValue(field)
        if err != nil {
            return nil, fmt.Errorf("field %s: %w", name, err)
        }

        result[name] = value
    }

    return result, nil
}

func convertValue(v reflect.Value) (interface{}, error) {
    switch v.Kind() {
    case reflect.Struct:
        return toMap(v.Interface())
    case reflect.Slice, reflect.Array:
        var result []interface{}
        for i := 0; i < v.Len(); i++ {
            item, err := convertValue(v.Index(i))
            if err != nil {
                return nil, err
            }
            result = append(result, item)
        }
        return result, nil
    case reflect.Map:
        result := make(map[string]interface{})
        for _, key := range v.MapKeys() {
            keyStr := fmt.Sprintf("%v", key.Interface())
            value, err := convertValue(v.MapIndex(key))
            if err != nil {
                return nil, err
            }
            result[keyStr] = value
        }
        return result, nil
    case reflect.Ptr:
        if v.IsNil() {
            return nil, nil
        }
        return convertValue(v.Elem())
    default:
        return v.Interface(), nil
    }
}
```

### Dependency Injection

```go
type Container struct {
    services map[reflect.Type]interface{}
    mu       sync.RWMutex
}

func NewContainer() *Container {
    return &Container{
        services: make(map[reflect.Type]interface{}),
    }
}

func (c *Container) Register(service interface{}) {
    c.mu.Lock()
    defer c.mu.Unlock()

    t := reflect.TypeOf(service)
    c.services[t] = service
}

func (c *Container) Get(serviceType interface{}) (interface{}, error) {
    c.mu.RLock()
    defer c.mu.RUnlock()

    t := reflect.TypeOf(serviceType)
    if t.Kind() == reflect.Ptr {
        t = t.Elem()
    }

    service, exists := c.services[t]
    if !exists {
        return nil, fmt.Errorf("service of type %s not found", t)
    }

    return service, nil
}

func (c *Container) Inject(target interface{}) error {
    v := reflect.ValueOf(target)
    if v.Kind() != reflect.Ptr || v.Elem().Kind() != reflect.Struct {
        return fmt.Errorf("target must be a pointer to struct")
    }

    v = v.Elem()
    t := v.Type()

    for i := 0; i < v.NumField(); i++ {
        field := v.Field(i)
        fieldType := t.Field(i)

        // Check for inject tag
        if fieldType.Tag.Get("inject") == "" {
            continue
        }

        if !field.CanSet() {
            continue
        }

        // Get service from container
        service, err := c.Get(reflect.New(field.Type()).Interface())
        if err != nil {
            return fmt.Errorf("failed to inject field %s: %w", fieldType.Name, err)
        }

        field.Set(reflect.ValueOf(service))
    }

    return nil
}

// Usage example
type Database struct{}
type Logger struct{}

type UserService struct {
    DB     *Database `inject:""`
    Logger *Logger   `inject:""`
}

func demonstrateDI() {
    container := NewContainer()
    container.Register(&Database{})
    container.Register(&Logger{})

    userService := &UserService{}
    if err := container.Inject(userService); err != nil {
        fmt.Printf("Injection failed: %v\n", err)
        return
    }

    fmt.Printf("UserService injected: DB=%v, Logger=%v\n",
        userService.DB != nil, userService.Logger != nil)
}
```

This comprehensive guide covers all aspects of reflection and metaprogramming in Go, from basic concepts to advanced patterns and real-world applications. Understanding these concepts will help you build flexible and dynamic Go applications while being aware of the performance implications and best practices.