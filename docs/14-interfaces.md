# Interfaces in Go

This comprehensive guide covers interfaces in Go, including their definition, implementation, type assertions, and advanced patterns for building flexible and maintainable applications.

## Table of Contents

1. [Overview](#overview)
2. [Interface Definition and Declaration](#interface-definition-and-declaration)
3. [Interface Implementation](#interface-implementation)
4. [Empty Interface](#empty-interface)
5. [Type Assertions](#type-assertions)
6. [Type Switches](#type-switches)
7. [Interface Embedding](#interface-embedding)
8. [Interface Internals](#interface-internals)
9. [Interface Patterns](#interface-patterns)
10. [Interface Segregation](#interface-segregation)
11. [Testing with Interfaces](#testing-with-interfaces)
12. [Best Practices](#best-practices)
13. [Common Mistakes](#common-mistakes)
14. [Performance Considerations](#performance-considerations)

## Overview

Interfaces in Go define method sets and enable polymorphism. They are implemented implicitly, meaning a type implements an interface simply by implementing all its methods.

### Key Characteristics

```go
// Interfaces define behavior, not data
// Implementation is implicit (duck typing)
// Interfaces are satisfied automatically
// Empty interface can hold any value
// Interfaces enable polymorphism and dependency injection
```

### Interfaces vs Other Languages

| Feature | Go Interfaces | Java/C# Interfaces |
|---------|---------------|-------------------|
| **Implementation** | Implicit | Explicit (implements keyword) |
| **Duck Typing** | Yes | No |
| **Multiple Inheritance** | Yes (embedding) | Yes |
| **Runtime Polymorphism** | Yes | Yes |
| **Compile-time Checking** | Yes | Yes |

## Interface Definition and Declaration

Multiple ways to define interfaces in Go.

### Basic Interface Definition

```go
// Simple interface
type Writer interface {
    Write([]byte) (int, error)
}

// Interface with multiple methods
type ReadWriter interface {
    Read([]byte) (int, error)
    Write([]byte) (int, error)
}

// Interface with no methods (empty interface)
type Any interface{}

// Interface with various method signatures
type Processor interface {
    Process(data []byte) error
    GetStatus() string
    SetConfig(key string, value interface{})
    Close() error
}

// Interface with generic methods (Go 1.18+)
type Container[T any] interface {
    Add(item T)
    Get(index int) T
    Size() int
}
```

### Interface Naming Conventions

```go
// Single method interfaces often end with -er
type Reader interface {
    Read([]byte) (int, error)
}

type Writer interface {
    Write([]byte) (int, error)
}

type Closer interface {
    Close() error
}

type Stringer interface {
    String() string
}

// Multi-method interfaces use descriptive names
type Database interface {
    Connect() error
    Query(sql string) ([]Row, error)
    Execute(sql string) error
    Close() error
}

type HTTPClient interface {
    Get(url string) (*Response, error)
    Post(url string, body []byte) (*Response, error)
    SetTimeout(duration time.Duration)
}
```

### Standard Library Interfaces

```go
import (
    "fmt"
    "io"
    "sort"
)

// Common standard library interfaces
var (
    // io package
    _ io.Reader     = (*os.File)(nil)
    _ io.Writer     = (*os.File)(nil)
    _ io.Closer     = (*os.File)(nil)
    _ io.ReadWriter = (*os.File)(nil)

    // fmt package
    _ fmt.Stringer = (*time.Time)(nil)
    _ fmt.GoStringer = (*url.URL)(nil)

    // sort package
    _ sort.Interface = (sort.IntSlice)(nil)
)

// Implementing standard interfaces
type LogWriter struct {
    prefix string
}

func (lw LogWriter) Write(data []byte) (int, error) {
    message := fmt.Sprintf("[%s] %s", lw.prefix, string(data))
    return fmt.Print(message)
}

// Usage with standard library
func demonstrateStandardInterfaces() {
    writer := LogWriter{prefix: "INFO"}

    // Can be used anywhere io.Writer is expected
    fmt.Fprintf(writer, "This is a log message\n")
    io.WriteString(writer, "Another message\n")
}
```

## Interface Implementation

How types implement interfaces in Go.

### Implicit Implementation

```go
// Interface definition
type Shape interface {
    Area() float64
    Perimeter() float64
}

// Rectangle implements Shape implicitly
type Rectangle struct {
    Width, Height float64
}

func (r Rectangle) Area() float64 {
    return r.Width * r.Height
}

func (r Rectangle) Perimeter() float64 {
    return 2 * (r.Width + r.Height)
}

// Circle implements Shape implicitly
type Circle struct {
    Radius float64
}

func (c Circle) Area() float64 {
    return math.Pi * c.Radius * c.Radius
}

func (c Circle) Perimeter() float64 {
    return 2 * math.Pi * c.Radius
}

// Function accepting interface
func PrintShapeInfo(s Shape) {
    fmt.Printf("Area: %.2f, Perimeter: %.2f\n", s.Area(), s.Perimeter())
}

// Usage
func demonstrateImplementation() {
    rect := Rectangle{Width: 5, Height: 3}
    circle := Circle{Radius: 2}

    PrintShapeInfo(rect)   // Works because Rectangle implements Shape
    PrintShapeInfo(circle) // Works because Circle implements Shape
}
```

### Method Sets and Receivers

```go
// Interface for incrementable types
type Incrementer interface {
    Increment()
    GetValue() int
}

// Counter with pointer receiver methods
type Counter struct {
    value int
}

func (c *Counter) Increment() {
    c.value++
}

func (c *Counter) GetValue() int {
    return c.value
}

// Method sets:
// *Counter implements Incrementer (pointer receiver methods)
// Counter does NOT implement Incrementer (would need value receiver methods)

func demonstrateMethodSets() {
    counter := &Counter{value: 0}

    // This works - *Counter implements Incrementer
    var inc Incrementer = counter
    inc.Increment()
    fmt.Println(inc.GetValue()) // 1

    // This would NOT compile:
    // var inc2 Incrementer = Counter{value: 0}
    // Error: Counter does not implement Incrementer
}
```

### Interface Satisfaction

```go
// Multiple interfaces can be satisfied by the same type
type Reader interface {
    Read([]byte) (int, error)
}

type Writer interface {
    Write([]byte) (int, error)
}

type Closer interface {
    Close() error
}

// File implements multiple interfaces
type File struct {
    name string
    data []byte
    pos  int
}

func (f *File) Read(p []byte) (int, error) {
    if f.pos >= len(f.data) {
        return 0, io.EOF
    }

    n := copy(p, f.data[f.pos:])
    f.pos += n
    return n, nil
}

func (f *File) Write(p []byte) (int, error) {
    f.data = append(f.data, p...)
    return len(p), nil
}

func (f *File) Close() error {
    f.pos = 0
    return nil
}

// File satisfies multiple interfaces
func demonstrateMultipleInterfaces() {
    file := &File{name: "test.txt"}

    // Can be used as different interface types
    var r Reader = file
    var w Writer = file
    var c Closer = file

    w.Write([]byte("Hello, World!"))

    buffer := make([]byte, 5)
    n, _ := r.Read(buffer)
    fmt.Printf("Read %d bytes: %s\n", n, string(buffer[:n]))

    c.Close()
}
```

## Empty Interface

The empty interface can hold values of any type.

### Understanding interface{}

```go
// Empty interface can hold any value
var anything interface{}

anything = 42
anything = "hello"
anything = []int{1, 2, 3}
anything = map[string]int{"key": 1}
anything = struct{ Name string }{Name: "Alice"}

// Function accepting any type
func PrintValue(value interface{}) {
    fmt.Printf("Value: %v, Type: %T\n", value, value)
}

// Slice of any type
func ProcessValues(values []interface{}) {
    for i, value := range values {
        fmt.Printf("Item %d: %v (%T)\n", i, value, value)
    }
}

// Usage
func demonstrateEmptyInterface() {
    PrintValue(42)
    PrintValue("hello")
    PrintValue([]int{1, 2, 3})

    mixed := []interface{}{
        42,
        "hello",
        true,
        3.14,
        []string{"a", "b"},
    }

    ProcessValues(mixed)
}
```

### Working with interface{} Values

```go
// Extracting values from interface{}
func ProcessAnyValue(value interface{}) {
    // Type assertion to get concrete type
    switch v := value.(type) {
    case int:
        fmt.Printf("Integer: %d\n", v)
    case string:
        fmt.Printf("String: %s (length: %d)\n", v, len(v))
    case bool:
        fmt.Printf("Boolean: %t\n", v)
    case []int:
        fmt.Printf("Integer slice: %v (sum: %d)\n", v, sum(v))
    case map[string]int:
        fmt.Printf("String-int map: %v\n", v)
    default:
        fmt.Printf("Unknown type: %T, value: %v\n", v, v)
    }
}

func sum(numbers []int) int {
    total := 0
    for _, num := range numbers {
        total += num
    }
    return total
}

// Generic container using interface{}
type Container struct {
    items []interface{}
}

func (c *Container) Add(item interface{}) {
    c.items = append(c.items, item)
}

func (c *Container) Get(index int) interface{} {
    if index < 0 || index >= len(c.items) {
        return nil
    }
    return c.items[index]
}

func (c *Container) Size() int {
    return len(c.items)
}

// Type-safe getter methods
func (c *Container) GetString(index int) (string, bool) {
    item := c.Get(index)
    if str, ok := item.(string); ok {
        return str, true
    }
    return "", false
}

func (c *Container) GetInt(index int) (int, bool) {
    item := c.Get(index)
    if num, ok := item.(int); ok {
        return num, true
    }
    return 0, false
}
```

### Modern Alternatives to interface{}

```go
// Go 1.18+ generics provide type-safe alternatives
type GenericContainer[T any] struct {
    items []T
}

func (c *GenericContainer[T]) Add(item T) {
    c.items = append(c.items, item)
}

func (c *GenericContainer[T]) Get(index int) T {
    return c.items[index]
}

func (c *GenericContainer[T]) Size() int {
    return len(c.items)
}

// Usage comparison
func compareContainers() {
    // Old way with interface{}
    oldContainer := &Container{}
    oldContainer.Add("hello")
    oldContainer.Add(42)

    // Need type assertion
    if str, ok := oldContainer.GetString(0); ok {
        fmt.Println("String:", str)
    }

    // New way with generics
    stringContainer := &GenericContainer[string]{}
    stringContainer.Add("hello")
    // stringContainer.Add(42) // Compile error - type safe!

    // No type assertion needed
    str := stringContainer.Get(0)
    fmt.Println("String:", str)
}
```

## Type Assertions

Type assertions extract concrete types from interface values.

### Basic Type Assertions

```go
// Basic type assertion syntax: value.(Type)
func demonstrateTypeAssertions() {
    var i interface{} = "hello"

    // Basic type assertion (panics if wrong type)
    s := i.(string)
    fmt.Println("String:", s)

    // Safe type assertion (returns value and boolean)
    s, ok := i.(string)
    if ok {
        fmt.Println("Successfully got string:", s)
    } else {
        fmt.Println("Not a string")
    }

    // Type assertion with wrong type
    // num := i.(int) // This would panic

    // Safe version
    num, ok := i.(int)
    if ok {
        fmt.Println("Integer:", num)
    } else {
        fmt.Println("Not an integer")
    }
}
```

### Type Assertion Patterns

```go
// Helper function for safe type assertions
func SafeStringAssertion(value interface{}) (string, error) {
    if str, ok := value.(string); ok {
        return str, nil
    }
    return "", fmt.Errorf("value is not a string, got %T", value)
}

func SafeIntAssertion(value interface{}) (int, error) {
    if num, ok := value.(int); ok {
        return num, nil
    }
    return 0, fmt.Errorf("value is not an int, got %T", value)
}

// Generic safe assertion function
func SafeAssertion[T any](value interface{}) (T, error) {
    if result, ok := value.(T); ok {
        return result, nil
    }
    var zero T
    return zero, fmt.Errorf("value is not of type %T, got %T", zero, value)
}

// Working with slices of interfaces
func ProcessInterfaceSlice(items []interface{}) {
    for i, item := range items {
        switch v := item.(type) {
        case string:
            fmt.Printf("Item %d: String '%s' (length: %d)\n", i, v, len(v))
        case int:
            fmt.Printf("Item %d: Integer %d\n", i, v)
        case bool:
            fmt.Printf("Item %d: Boolean %t\n", i, v)
        case nil:
            fmt.Printf("Item %d: nil\n", i)
        default:
            fmt.Printf("Item %d: Unknown type %T with value %v\n", i, v, v)
        }
    }
}
```

### Interface to Interface Assertions

```go
// Asserting to interface types
type Reader interface {
    Read([]byte) (int, error)
}

type Writer interface {
    Write([]byte) (int, error)
}

type ReadWriter interface {
    Reader
    Writer
}

type Closer interface {
    Close() error
}

func HandleReader(r Reader) {
    // Check if Reader also implements Writer
    if rw, ok := r.(ReadWriter); ok {
        fmt.Println("Reader also implements Writer")
        // Can use both Read and Write methods
        rw.Write([]byte("test"))
    }

    // Check if Reader implements Closer
    if closer, ok := r.(Closer); ok {
        defer closer.Close()
        fmt.Println("Reader implements Closer, will close when done")
    }

    // Use the reader
    buffer := make([]byte, 1024)
    r.Read(buffer)
}

// Multiple interface checks
func AnalyzeInterface(value interface{}) {
    fmt.Printf("Analyzing value of type %T\n", value)

    if stringer, ok := value.(fmt.Stringer); ok {
        fmt.Printf("  Implements Stringer: %s\n", stringer.String())
    }

    if reader, ok := value.(Reader); ok {
        fmt.Println("  Implements Reader")
        _ = reader // Use reader
    }

    if writer, ok := value.(Writer); ok {
        fmt.Println("  Implements Writer")
        _ = writer // Use writer
    }

    if closer, ok := value.(Closer); ok {
        fmt.Println("  Implements Closer")
        _ = closer // Use closer
    }
}
```

## Type Switches

Type switches provide a clean way to handle multiple types.

### Basic Type Switch

```go
// Type switch syntax
func ProcessValue(value interface{}) {
    switch v := value.(type) {
    case string:
        fmt.Printf("String: %s (length: %d)\n", v, len(v))
    case int:
        fmt.Printf("Integer: %d (squared: %d)\n", v, v*v)
    case bool:
        fmt.Printf("Boolean: %t\n", v)
    case []int:
        fmt.Printf("Integer slice: %v (sum: %d)\n", v, sum(v))
    case map[string]int:
        fmt.Printf("String-int map with %d entries\n", len(v))
    case nil:
        fmt.Println("nil value")
    default:
        fmt.Printf("Unknown type: %T\n", v)
    }
}

// Type switch with multiple types in one case
func HandleNumericTypes(value interface{}) {
    switch v := value.(type) {
    case int, int8, int16, int32, int64:
        fmt.Printf("Signed integer: %v\n", v)
    case uint, uint8, uint16, uint32, uint64:
        fmt.Printf("Unsigned integer: %v\n", v)
    case float32, float64:
        fmt.Printf("Floating point: %v\n", v)
    case complex64, complex128:
        fmt.Printf("Complex number: %v\n", v)
    default:
        fmt.Printf("Not a numeric type: %T\n", v)
    }
}
```

### Advanced Type Switch Patterns

```go
// Type switch with interface checks
func HandleIO(value interface{}) {
    switch v := value.(type) {
    case Reader:
        fmt.Println("Can read from this")
        handleReader(v)
    case Writer:
        fmt.Println("Can write to this")
        handleWriter(v)
    case ReadWriter:
        fmt.Println("Can both read and write")
        handleReader(v)
        handleWriter(v)
    case Closer:
        fmt.Println("Can close this")
        defer v.Close()
    default:
        fmt.Printf("Cannot handle type %T\n", v)
    }
}

func handleReader(r Reader) {
    buffer := make([]byte, 1024)
    r.Read(buffer)
}

func handleWriter(w Writer) {
    w.Write([]byte("test data"))
}

// Type switch for error handling
func HandleError(err interface{}) {
    switch e := err.(type) {
    case nil:
        fmt.Println("No error")
    case *os.PathError:
        fmt.Printf("Path error: %s (path: %s)\n", e.Err, e.Path)
    case *net.OpError:
        fmt.Printf("Network error: %s (op: %s)\n", e.Err, e.Op)
    case error:
        fmt.Printf("Generic error: %s\n", e.Error())
    default:
        fmt.Printf("Not an error type: %T\n", e)
    }
}

// Type switch in method
type Processor struct {
    name string
}

func (p *Processor) Process(data interface{}) error {
    switch v := data.(type) {
    case string:
        return p.processString(v)
    case []byte:
        return p.processBytes(v)
    case io.Reader:
        return p.processReader(v)
    default:
        return fmt.Errorf("unsupported data type: %T", v)
    }
}

func (p *Processor) processString(s string) error {
    fmt.Printf("[%s] Processing string: %s\n", p.name, s)
    return nil
}

func (p *Processor) processBytes(b []byte) error {
    fmt.Printf("[%s] Processing %d bytes\n", p.name, len(b))
    return nil
}

func (p *Processor) processReader(r io.Reader) error {
    fmt.Printf("[%s] Processing reader\n", p.name)
    return nil
}
```

## Interface Embedding

Interfaces can embed other interfaces to compose larger interfaces.

### Basic Interface Embedding

```go
// Basic interfaces
type Reader interface {
    Read([]byte) (int, error)
}

type Writer interface {
    Write([]byte) (int, error)
}

type Closer interface {
    Close() error
}

// Embedded interfaces
type ReadWriter interface {
    Reader  // Embedded interface
    Writer  // Embedded interface
}

type ReadWriteCloser interface {
    Reader
    Writer
    Closer
}

// Alternative syntax (equivalent)
type ReadWriteCloser2 interface {
    Read([]byte) (int, error)
    Write([]byte) (int, error)
    Close() error
}

// Implementation
type File struct {
    name string
    data []byte
    pos  int
}

func (f *File) Read(p []byte) (int, error) {
    // Implementation
    return 0, nil
}

func (f *File) Write(p []byte) (int, error) {
    // Implementation
    return len(p), nil
}

func (f *File) Close() error {
    // Implementation
    return nil
}

// File automatically implements all embedded interfaces
func demonstrateEmbedding() {
    file := &File{name: "test.txt"}

    // Can be used as any of the embedded interfaces
    var r Reader = file
    var w Writer = file
    var c Closer = file
    var rw ReadWriter = file
    var rwc ReadWriteCloser = file

    _ = r
    _ = w
    _ = c
    _ = rw
    _ = rwc
}
```

### Complex Interface Embedding

```go
// HTTP-related interfaces
type HTTPHandler interface {
    ServeHTTP(ResponseWriter, *Request)
}

type ResponseWriter interface {
    Header() Header
    Write([]byte) (int, error)
    WriteHeader(statusCode int)
}

type Flusher interface {
    Flush()
}

type Hijacker interface {
    Hijack() (net.Conn, *bufio.ReadWriter, error)
}

// Composed interface
type AdvancedResponseWriter interface {
    ResponseWriter
    Flusher
    Hijacker
}

// Database interfaces
type Querier interface {
    Query(query string, args ...interface{}) (*Rows, error)
    QueryRow(query string, args ...interface{}) *Row
}

type Executer interface {
    Exec(query string, args ...interface{}) (Result, error)
}

type Transactioner interface {
    Begin() (*Tx, error)
    Commit() error
    Rollback() error
}

// Full database interface
type Database interface {
    Querier
    Executer
    Transactioner
    Closer
}
```

### Interface Embedding Patterns

```go
// Extending standard library interfaces
type ExtendedWriter interface {
    io.Writer
    WriteString(s string) (n int, err error)
    Flush() error
}

type ExtendedReader interface {
    io.Reader
    ReadString(delim byte) (string, error)
    Peek(n int) ([]byte, error)
}

// Service interfaces
type UserService interface {
    GetUser(id int) (*User, error)
    CreateUser(user *User) error
    UpdateUser(user *User) error
    DeleteUser(id int) error
}

type AuthService interface {
    Login(username, password string) (*Token, error)
    Logout(token string) error
    ValidateToken(token string) (*User, error)
}

// Combined service
type UserAuthService interface {
    UserService
    AuthService
}

// Optional interfaces pattern
type BasicProcessor interface {
    Process(data []byte) error
}

type ConfigurableProcessor interface {
    BasicProcessor
    SetConfig(config map[string]interface{}) error
}

type MonitorableProcessor interface {
    BasicProcessor
    GetMetrics() map[string]interface{}
}

type AdvancedProcessor interface {
    ConfigurableProcessor
    MonitorableProcessor
}
```

## Interface Internals

Understanding how interfaces work internally in Go.

### Interface Structure

```go
import "unsafe"

// Conceptual interface structure
// Empty interface (interface{})
type eface struct {
    _type *_type       // Type information
    data  unsafe.Pointer // Pointer to actual value
}

// Non-empty interface
type iface struct {
    tab  *itab         // Interface table
    data unsafe.Pointer // Pointer to actual value
}

// Interface table
type itab struct {
    inter *interfacetype // Interface type
    _type *_type         // Concrete type
    hash  uint32         // Copy of _type.hash
    _     [4]byte        // Padding
    fun   [1]uintptr     // Method table
}
```

### Interface Assignment and Method Calls

```go
// Interface assignment creates interface value
func demonstrateInterfaceInternals() {
    var w io.Writer

    // w is nil interface (both type and data are nil)
    fmt.Printf("nil interface: %v\n", w == nil) // true

    // Assign concrete type
    var buf bytes.Buffer
    w = &buf

    // Now w contains:
    // - Type information pointing to *bytes.Buffer
    // - Data pointer pointing to buf
    fmt.Printf("assigned interface: %v\n", w == nil) // false

    // Method call goes through interface table
    w.Write([]byte("hello")) // Calls (*bytes.Buffer).Write
}

// Interface comparison
func demonstrateInterfaceComparison() {
    var w1, w2 io.Writer

    // Both nil - equal
    fmt.Println(w1 == w2) // true

    // Assign same type and value
    buf1 := &bytes.Buffer{}
    buf2 := &bytes.Buffer{}

    w1 = buf1
    w2 = buf1
    fmt.Println(w1 == w2) // true (same type, same data pointer)

    w1 = buf1
    w2 = buf2
    fmt.Println(w1 == w2) // false (same type, different data pointers)
}
```

### Interface Performance

```go
import "testing"

// Interface call vs direct call performance
type Calculator struct {
    value int
}

func (c *Calculator) Add(n int) {
    c.value += n
}

type Adder interface {
    Add(int)
}

// Benchmark direct call
func BenchmarkDirectCall(b *testing.B) {
    calc := &Calculator{}

    for i := 0; i < b.N; i++ {
        calc.Add(1)
    }
}

// Benchmark interface call
func BenchmarkInterfaceCall(b *testing.B) {
    calc := &Calculator{}
    var adder Adder = calc

    for i := 0; i < b.N; i++ {
        adder.Add(1)
    }
}

// Interface calls have small overhead due to:
// 1. Method lookup through interface table
// 2. Indirect function call
// 3. Potential cache misses
```

## Interface Patterns

Common patterns for using interfaces effectively.

### Dependency Injection

```go
// Define interfaces for dependencies
type Logger interface {
    Log(message string)
    LogError(err error)
}

type Database interface {
    Save(entity interface{}) error
    Find(id int, entity interface{}) error
}

type EmailSender interface {
    SendEmail(to, subject, body string) error
}

// Service using dependency injection
type UserService struct {
    db     Database
    logger Logger
    email  EmailSender
}

func NewUserService(db Database, logger Logger, email EmailSender) *UserService {
    return &UserService{
        db:     db,
        logger: logger,
        email:  email,
    }
}

func (s *UserService) CreateUser(user *User) error {
    s.logger.Log("Creating user: " + user.Name)

    if err := s.db.Save(user); err != nil {
        s.logger.LogError(err)
        return err
    }

    if err := s.email.SendEmail(user.Email, "Welcome", "Welcome to our service!"); err != nil {
        s.logger.LogError(err)
        // Don't fail user creation if email fails
    }

    return nil
}

// Implementations
type ConsoleLogger struct{}

func (l ConsoleLogger) Log(message string) {
    fmt.Println("LOG:", message)
}

func (l ConsoleLogger) LogError(err error) {
    fmt.Println("ERROR:", err)
}

type MockDatabase struct {
    users map[int]*User
}

func (db *MockDatabase) Save(entity interface{}) error {
    if user, ok := entity.(*User); ok {
        db.users[user.ID] = user
    }
    return nil
}

func (db *MockDatabase) Find(id int, entity interface{}) error {
    if user, ok := entity.(*User); ok {
        if found, exists := db.users[id]; exists {
            *user = *found
        }
    }
    return nil
}
```

### Strategy Pattern

```go
// Strategy interface
type SortStrategy interface {
    Sort([]int)
}

// Concrete strategies
type BubbleSort struct{}

func (bs BubbleSort) Sort(data []int) {
    n := len(data)
    for i := 0; i < n; i++ {
        for j := 0; j < n-i-1; j++ {
            if data[j] > data[j+1] {
                data[j], data[j+1] = data[j+1], data[j]
            }
        }
    }
}

type QuickSort struct{}

func (qs QuickSort) Sort(data []int) {
    if len(data) < 2 {
        return
    }

    pivot := partition(data)
    qs.Sort(data[:pivot])
    qs.Sort(data[pivot+1:])
}

func partition(data []int) int {
    // Quick sort partition implementation
    return 0 // Simplified
}

// Context using strategy
type Sorter struct {
    strategy SortStrategy
}

func (s *Sorter) SetStrategy(strategy SortStrategy) {
    s.strategy = strategy
}

func (s *Sorter) Sort(data []int) {
    if s.strategy != nil {
        s.strategy.Sort(data)
    }
}

// Usage
func demonstrateStrategy() {
    data := []int{64, 34, 25, 12, 22, 11, 90}
    sorter := &Sorter{}

    // Use bubble sort
    sorter.SetStrategy(BubbleSort{})
    sorter.Sort(data)

    // Switch to quick sort
    sorter.SetStrategy(QuickSort{})
    sorter.Sort(data)
}
```

### Adapter Pattern

```go
// Target interface
type MediaPlayer interface {
    Play(audioType, fileName string)
}

// Adaptee interfaces
type AdvancedMediaPlayer interface {
    PlayVlc(fileName string)
    PlayMp4(fileName string)
}

// Concrete adaptees
type VlcPlayer struct{}

func (v VlcPlayer) PlayVlc(fileName string) {
    fmt.Printf("Playing vlc file: %s\n", fileName)
}

func (v VlcPlayer) PlayMp4(fileName string) {
    // VlcPlayer doesn't support mp4
}

type Mp4Player struct{}

func (m Mp4Player) PlayVlc(fileName string) {
    // Mp4Player doesn't support vlc
}

func (m Mp4Player) PlayMp4(fileName string) {
    fmt.Printf("Playing mp4 file: %s\n", fileName)
}

// Adapter
type MediaAdapter struct {
    advancedPlayer AdvancedMediaPlayer
}

func NewMediaAdapter(audioType string) *MediaAdapter {
    switch audioType {
    case "vlc":
        return &MediaAdapter{advancedPlayer: VlcPlayer{}}
    case "mp4":
        return &MediaAdapter{advancedPlayer: Mp4Player{}}
    default:
        return nil
    }
}

func (ma *MediaAdapter) Play(audioType, fileName string) {
    switch audioType {
    case "vlc":
        ma.advancedPlayer.PlayVlc(fileName)
    case "mp4":
        ma.advancedPlayer.PlayMp4(fileName)
    }
}

// Concrete target
type AudioPlayer struct {
    adapter *MediaAdapter
}

func (ap *AudioPlayer) Play(audioType, fileName string) {
    switch audioType {
    case "mp3":
        fmt.Printf("Playing mp3 file: %s\n", fileName)
    case "vlc", "mp4":
        ap.adapter = NewMediaAdapter(audioType)
        if ap.adapter != nil {
            ap.adapter.Play(audioType, fileName)
        }
    default:
        fmt.Printf("Invalid media. %s format not supported\n", audioType)
    }
}
```

## Interface Segregation

Following the Interface Segregation Principle to create focused, cohesive interfaces.

### Small, Focused Interfaces

```go
// Bad: Large interface with many responsibilities
type BadUserManager interface {
    // User CRUD operations
    CreateUser(user *User) error
    GetUser(id int) (*User, error)
    UpdateUser(user *User) error
    DeleteUser(id int) error

    // Authentication
    Login(username, password string) (*Token, error)
    Logout(token string) error
    ValidateToken(token string) (*User, error)

    // Email operations
    SendWelcomeEmail(user *User) error
    SendPasswordResetEmail(email string) error

    // Reporting
    GetUserStats() (*UserStats, error)
    GenerateUserReport() ([]byte, error)

    // Caching
    CacheUser(user *User) error
    InvalidateUserCache(id int) error
}

// Good: Segregated interfaces
type UserRepository interface {
    CreateUser(user *User) error
    GetUser(id int) (*User, error)
    UpdateUser(user *User) error
    DeleteUser(id int) error
}

type Authenticator interface {
    Login(username, password string) (*Token, error)
    Logout(token string) error
    ValidateToken(token string) (*User, error)
}

type EmailService interface {
    SendWelcomeEmail(user *User) error
    SendPasswordResetEmail(email string) error
}

type UserReporter interface {
    GetUserStats() (*UserStats, error)
    GenerateUserReport() ([]byte, error)
}

type UserCache interface {
    CacheUser(user *User) error
    InvalidateUserCache(id int) error
}
```

### Composing Interfaces

```go
// Compose larger interfaces from smaller ones when needed
type UserService interface {
    UserRepository
    Authenticator
}

type FullUserService interface {
    UserRepository
    Authenticator
    EmailService
    UserReporter
    UserCache
}

// Implementation can choose which interfaces to implement
type BasicUserService struct {
    db Database
}

func (s *BasicUserService) CreateUser(user *User) error {
    return s.db.Save(user)
}

func (s *BasicUserService) GetUser(id int) (*User, error) {
    var user User
    err := s.db.Find(id, &user)
    return &user, err
}

// ... implement other UserRepository methods

func (s *BasicUserService) Login(username, password string) (*Token, error) {
    // Implementation
    return nil, nil
}

// ... implement other Authenticator methods

// BasicUserService implements UserService (UserRepository + Authenticator)
var _ UserService = (*BasicUserService)(nil)
```

### Optional Interface Pattern

```go
// Core interface
type FileProcessor interface {
    ProcessFile(filename string) error
}

// Optional interfaces
type Configurable interface {
    SetConfig(config map[string]interface{}) error
}

type Monitorable interface {
    GetMetrics() map[string]interface{}
}

type Cacheable interface {
    EnableCache(enabled bool)
    ClearCache() error
}

// Implementation
type TextProcessor struct {
    config  map[string]interface{}
    metrics map[string]interface{}
    cache   map[string][]byte
}

func (tp *TextProcessor) ProcessFile(filename string) error {
    // Core functionality
    return nil
}

func (tp *TextProcessor) SetConfig(config map[string]interface{}) error {
    tp.config = config
    return nil
}

func (tp *TextProcessor) GetMetrics() map[string]interface{} {
    return tp.metrics
}

func (tp *TextProcessor) EnableCache(enabled bool) {
    if !enabled {
        tp.cache = nil
    } else if tp.cache == nil {
        tp.cache = make(map[string][]byte)
    }
}

func (tp *TextProcessor) ClearCache() error {
    tp.cache = make(map[string][]byte)
    return nil
}

// Usage with optional interfaces
func UseProcessor(processor FileProcessor) {
    // Core functionality
    processor.ProcessFile("input.txt")

    // Check for optional capabilities
    if configurable, ok := processor.(Configurable); ok {
        configurable.SetConfig(map[string]interface{}{
            "encoding": "utf-8",
            "buffer":   4096,
        })
    }

    if monitorable, ok := processor.(Monitorable); ok {
        metrics := monitorable.GetMetrics()
        fmt.Printf("Metrics: %v\n", metrics)
    }

    if cacheable, ok := processor.(Cacheable); ok {
        cacheable.EnableCache(true)
    }
}
```

## Testing with Interfaces

Interfaces make testing easier by enabling dependency injection and mocking.

### Mock Implementations

```go
// Production interface
type PaymentGateway interface {
    ProcessPayment(amount float64, cardToken string) (*PaymentResult, error)
    RefundPayment(transactionID string) error
}

// Production implementation
type StripeGateway struct {
    apiKey string
}

func (sg *StripeGateway) ProcessPayment(amount float64, cardToken string) (*PaymentResult, error) {
    // Real Stripe API call
    return &PaymentResult{
        TransactionID: "stripe_tx_123",
        Status:        "completed",
        Amount:        amount,
    }, nil
}

func (sg *StripeGateway) RefundPayment(transactionID string) error {
    // Real Stripe refund API call
    return nil
}

// Mock implementation for testing
type MockPaymentGateway struct {
    payments map[string]*PaymentResult
    refunds  map[string]bool

    // Control test behavior
    shouldFailPayment bool
    shouldFailRefund  bool
}

func NewMockPaymentGateway() *MockPaymentGateway {
    return &MockPaymentGateway{
        payments: make(map[string]*PaymentResult),
        refunds:  make(map[string]bool),
    }
}

func (mpg *MockPaymentGateway) ProcessPayment(amount float64, cardToken string) (*PaymentResult, error) {
    if mpg.shouldFailPayment {
        return nil, errors.New("payment failed")
    }

    result := &PaymentResult{
        TransactionID: fmt.Sprintf("mock_tx_%d", len(mpg.payments)+1),
        Status:        "completed",
        Amount:        amount,
    }

    mpg.payments[result.TransactionID] = result
    return result, nil
}

func (mpg *MockPaymentGateway) RefundPayment(transactionID string) error {
    if mpg.shouldFailRefund {
        return errors.New("refund failed")
    }

    mpg.refunds[transactionID] = true
    return nil
}

func (mpg *MockPaymentGateway) SetFailPayment(fail bool) {
    mpg.shouldFailPayment = fail
}

func (mpg *MockPaymentGateway) SetFailRefund(fail bool) {
    mpg.shouldFailRefund = fail
}

func (mpg *MockPaymentGateway) GetPaymentCount() int {
    return len(mpg.payments)
}
```

### Test Examples

```go
import "testing"

// Service under test
type OrderService struct {
    gateway PaymentGateway
}

func NewOrderService(gateway PaymentGateway) *OrderService {
    return &OrderService{gateway: gateway}
}

func (os *OrderService) ProcessOrder(order *Order) error {
    result, err := os.gateway.ProcessPayment(order.Total, order.CardToken)
    if err != nil {
        return fmt.Errorf("payment failed: %w", err)
    }

    order.TransactionID = result.TransactionID
    order.Status = "paid"
    return nil
}

func (os *OrderService) RefundOrder(order *Order) error {
    if order.TransactionID == "" {
        return errors.New("no transaction to refund")
    }

    return os.gateway.RefundPayment(order.TransactionID)
}

// Tests using mock
func TestOrderService_ProcessOrder_Success(t *testing.T) {
    // Arrange
    mockGateway := NewMockPaymentGateway()
    service := NewOrderService(mockGateway)

    order := &Order{
        ID:        1,
        Total:     99.99,
        CardToken: "card_123",
    }

    // Act
    err := service.ProcessOrder(order)

    // Assert
    if err != nil {
        t.Errorf("Expected no error, got %v", err)
    }

    if order.Status != "paid" {
        t.Errorf("Expected status 'paid', got %s", order.Status)
    }

    if order.TransactionID == "" {
        t.Error("Expected transaction ID to be set")
    }

    if mockGateway.GetPaymentCount() != 1 {
        t.Errorf("Expected 1 payment, got %d", mockGateway.GetPaymentCount())
    }
}

func TestOrderService_ProcessOrder_PaymentFailure(t *testing.T) {
    // Arrange
    mockGateway := NewMockPaymentGateway()
    mockGateway.SetFailPayment(true)
    service := NewOrderService(mockGateway)

    order := &Order{
        ID:        1,
        Total:     99.99,
        CardToken: "card_123",
    }

    // Act
    err := service.ProcessOrder(order)

    // Assert
    if err == nil {
        t.Error("Expected error, got nil")
    }

    if order.Status == "paid" {
        t.Error("Order should not be marked as paid when payment fails")
    }
}

func TestOrderService_RefundOrder_Success(t *testing.T) {
    // Arrange
    mockGateway := NewMockPaymentGateway()
    service := NewOrderService(mockGateway)

    order := &Order{
        ID:            1,
        TransactionID: "tx_123",
    }

    // Act
    err := service.RefundOrder(order)

    // Assert
    if err != nil {
        t.Errorf("Expected no error, got %v", err)
    }
}
```

### Interface-based Test Helpers

```go
// Test helper that works with any Logger interface
func TestWithLogger(t *testing.T, logger Logger) {
    // Test code that uses logger
    logger.Log("test message")
    logger.LogError(errors.New("test error"))
}

// Mock logger for testing
type TestLogger struct {
    messages []string
    errors   []error
}

func (tl *TestLogger) Log(message string) {
    tl.messages = append(tl.messages, message)
}

func (tl *TestLogger) LogError(err error) {
    tl.errors = append(tl.errors, err)
}

func (tl *TestLogger) GetMessages() []string {
    return tl.messages
}

func (tl *TestLogger) GetErrors() []error {
    return tl.errors
}

// Test using the helper
func TestSomethingWithLogging(t *testing.T) {
    logger := &TestLogger{}

    // Test code
    TestWithLogger(t, logger)

    // Verify logging behavior
    messages := logger.GetMessages()
    if len(messages) != 1 || messages[0] != "test message" {
        t.Errorf("Expected 1 message 'test message', got %v", messages)
    }

    errors := logger.GetErrors()
    if len(errors) != 1 || errors[0].Error() != "test error" {
        t.Errorf("Expected 1 error 'test error', got %v", errors)
    }
}
```

## Best Practices

Guidelines for effective interface usage in Go.

### Interface Design Principles

```go
// 1. Keep interfaces small and focused
// Good: Single responsibility
type Reader interface {
    Read([]byte) (int, error)
}

type Writer interface {
    Write([]byte) (int, error)
}

// Avoid: Large interfaces
type BadFileManager interface {
    Read([]byte) (int, error)
    Write([]byte) (int, error)
    Close() error
    Seek(int64, int) (int64, error)
    Stat() (FileInfo, error)
    Chmod(FileMode) error
    Chown(int, int) error
    Truncate(int64) error
    Sync() error
}

// 2. Accept interfaces, return concrete types
// Good: Accept interface parameter
func ProcessData(r Reader) ([]byte, error) {
    data, err := io.ReadAll(r)
    return data, err
}

// Good: Return concrete type
func NewFileReader(filename string) (*os.File, error) {
    return os.Open(filename)
}

// Avoid: Return interface (unless necessary)
func BadNewReader(filename string) (Reader, error) {
    return os.Open(filename) // Limits caller's options
}

// 3. Define interfaces at the point of use
// Good: Define interface where it's used
package processor

type DataSource interface {
    GetData() ([]byte, error)
}

func ProcessData(source DataSource) error {
    data, err := source.GetData()
    if err != nil {
        return err
    }
    // Process data
    return nil
}

// Avoid: Define interfaces in implementation package
package database

type DataProvider interface { // Don't define here
    GetData() ([]byte, error)
}

type Database struct{}

func (db *Database) GetData() ([]byte, error) {
    // Implementation
    return nil, nil
}
```

### Naming Conventions

```go
// 1. Single-method interfaces often end with -er
type Stringer interface {
    String() string
}

type Closer interface {
    Close() error
}

type Validator interface {
    Validate() error
}

// 2. Multi-method interfaces use descriptive names
type UserRepository interface {
    GetUser(id int) (*User, error)
    SaveUser(user *User) error
    DeleteUser(id int) error
}

type PaymentProcessor interface {
    ProcessPayment(amount float64) (*Receipt, error)
    RefundPayment(receiptID string) error
    GetPaymentStatus(receiptID string) (string, error)
}

// 3. Avoid generic names like Manager, Handler (unless specific)
// Good: Specific names
type EmailSender interface {
    SendEmail(to, subject, body string) error
}

type ConfigLoader interface {
    LoadConfig(path string) (*Config, error)
}

// Avoid: Generic names
type Manager interface {
    Manage() error
}

type Handler interface {
    Handle() error
}
```

### Interface Composition

```go
// Compose interfaces from smaller ones
type ReadCloser interface {
    Reader
    Closer
}

type WriteCloser interface {
    Writer
    Closer
}

type ReadWriteCloser interface {
    Reader
    Writer
    Closer
}

// Use embedding for optional capabilities
type BasicService interface {
    Process(data []byte) error
}

type ConfigurableService interface {
    BasicService
    Configure(config map[string]interface{}) error
}

type MonitorableService interface {
    BasicService
    GetMetrics() map[string]interface{}
}

type AdvancedService interface {
    ConfigurableService
    MonitorableService
}
```

## Common Mistakes

Avoiding common pitfalls when working with interfaces.

### Mistake 1: Over-engineering with Interfaces

```go
// Wrong: Creating interfaces for everything
type UserGetter interface {
    GetUser(id int) (*User, error)
}

type UserSetter interface {
    SetUser(user *User) error
}

type UserDeleter interface {
    DeleteUser(id int) error
}

// If you only have one implementation and no testing needs,
// this is over-engineering

// Better: Start with concrete types, add interfaces when needed
type UserService struct {
    db *sql.DB
}

func (us *UserService) GetUser(id int) (*User, error) {
    // Implementation
    return nil, nil
}

func (us *UserService) SetUser(user *User) error {
    // Implementation
    return nil
}

func (us *UserService) DeleteUser(id int) error {
    // Implementation
    return nil
}

// Add interfaces later when you need abstraction
```

### Mistake 2: Interface Pollution

```go
// Wrong: Defining interfaces in implementation packages
package database

type UserRepository interface {
    GetUser(id int) (*User, error)
    SaveUser(user *User) error
}

type PostgresUserRepository struct {
    db *sql.DB
}

func (pur *PostgresUserRepository) GetUser(id int) (*User, error) {
    // Implementation
    return nil, nil
}

func (pur *PostgresUserRepository) SaveUser(user *User) error {
    // Implementation
    return nil
}

// Better: Define interfaces where they're used
package service

type UserRepository interface {
    GetUser(id int) (*User, error)
    SaveUser(user *User) error
}

type UserService struct {
    repo UserRepository
}

func NewUserService(repo UserRepository) *UserService {
    return &UserService{repo: repo}
}
```

### Mistake 3: Returning Interfaces

```go
// Wrong: Returning interfaces unnecessarily
func NewDatabase() Database {
    return &PostgresDB{}
}

// This limits the caller - they can't access PostgresDB-specific methods

// Better: Return concrete types
func NewDatabase() *PostgresDB {
    return &PostgresDB{}
}

// Caller can choose to use as interface
var db Database = NewDatabase()

// Exception: When you truly need to hide implementation
func NewDatabaseFromConfig(config *Config) Database {
    switch config.Type {
    case "postgres":
        return &PostgresDB{}
    case "mysql":
        return &MySQLDB{}
    default:
        return &InMemoryDB{}
    }
}
```

### Mistake 4: Interface{} Overuse

```go
// Wrong: Using interface{} when you know the type
func ProcessData(data interface{}) error {
    // Need type assertion everywhere
    switch v := data.(type) {
    case string:
        return processString(v)
    case []byte:
        return processBytes(v)
    default:
        return errors.New("unsupported type")
    }
}

// Better: Use specific types or generics
func ProcessString(data string) error {
    return processString(data)
}

func ProcessBytes(data []byte) error {
    return processBytes(data)
}

// Or with generics (Go 1.18+)
func ProcessData[T string | []byte](data T) error {
    // Type-safe processing
    return nil
}

// interface{} is appropriate for truly generic containers
func LogValue(key string, value interface{}) {
    fmt.Printf("%s: %v\n", key, value)
}
```

## Performance Considerations

Understanding the performance implications of interfaces.

### Interface Call Overhead

```go
import "testing"

type Calculator struct {
    value int
}

func (c *Calculator) Add(n int) {
    c.value += n
}

type Adder interface {
    Add(int)
}

// Direct call benchmark
func BenchmarkDirectCall(b *testing.B) {
    calc := &Calculator{}

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        calc.Add(1)
    }
}

// Interface call benchmark
func BenchmarkInterfaceCall(b *testing.B) {
    calc := &Calculator{}
    var adder Adder = calc

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        adder.Add(1)
    }
}

// Results show interface calls are slightly slower due to:
// - Method lookup through interface table
// - Indirect function call
// - Potential CPU cache misses
```

### Memory Considerations

```go
import "unsafe"

// Interface values have overhead
func demonstrateInterfaceMemory() {
    var i int = 42
    var iface interface{} = i

    fmt.Printf("int size: %d bytes\n", unsafe.Sizeof(i))       // 8 bytes
    fmt.Printf("interface{} size: %d bytes\n", unsafe.Sizeof(iface)) // 16 bytes

    // Interface contains:
    // - Type pointer (8 bytes)
    // - Data pointer (8 bytes)
    // Total: 16 bytes overhead
}

// Small values are copied into interface
func smallValueInterface() {
    var b byte = 42
    var iface interface{} = b

    // byte value is copied into interface
    // No additional allocation for small values
    _ = iface
}

// Large values may cause allocation
func largeValueInterface() {
    var large [1000]int
    var iface interface{} = large

    // Large value may be allocated on heap
    // Interface points to heap allocation
    _ = iface
}
```

### Optimization Tips

```go
// 1. Avoid interface{} in hot paths
func hotPathBad(values []interface{}) int {
    sum := 0
    for _, v := range values {
        if num, ok := v.(int); ok {
            sum += num
        }
    }
    return sum
}

func hotPathGood(values []int) int {
    sum := 0
    for _, v := range values {
        sum += v
    }
    return sum
}

// 2. Use concrete types for data structures
type BadContainer struct {
    items []interface{}
}

type GoodContainer struct {
    items []int
}

// 3. Consider generics for type-safe containers (Go 1.18+)
type GenericContainer[T any] struct {
    items []T
}

// 4. Minimize interface conversions in loops
func inefficientLoop(items []SomeInterface) {
    for _, item := range items {
        if concrete, ok := item.(*ConcreteType); ok {
            // Type assertion in every iteration
            concrete.DoSomething()
        }
    }
}

func efficientLoop(items []SomeInterface) {
    // Pre-filter concrete types
    var concreteItems []*ConcreteType
    for _, item := range items {
        if concrete, ok := item.(*ConcreteType); ok {
            concreteItems = append(concreteItems, concrete)
        }
    }

    // Process without type assertions
    for _, concrete := range concreteItems {
        concrete.DoSomething()
    }
}
```

This comprehensive guide covers all aspects of interfaces in Go, from basic usage to advanced patterns and performance optimization. Understanding these concepts will help you design flexible, testable, and maintainable Go applications.
