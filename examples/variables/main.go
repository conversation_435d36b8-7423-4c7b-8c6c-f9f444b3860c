package main

import (
	"fmt"
	"time"
)

// Package-level variables
var packageName string = "variables"

// Using var block for organization
var (
	version   = "1.0.0"
	buildDate = "2024-01-01"
	debugMode = false
)

func main() {
	fmt.Println("--- Variables ---")
	fmt.Println("--- Package-level variables ---")
	fmt.Println("Package-level variable:", packageName)
	fmt.Println("Version:", version)
	fmt.Println("Build Date:", buildDate)
	fmt.Println("Debug Mode:", debugMode)
	fmt.Println("--- Package-level variables ---")

	fmt.Println("--- Function-level variables ---")
	fmt.Println("*** Using Var Keyword ***")
	// Basic syntax: var name type
	var variable1 string

	// With initialization
	var variable2 string = "Hello, World!"

	// Multiple variables of same type
	var variable3, variable4, variable5 string

	// Multiple variables with initialization
	var variable6, variable7, variable8 int = 1, 2, 3

	// Different types in one declaration
	var (
		variable9  string  = "John"
		variable10 int     = 30
		variable11 float64 = 5.9
		variable12 bool    = true
	)

	fmt.Println("variable1:", variable1)
	fmt.Println("variable2:", variable2)
	fmt.Println("variable3:", variable3)
	fmt.Println("variable4:", variable4)
	fmt.Println("variable5:", variable5)
	fmt.Println("variable6:", variable6)
	fmt.Println("variable7:", variable7)
	fmt.Println("variable8:", variable8)
	fmt.Println("variable9:", variable9)
	fmt.Println("variable10:", variable10)
	fmt.Println("variable11:", variable11)
	fmt.Println("variable12:", variable12)

	fmt.Println("*** Using Var Keyword ***")

	fmt.Println("*** Using Short Variable Declaration ***")
	// Short declaration syntax: name := value
	shortVariable1 := "Hello, World!"
	// Multiple variables
	shortVariable2, shortVariable3 := "John", 30

	// Mixed declaration (at least one new variable)
	var shortVariable4 int
	shortVariable4, shortVariable5 := 10, 20 // newVar is newly declared

	fmt.Println("shortVariable1:", shortVariable1)
	fmt.Println("shortVariable2:", shortVariable2)
	fmt.Println("shortVariable3:", shortVariable3)
	fmt.Println("shortVariable4:", shortVariable4)
	fmt.Println("shortVariable5:", shortVariable5)

	fmt.Println("*** Using Short Variable Declaration ***")

	fmt.Println("--- Function-level variables ---")

	fmt.Println("--- Block-level variables ---")

	// Block-level variables
	{
		blockVariable := "I am in a block"
		fmt.Println("Block-level variable:", blockVariable)
	}

	// fmt.Println("Block-level variable:", blockVariable) // Error: blockVariable is not accessible here

	fmt.Println("--- Block-level variables ---")

	fmt.Println("--- Shadowing variables ---")

	// Shadowing variables
	shadowVariable := "I am in the outer scope"
	{
		shadowVariable := "I am in the inner scope"
		fmt.Println("Inner scope:", shadowVariable)
	}
	fmt.Println("Outer scope:", shadowVariable)

	fmt.Println("--- Shadowing variables ---")
	fmt.Println("--- Variables ---")

	fmt.Println("--- Constants ---")

	// Constants
	const constant1 string = "I am a constant"
	const constant2 int = 42
	const constant3 float64 = 3.14159
	const constant4 bool = true

	fmt.Println("constant1:", constant1)
	fmt.Println("constant2:", constant2)
	fmt.Println("constant3:", constant3)
	fmt.Println("constant4:", constant4)

	// Constants must be known at compile time
	const (
		maxUsers = 1000
		timeout  = 30 * time.Second // OK: time.Second is a constant
		version  = "1.0.0"
	)
	fmt.Println("maxUsers:", maxUsers)
	fmt.Println("timeout:", timeout)
	fmt.Println("version:", version)

	// Untyped constants are flexible
	const number = 42

	var int8Val int8 = number         // OK: 42 fits in int8
	var int64Val int64 = number       // OK: 42 fits in int64
	var float32Val float32 = number   // OK: 42 can be float32
	var complexVal complex64 = number // OK: 42 can be complex

	fmt.Println("int8Val:", int8Val)
	fmt.Println("int64Val:", int64Val)
	fmt.Println("float32Val:", float32Val)
	fmt.Println("complexVal:", complexVal)

	var int8Val1 int8 = number // OK: 42 fits in int8

	// Variables require explicit conversion
	var numberVar int = 42
	// var int8Val int8 = numberVar     // Error: cannot use int as int8
	var int8Val2 int8 = int8(numberVar) // OK: explicit conversion

	fmt.Println("int8Val1:", int8Val1)
	fmt.Println("int8Val2:", int8Val2)

	const typedInt int = 42
	// var int8Val3 int8 = typedInt     // Error: cannot use int as int8
	var int8Val3 int8 = int8(typedInt) // OK: explicit conversion
	fmt.Println("int8Val3:", int8Val3)

	fmt.Println("--- Constants ---")

	fmt.Println("--- Iota ---")

	// Basic iota sequence
	const (
		Sunday    = iota // 0
		Monday           // 1
		Tuesday          // 2
		Wednesday        // 3
		Thursday         // 4
		Friday           // 5
		Saturday         // 6
	)
	fmt.Println("Sunday:", Sunday)
	fmt.Println("Monday:", Monday)
	fmt.Println("Tuesday:", Tuesday)
	fmt.Println("Wednesday:", Wednesday)
	fmt.Println("Thursday:", Thursday)
	fmt.Println("Friday:", Friday)
	fmt.Println("Saturday:", Saturday)

	fmt.Println("--- Iota ---")

}
