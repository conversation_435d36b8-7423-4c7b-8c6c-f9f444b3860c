package main

import (
	"errors"
	"fmt"
	"math/rand"
	"strconv"
	"time"
)

func main() {
	fmt.Println("=== GO CONTROL STRUCTURES EXAMPLES ===\n")

	// If statements
	demonstrateIfStatements()

	// Switch statements
	demonstrateSwitchStatements()

	// For loops
	demonstrateForLoops()

	// Range loops
	demonstrateRangeLoops()

	// Break and continue
	demonstrateBreakContinue()

	// Goto statements
	demonstrateGoto()

	// Defer statements
	demonstrateDefer()

	// Error handling patterns
	demonstrateErrorHandling()

	// Advanced patterns
	demonstrateAdvancedPatterns()
}

func demonstrateIfStatements() {
	fmt.Println("--- IF STATEMENTS ---")

	// Basic if statement
	age := 25
	if age >= 18 {
		fmt.Printf("Age %d: You are an adult\n", age)
	}

	// If-else statement
	score := 85
	if score >= 90 {
		fmt.Printf("Score %d: Excellent!\n", score)
	} else if score >= 80 {
		fmt.Printf("Score %d: Good job!\n", score)
	} else if score >= 70 {
		fmt.Printf("Score %d: Not bad\n", score)
	} else {
		fmt.Printf("Score %d: Need improvement\n", score)
	}

	// If with initialization
	if num := rand.Intn(100); num%2 == 0 {
		fmt.Printf("Random number %d is even\n", num)
	} else {
		fmt.Printf("Random number %d is odd\n", num)
	}

	// If with multiple conditions
	temperature := 22
	humidity := 60
	if temperature > 20 && temperature < 30 && humidity < 70 {
		fmt.Printf("Weather is comfortable (temp: %d°C, humidity: %d%%)\n", temperature, humidity)
	}

	// If with function call
	if result, err := divide(10, 2); err != nil {
		fmt.Printf("Error: %v\n", err)
	} else {
		fmt.Printf("Division result: %.2f\n", result)
	}

	// Nested if statements
	username := "admin"
	password := "secret123"
	if username != "" {
		if len(username) >= 3 {
			if password != "" {
				if len(password) >= 8 {
					fmt.Printf("Login successful for user: %s\n", username)
				} else {
					fmt.Println("Password too short")
				}
			} else {
				fmt.Println("Password required")
			}
		} else {
			fmt.Println("Username too short")
		}
	} else {
		fmt.Println("Username required")
	}

	fmt.Println()
}

func demonstrateSwitchStatements() {
	fmt.Println("--- SWITCH STATEMENTS ---")

	// Basic switch
	day := time.Now().Weekday()
	switch day {
	case time.Monday:
		fmt.Println("Today is Monday - Start of work week")
	case time.Tuesday, time.Wednesday, time.Thursday:
		fmt.Println("Today is a weekday - Keep working")
	case time.Friday:
		fmt.Println("Today is Friday - TGIF!")
	case time.Saturday, time.Sunday:
		fmt.Println("Today is weekend - Relax!")
	default:
		fmt.Println("Unknown day")
	}

	// Switch with initialization
	switch hour := time.Now().Hour(); {
	case hour < 6:
		fmt.Printf("It's %d:xx - Very early morning\n", hour)
	case hour < 12:
		fmt.Printf("It's %d:xx - Morning\n", hour)
	case hour < 18:
		fmt.Printf("It's %d:xx - Afternoon\n", hour)
	case hour < 22:
		fmt.Printf("It's %d:xx - Evening\n", hour)
	default:
		fmt.Printf("It's %d:xx - Night\n", hour)
	}

	// Switch without expression (like if-else chain)
	grade := 'B'
	switch {
	case grade == 'A':
		fmt.Println("Excellent work!")
	case grade == 'B':
		fmt.Println("Good work!")
	case grade == 'C':
		fmt.Println("Average work")
	case grade == 'D':
		fmt.Println("Below average")
	case grade == 'F':
		fmt.Println("Failed")
	default:
		fmt.Println("Invalid grade")
	}

	// Type switch
	var value interface{} = "Hello, World!"
	switch v := value.(type) {
	case string:
		fmt.Printf("String value: %q (length: %d)\n", v, len(v))
	case int:
		fmt.Printf("Integer value: %d\n", v)
	case float64:
		fmt.Printf("Float value: %.2f\n", v)
	case bool:
		fmt.Printf("Boolean value: %t\n", v)
	case nil:
		fmt.Println("Nil value")
	default:
		fmt.Printf("Unknown type: %T\n", v)
	}

	// Switch with fallthrough
	number := 3
	fmt.Printf("Number %d: ", number)
	switch number {
	case 1:
		fmt.Print("One ")
		fallthrough
	case 2:
		fmt.Print("Two ")
		fallthrough
	case 3:
		fmt.Print("Three ")
		fallthrough
	case 4:
		fmt.Print("Four ")
		fallthrough
	case 5:
		fmt.Print("Five ")
	default:
		fmt.Print("Other")
	}
	fmt.Println("(with fallthrough)")

	fmt.Println()
}

// Helper function for division
func divide(a, b float64) (float64, error) {
	if b == 0 {
		return 0, errors.New("division by zero")
	}
	return a / b, nil
}

func demonstrateForLoops() {
	fmt.Println("--- FOR LOOPS ---")

	// Basic for loop
	fmt.Print("Basic for loop (1-5): ")
	for i := 1; i <= 5; i++ {
		fmt.Printf("%d ", i)
	}
	fmt.Println()

	// For loop with different increment
	fmt.Print("Even numbers (0-10): ")
	for i := 0; i <= 10; i += 2 {
		fmt.Printf("%d ", i)
	}
	fmt.Println()

	// For loop with decrement
	fmt.Print("Countdown (5-1): ")
	for i := 5; i >= 1; i-- {
		fmt.Printf("%d ", i)
	}
	fmt.Println()

	// While-style for loop
	fmt.Print("While-style loop: ")
	count := 0
	for count < 5 {
		fmt.Printf("%d ", count)
		count++
	}
	fmt.Println()

	// Infinite loop with break
	fmt.Print("Infinite loop with break: ")
	counter := 0
	for {
		if counter >= 3 {
			break
		}
		fmt.Printf("%d ", counter)
		counter++
	}
	fmt.Println()

	// Nested for loops - multiplication table
	fmt.Println("Multiplication table (3x3):")
	for i := 1; i <= 3; i++ {
		for j := 1; j <= 3; j++ {
			fmt.Printf("%d*%d=%d ", i, j, i*j)
		}
		fmt.Println()
	}

	// For loop with multiple variables
	fmt.Print("Multiple variables: ")
	for i, j := 0, 10; i < 5; i, j = i+1, j-1 {
		fmt.Printf("(%d,%d) ", i, j)
	}
	fmt.Println()

	// For loop with string iteration
	text := "Hello"
	fmt.Printf("String iteration '%s': ", text)
	for i := 0; i < len(text); i++ {
		fmt.Printf("%c ", text[i])
	}
	fmt.Println()

	// For loop with condition only (while equivalent)
	fmt.Print("Fibonacci sequence (first 8): ")
	a, b := 0, 1
	count = 0
	for count < 8 {
		fmt.Printf("%d ", a)
		a, b = b, a+b
		count++
	}
	fmt.Println()

	fmt.Println()
}

func demonstrateRangeLoops() {
	fmt.Println("--- RANGE LOOPS ---")

	// Range over slice
	numbers := []int{10, 20, 30, 40, 50}
	fmt.Print("Range over slice (index, value): ")
	for i, v := range numbers {
		fmt.Printf("(%d:%d) ", i, v)
	}
	fmt.Println()

	// Range over slice (value only)
	fmt.Print("Range over slice (value only): ")
	for _, v := range numbers {
		fmt.Printf("%d ", v)
	}
	fmt.Println()

	// Range over slice (index only)
	fmt.Print("Range over slice (index only): ")
	for i := range numbers {
		fmt.Printf("%d ", i)
	}
	fmt.Println()

	// Range over array
	colors := [3]string{"red", "green", "blue"}
	fmt.Print("Range over array: ")
	for i, color := range colors {
		fmt.Printf("%d:%s ", i, color)
	}
	fmt.Println()

	// Range over map
	scores := map[string]int{
		"Alice": 95,
		"Bob":   87,
		"Carol": 92,
	}
	fmt.Println("Range over map:")
	for name, score := range scores {
		fmt.Printf("  %s: %d\n", name, score)
	}

	// Range over string (runes)
	text := "Hello 世界"
	fmt.Printf("Range over string '%s' (runes): ", text)
	for i, r := range text {
		fmt.Printf("(%d:%c) ", i, r)
	}
	fmt.Println()

	// Range over string (bytes)
	fmt.Printf("Range over string '%s' (bytes): ", text)
	for i := 0; i < len(text); i++ {
		fmt.Printf("(%d:%d) ", i, text[i])
	}
	fmt.Println()

	// Range over channel
	ch := make(chan int, 3)
	ch <- 1
	ch <- 2
	ch <- 3
	close(ch)

	fmt.Print("Range over channel: ")
	for value := range ch {
		fmt.Printf("%d ", value)
	}
	fmt.Println()

	// Range with modification
	fmt.Print("Original slice: ")
	nums := []int{1, 2, 3, 4, 5}
	for _, v := range nums {
		fmt.Printf("%d ", v)
	}
	fmt.Println()

	fmt.Print("Modified slice (doubled): ")
	for i := range nums {
		nums[i] *= 2
	}
	for _, v := range nums {
		fmt.Printf("%d ", v)
	}
	fmt.Println()

	fmt.Println()
}

func demonstrateBreakContinue() {
	fmt.Println("--- BREAK AND CONTINUE ---")

	// Continue example
	fmt.Print("Continue example (skip even numbers): ")
	for i := 1; i <= 10; i++ {
		if i%2 == 0 {
			continue // Skip even numbers
		}
		fmt.Printf("%d ", i)
	}
	fmt.Println()

	// Break example
	fmt.Print("Break example (stop at 5): ")
	for i := 1; i <= 10; i++ {
		if i > 5 {
			break // Stop when i > 5
		}
		fmt.Printf("%d ", i)
	}
	fmt.Println()

	// Nested loops with labeled break
	fmt.Println("Nested loops with labeled break:")
outer:
	for i := 1; i <= 3; i++ {
		for j := 1; j <= 3; j++ {
			if i*j > 4 {
				fmt.Printf("Breaking at i=%d, j=%d\n", i, j)
				break outer // Break out of both loops
			}
			fmt.Printf("i=%d, j=%d, product=%d\n", i, j, i*j)
		}
	}

	// Nested loops with labeled continue
	fmt.Println("Nested loops with labeled continue:")
outerLoop:
	for i := 1; i <= 3; i++ {
		for j := 1; j <= 3; j++ {
			if j == 2 {
				fmt.Printf("Skipping j=2 for i=%d\n", i)
				continue outerLoop // Continue outer loop
			}
			fmt.Printf("i=%d, j=%d\n", i, j)
		}
	}

	// Break and continue with range
	fmt.Print("Range with continue (skip negative): ")
	values := []int{-2, -1, 0, 1, 2, 3, -4, 5}
	for _, v := range values {
		if v < 0 {
			continue
		}
		if v > 3 {
			break
		}
		fmt.Printf("%d ", v)
	}
	fmt.Println()

	// Finding first match
	fmt.Println("Finding first match:")
	words := []string{"apple", "banana", "cherry", "date"}
	target := "cherry"
	for i, word := range words {
		if word == target {
			fmt.Printf("Found '%s' at index %d\n", target, i)
			break
		}
	}

	fmt.Println()
}

func demonstrateGoto() {
	fmt.Println("--- GOTO STATEMENTS ---")
	fmt.Println("Note: goto is rarely used in Go and generally discouraged")

	// Simple goto example
	i := 0
start:
	if i < 3 {
		fmt.Printf("Iteration %d\n", i)
		i++
		goto start
	}

	// Goto for error handling (old style)
	fmt.Println("Goto for cleanup pattern:")
	if err := processFile("test.txt"); err != nil {
		fmt.Printf("Error processing file: %v\n", err)
		goto cleanup
	}
	fmt.Println("File processed successfully")

cleanup:
	fmt.Println("Cleanup completed")

	// Goto to skip code
	condition := true
	if condition {
		goto skip
	}
	fmt.Println("This will be skipped")

skip:
	fmt.Println("Skipped to here")

	fmt.Println()
}

func demonstrateDefer() {
	fmt.Println("--- DEFER STATEMENTS ---")

	// Basic defer
	fmt.Println("Basic defer example:")
	defer fmt.Println("This will be printed last")
	fmt.Println("This will be printed first")
	fmt.Println("This will be printed second")

	// Multiple defers (LIFO order)
	fmt.Println("Multiple defers (LIFO order):")
	defer fmt.Println("Defer 1")
	defer fmt.Println("Defer 2")
	defer fmt.Println("Defer 3")
	fmt.Println("Regular statement")

	// Defer with function call
	fmt.Println("Defer with function call:")
	deferExample()

	// Defer for resource cleanup
	fmt.Println("Defer for resource cleanup:")
	resourceCleanupExample()

	// Defer with loop
	fmt.Println("Defer with loop:")
	for i := 1; i <= 3; i++ {
		defer fmt.Printf("Deferred from loop: %d\n", i)
	}

	// Defer with anonymous function
	fmt.Println("Defer with anonymous function:")
	x := 10
	defer func() {
		fmt.Printf("Deferred anonymous function: x = %d\n", x)
	}()
	x = 20
	fmt.Printf("Regular statement: x = %d\n", x)

	fmt.Println()
}

func demonstrateErrorHandling() {
	fmt.Println("--- ERROR HANDLING PATTERNS ---")

	// Basic error handling
	fmt.Println("Basic error handling:")
	result, err := divide(10, 2)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
	} else {
		fmt.Printf("Result: %.2f\n", result)
	}

	// Error handling with zero division
	result, err = divide(10, 0)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
	} else {
		fmt.Printf("Result: %.2f\n", result)
	}

	// Multiple return values with error
	fmt.Println("Multiple return values with error:")
	value, valid, err := parseAndValidate("123")
	if err != nil {
		fmt.Printf("Error: %v\n", err)
	} else {
		fmt.Printf("Value: %d, Valid: %t\n", value, valid)
	}

	// Error handling in loops
	fmt.Println("Error handling in loops:")
	inputs := []string{"10", "abc", "20", "xyz", "30"}
	for i, input := range inputs {
		if num, err := strconv.Atoi(input); err != nil {
			fmt.Printf("Input %d ('%s'): Error - %v\n", i, input, err)
			continue
		} else {
			fmt.Printf("Input %d ('%s'): Number - %d\n", i, input, num)
		}
	}

	// Early return pattern
	fmt.Println("Early return pattern:")
	if err := validateUser("admin", "password123"); err != nil {
		fmt.Printf("Validation failed: %v\n", err)
		return
	}
	fmt.Println("User validation successful")

	fmt.Println()
}

// Helper functions
func processFile(filename string) error {
	// Simulate file processing
	if filename == "test.txt" {
		return errors.New("file not found")
	}
	return nil
}

func deferExample() {
	defer fmt.Println("  Deferred in deferExample")
	fmt.Println("  Regular statement in deferExample")
}

func resourceCleanupExample() {
	fmt.Println("  Opening resource...")
	defer fmt.Println("  Closing resource...")
	fmt.Println("  Using resource...")
}

func parseAndValidate(s string) (int, bool, error) {
	num, err := strconv.Atoi(s)
	if err != nil {
		return 0, false, err
	}
	valid := num > 0 && num < 1000
	return num, valid, nil
}

func validateUser(username, password string) error {
	if username == "" {
		return errors.New("username cannot be empty")
	}
	if len(password) < 8 {
		return errors.New("password must be at least 8 characters")
	}
	return nil
}

func demonstrateAdvancedPatterns() {
	fmt.Println("--- ADVANCED PATTERNS ---")

	// State machine using switch
	fmt.Println("State machine example:")
	state := "start"
	for i := 0; i < 5; i++ {
		switch state {
		case "start":
			fmt.Printf("Step %d: Starting process\n", i+1)
			state = "processing"
		case "processing":
			fmt.Printf("Step %d: Processing data\n", i+1)
			if i%2 == 0 {
				state = "waiting"
			} else {
				state = "complete"
			}
		case "waiting":
			fmt.Printf("Step %d: Waiting for input\n", i+1)
			state = "processing"
		case "complete":
			fmt.Printf("Step %d: Process complete\n", i+1)
			state = "start"
		}
	}

	// Pattern matching with type switch
	fmt.Println("Pattern matching with type switch:")
	values := []interface{}{
		42,
		"hello",
		3.14,
		[]int{1, 2, 3},
		map[string]int{"key": 42},
		true,
	}

	for i, v := range values {
		fmt.Printf("Value %d: ", i+1)
		switch val := v.(type) {
		case int:
			if val > 0 {
				fmt.Printf("Positive integer: %d\n", val)
			} else {
				fmt.Printf("Non-positive integer: %d\n", val)
			}
		case string:
			if len(val) > 5 {
				fmt.Printf("Long string: %q\n", val)
			} else {
				fmt.Printf("Short string: %q\n", val)
			}
		case float64:
			fmt.Printf("Float: %.2f\n", val)
		case []int:
			fmt.Printf("Integer slice with %d elements\n", len(val))
		case map[string]int:
			fmt.Printf("String-to-int map with %d keys\n", len(val))
		case bool:
			fmt.Printf("Boolean: %t\n", val)
		default:
			fmt.Printf("Unknown type: %T\n", val)
		}
	}

	// Control flow with channels
	fmt.Println("Control flow with channels:")
	done := make(chan bool)
	go func() {
		for i := 1; i <= 3; i++ {
			fmt.Printf("  Goroutine working: step %d\n", i)
			time.Sleep(100 * time.Millisecond)
		}
		done <- true
	}()

	// Wait for goroutine to complete
	select {
	case <-done:
		fmt.Println("  Goroutine completed")
	case <-time.After(1 * time.Second):
		fmt.Println("  Timeout waiting for goroutine")
	}

	// Complex nested control structures
	fmt.Println("Complex nested control structures:")
	matrix := [][]int{
		{1, 2, 3},
		{4, 5, 6},
		{7, 8, 9},
	}

	target := 5
	found := false
outerSearch:
	for i, row := range matrix {
		for j, val := range row {
			if val == target {
				fmt.Printf("Found %d at position (%d, %d)\n", target, i, j)
				found = true
				break outerSearch
			}
		}
	}

	if !found {
		fmt.Printf("Value %d not found in matrix\n", target)
	}

	// Error handling with multiple conditions
	fmt.Println("Complex error handling:")
	data := []string{"", "abc", "123", "456"}
	for i, item := range data {
		switch {
		case item == "":
			fmt.Printf("Item %d: Empty string - skipping\n", i)
			continue
		case len(item) < 3:
			fmt.Printf("Item %d: Too short - %q\n", i, item)
		default:
			if num, err := strconv.Atoi(item); err == nil {
				if num%2 == 0 {
					fmt.Printf("Item %d: Even number - %d\n", i, num)
				} else {
					fmt.Printf("Item %d: Odd number - %d\n", i, num)
				}
			} else {
				fmt.Printf("Item %d: Text - %q\n", i, item)
			}
		}
	}

	fmt.Println("\n=== END OF CONTROL STRUCTURES EXAMPLES ===")
}
