package main

import "fmt"

type contactInfo struct {
	email   string
	zipCode int
}

type person struct {
	firstName string
	lastName  string
	contact   contactInfo
}

func main() {
	/*var alex person

	alex.firstName = "Alex"
	alex.lastName = "Anderson"
	//fmt.Println(alex)
	//fmt.Printf("%+v", alex)

	jim := person{
		firstName: "Jim",
		lastName:  "Party",
		contact: contactInfo{
			email:   "<EMAIL>",
			zipCode: 54200,
		},
	}

	jimPointer := &jim
	fmt.Println(jimPointer)
	fmt.Printf("%+v", jimPointer)

	jimPointer.updateName("Jimmy")
	fmt.Println(jim)
	jim.print()
	*/

	name := "bill"

	namePointer := &name

	fmt.Println(&namePointer)
	printPointer(namePointer)
}

func printPointer(namePointer *string) {
	fmt.Println(&namePointer)
}

func (p *person) updateName(newFirstName string) {
	(*p).firstName = newFirstName
}

func (p person) print() {
	fmt.Printf("%+v", p)
}
