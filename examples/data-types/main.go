package main

import (
	"fmt"
)

func main() {
	fmt.Println("--- Data Types ---")

	// Basic types
	fmt.Println("--- Basic Types ---")

	// Numeric types
	fmt.Println("--- Numeric Types ---")

	// Integer types
	fmt.Println("--- Integer Types ---")

	// Signed integers
	fmt.Println("--- Signed Integers ---")

	// Unsigned integers
	fmt.Println("--- Unsigned Integers ---")

	// Floating-point numbers
	fmt.Println("--- Floating-Point Numbers ---")

	// Complex numbers
	fmt.Println("--- Complex Numbers ---")

	// String type
	fmt.Println("--- String Type ---")

	// Boolean type
	fmt.Println("--- Boolean Type ---")

	// Composite types
	fmt.Println("--- Composite Types ---")

	// Arrays
	fmt.Println("--- Arrays ---")

	// Slices
	fmt.Println("--- Slices ---")

	// Maps
	fmt.Println("--- Maps ---")

	// Structs
	fmt.Println("--- Structs ---")

	// Pointers
	fmt.Println("--- Pointers ---")

	// Interfaces
	fmt.Println("--- Interfaces ---")

	// Channels
	fmt.Println("--- Channels ---")

	// Functions as types
	fmt.Println("--- Functions as Types ---")

	// Type conversion
	fmt.Println("--- Type Conversion ---")

	// Type assertions
	fmt.Println("--- Type Assertions ---")

	// Custom types
	fmt.Println("--- Custom Types ---")

	// Type embedding
	fmt.Println("--- Type Embedding ---")

	// Memory layout
	fmt.Println("--- Memory Layout ---")

	// Performance considerations
	fmt.Println("--- Performance Considerations ---")
}
