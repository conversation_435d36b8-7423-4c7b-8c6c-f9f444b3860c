package main

import (
	"fmt"
	"math"
	"reflect"
	"strings"
	"time"
	"unsafe"
)

// Custom types for examples
type UserID int
type Temperature float64
type Status string

// Struct types
type Person struct {
	Name string
	Age  int
}

type Employee struct {
	Person // Embedded struct
	ID     UserID
	Salary float64
}

// Interface types
type Shape interface {
	Area() float64
	Perimeter() float64
}

type Rectangle struct {
	Width, Height float64
}

func (r Rectangle) Area() float64 {
	return r.Width * r.Height
}

func (r Rectangle) Perimeter() float64 {
	return 2 * (r.Width + r.Height)
}

// Function types
type Calculator func(int, int) int
type Validator func(string) bool

func main() {
	fmt.Println("=== GO DATA TYPES EXAMPLES ===\n")

	// Basic types
	demonstrateBasicTypes()

	// Numeric types
	demonstrateNumericTypes()

	// String operations
	demonstrateStringTypes()

	// Boolean operations
	demonstrateBooleanTypes()

	// Composite types
	demonstrateCompositeTypes()

	// Type conversions
	demonstrateTypeConversions()

	// Custom types
	demonstrateCustomTypes()

	// Memory and performance
	demonstrateMemoryLayout()
}

func demonstrateBasicTypes() {
	fmt.Println("--- BASIC TYPES ---")

	// Integer types - signed
	var int8Val int8 = 127
	var int16Val int16 = 32767
	var int32Val int32 = 2147483647
	var int64Val int64 = 9223372036854775807
	var intVal int = 42 // Platform dependent (32 or 64 bit)

	fmt.Printf("Signed integers:\n")
	fmt.Printf("int8:  %d (size: %d bytes, range: %d to %d)\n",
		int8Val, unsafe.Sizeof(int8Val), math.MinInt8, math.MaxInt8)
	fmt.Printf("int16: %d (size: %d bytes, range: %d to %d)\n",
		int16Val, unsafe.Sizeof(int16Val), math.MinInt16, math.MaxInt16)
	fmt.Printf("int32: %d (size: %d bytes, range: %d to %d)\n",
		int32Val, unsafe.Sizeof(int32Val), math.MinInt32, math.MaxInt32)
	fmt.Printf("int64: %d (size: %d bytes, range: %d to %d)\n",
		int64Val, unsafe.Sizeof(int64Val), math.MinInt64, math.MaxInt64)
	fmt.Printf("int:   %d (size: %d bytes)\n", intVal, unsafe.Sizeof(intVal))

	// Integer types - unsigned
	var uint8Val uint8 = 255
	var uint16Val uint16 = 65535
	var uint32Val uint32 = 4294967295
	var uint64Val uint64 = 18446744073709551615
	var uintVal uint = 42
	var byteVal byte = 255 // alias for uint8

	fmt.Printf("\nUnsigned integers:\n")
	fmt.Printf("uint8:  %d (size: %d bytes, range: 0 to %d)\n",
		uint8Val, unsafe.Sizeof(uint8Val), math.MaxUint8)
	fmt.Printf("uint16: %d (size: %d bytes, range: 0 to %d)\n",
		uint16Val, unsafe.Sizeof(uint16Val), math.MaxUint16)
	fmt.Printf("uint32: %d (size: %d bytes, range: 0 to %d)\n",
		uint32Val, unsafe.Sizeof(uint32Val), math.MaxUint32)
	fmt.Printf("uint64: %d (size: %d bytes, range: 0 to %d)\n",
		uint64Val, unsafe.Sizeof(uint64Val), uint64(math.MaxUint64))
	fmt.Printf("uint:   %d (size: %d bytes)\n", uintVal, unsafe.Sizeof(uintVal))
	fmt.Printf("byte:   %d (size: %d bytes, alias for uint8)\n",
		byteVal, unsafe.Sizeof(byteVal))

	// Special integer type
	var uintptrVal uintptr = uintptr(unsafe.Pointer(&intVal))
	fmt.Printf("uintptr: %d (size: %d bytes, pointer as integer)\n",
		uintptrVal, unsafe.Sizeof(uintptrVal))

	fmt.Println()
}

func demonstrateNumericTypes() {
	fmt.Println("--- NUMERIC TYPES ---")

	// Floating-point numbers
	var float32Val float32 = 3.14159
	var float64Val float64 = 3.141592653589793

	fmt.Printf("Floating-point numbers:\n")
	fmt.Printf("float32: %f (size: %d bytes, precision: ~7 digits)\n",
		float32Val, unsafe.Sizeof(float32Val))
	fmt.Printf("float64: %f (size: %d bytes, precision: ~15 digits)\n",
		float64Val, unsafe.Sizeof(float64Val))

	// Special floating-point values
	fmt.Printf("\nSpecial float values:\n")
	fmt.Printf("Positive infinity: %f\n", math.Inf(1))
	fmt.Printf("Negative infinity: %f\n", math.Inf(-1))
	fmt.Printf("Not a Number: %f\n", math.NaN())
	fmt.Printf("Is NaN: %t\n", math.IsNaN(math.NaN()))
	fmt.Printf("Is Inf: %t\n", math.IsInf(math.Inf(1), 0))

	// Complex numbers
	var complex64Val complex64 = 3 + 4i
	var complex128Val complex128 = 5 + 12i

	fmt.Printf("\nComplex numbers:\n")
	fmt.Printf("complex64:  %v (size: %d bytes)\n", complex64Val, unsafe.Sizeof(complex64Val))
	fmt.Printf("complex128: %v (size: %d bytes)\n", complex128Val, unsafe.Sizeof(complex128Val))

	// Complex number operations
	fmt.Printf("Real part of %v: %f\n", complex128Val, real(complex128Val))
	fmt.Printf("Imaginary part of %v: %f\n", complex128Val, imag(complex128Val))
	fmt.Printf("Absolute value: %f\n", math.Abs(real(complex128Val)))

	// Creating complex numbers
	complexFromParts := complex(3.0, 4.0)
	fmt.Printf("Complex from parts: %v\n", complexFromParts)

	// Numeric operations and overflow
	fmt.Printf("\nNumeric operations:\n")
	var maxInt8 int8 = math.MaxInt8
	fmt.Printf("Max int8: %d\n", maxInt8)
	// Overflow example (commented to avoid runtime panic)
	// maxInt8++ // This would cause overflow

	// Type-specific constants
	fmt.Printf("\nNumeric constants:\n")
	fmt.Printf("math.MaxInt8:    %d\n", math.MaxInt8)
	fmt.Printf("math.MaxInt16:   %d\n", math.MaxInt16)
	fmt.Printf("math.MaxInt32:   %d\n", math.MaxInt32)
	fmt.Printf("math.MaxInt64:   %d\n", math.MaxInt64)
	fmt.Printf("math.MaxUint8:   %d\n", math.MaxUint8)
	fmt.Printf("math.MaxUint16:  %d\n", math.MaxUint16)
	fmt.Printf("math.MaxUint32:  %d\n", math.MaxUint32)
	fmt.Printf("math.MaxFloat32: %g\n", math.MaxFloat32)
	fmt.Printf("math.MaxFloat64: %g\n", math.MaxFloat64)

	fmt.Println()
}

func demonstrateStringTypes() {
	fmt.Println("--- STRING TYPES ---")

	// String basics
	var str1 string = "Hello, World!"
	str2 := "Go Programming"
	str3 := `Raw string literal
	with multiple lines
	and special characters: \n \t`

	fmt.Printf("String examples:\n")
	fmt.Printf("str1: %q (length: %d, size: %d bytes)\n",
		str1, len(str1), unsafe.Sizeof(str1))
	fmt.Printf("str2: %q\n", str2)
	fmt.Printf("str3: %q\n", str3)

	// String operations
	fmt.Printf("\nString operations:\n")
	fmt.Printf("Concatenation: %q\n", str1+" "+str2)
	fmt.Printf("Contains 'World': %t\n", strings.Contains(str1, "World"))
	fmt.Printf("Index of 'World': %d\n", strings.Index(str1, "World"))
	fmt.Printf("ToUpper: %q\n", strings.ToUpper(str2))
	fmt.Printf("ToLower: %q\n", strings.ToLower(str2))
	fmt.Printf("Split: %v\n", strings.Split(str2, " "))

	// String indexing and slicing
	fmt.Printf("\nString indexing:\n")
	fmt.Printf("First byte: %c (value: %d)\n", str1[0], str1[0])
	fmt.Printf("Substring [0:5]: %q\n", str1[0:5])
	fmt.Printf("Substring [7:]: %q\n", str1[7:])

	// Rune type (alias for int32)
	var runeVal rune = 'A'
	var runeUnicode rune = '🚀'

	fmt.Printf("\nRune type:\n")
	fmt.Printf("Rune 'A': %c (value: %d, size: %d bytes)\n",
		runeVal, runeVal, unsafe.Sizeof(runeVal))
	fmt.Printf("Unicode rune: %c (value: %d)\n", runeUnicode, runeUnicode)

	// String iteration
	fmt.Printf("\nString iteration:\n")
	unicodeStr := "Hello 世界 🌍"
	fmt.Printf("String: %q (len: %d bytes)\n", unicodeStr, len(unicodeStr))

	// Byte iteration
	fmt.Printf("Byte iteration: ")
	for i := 0; i < len(unicodeStr); i++ {
		fmt.Printf("%d ", unicodeStr[i])
	}
	fmt.Println()

	// Rune iteration
	fmt.Printf("Rune iteration: ")
	for _, r := range unicodeStr {
		fmt.Printf("%c ", r)
	}
	fmt.Println()

	// String conversion
	fmt.Printf("\nString conversions:\n")
	bytes := []byte(str1)
	runes := []rune(str1)
	fmt.Printf("String to bytes: %v\n", bytes)
	fmt.Printf("String to runes: %v\n", runes)
	fmt.Printf("Bytes to string: %q\n", string(bytes))
	fmt.Printf("Runes to string: %q\n", string(runes))

	fmt.Println()
}

func demonstrateBooleanTypes() {
	fmt.Println("--- BOOLEAN TYPES ---")

	// Boolean basics
	var bool1 bool = true
	var bool2 bool = false
	bool3 := true

	fmt.Printf("Boolean values:\n")
	fmt.Printf("bool1: %t (size: %d bytes)\n", bool1, unsafe.Sizeof(bool1))
	fmt.Printf("bool2: %t\n", bool2)
	fmt.Printf("bool3: %t\n", bool3)

	// Boolean operations
	fmt.Printf("\nBoolean operations:\n")
	fmt.Printf("bool1 && bool2: %t (AND)\n", bool1 && bool2)
	fmt.Printf("bool1 || bool2: %t (OR)\n", bool1 || bool2)
	fmt.Printf("!bool1: %t (NOT)\n", !bool1)
	fmt.Printf("!bool2: %t (NOT)\n", !bool2)

	// Comparison operations
	fmt.Printf("\nComparison operations:\n")
	a, b := 10, 20
	fmt.Printf("%d == %d: %t\n", a, b, a == b)
	fmt.Printf("%d != %d: %t\n", a, b, a != b)
	fmt.Printf("%d < %d: %t\n", a, b, a < b)
	fmt.Printf("%d > %d: %t\n", a, b, a > b)
	fmt.Printf("%d <= %d: %t\n", a, b, a <= b)
	fmt.Printf("%d >= %d: %t\n", a, b, a >= b)

	// Zero value
	var zeroBool bool
	fmt.Printf("\nZero value of bool: %t\n", zeroBool)

	fmt.Println()
}

func demonstrateCompositeTypes() {
	fmt.Println("--- COMPOSITE TYPES ---")

	// Arrays
	fmt.Printf("Arrays:\n")
	var arr1 [5]int = [5]int{1, 2, 3, 4, 5}
	arr2 := [3]string{"Go", "is", "awesome"}
	arr3 := [...]int{10, 20, 30} // Compiler infers length

	fmt.Printf("arr1: %v (type: %T, size: %d bytes)\n",
		arr1, arr1, unsafe.Sizeof(arr1))
	fmt.Printf("arr2: %v (type: %T)\n", arr2, arr2)
	fmt.Printf("arr3: %v (length: %d)\n", arr3, len(arr3))

	// Slices
	fmt.Printf("\nSlices:\n")
	var slice1 []int = []int{1, 2, 3, 4, 5}
	slice2 := make([]string, 3, 5) // length 3, capacity 5
	slice3 := arr1[1:4]            // Slice from array

	fmt.Printf("slice1: %v (len: %d, cap: %d, size: %d bytes)\n",
		slice1, len(slice1), cap(slice1), unsafe.Sizeof(slice1))
	fmt.Printf("slice2: %v (len: %d, cap: %d)\n",
		slice2, len(slice2), cap(slice2))
	fmt.Printf("slice3: %v (len: %d, cap: %d)\n",
		slice3, len(slice3), cap(slice3))

	// Slice operations
	slice1 = append(slice1, 6, 7)
	fmt.Printf("After append: %v (len: %d, cap: %d)\n",
		slice1, len(slice1), cap(slice1))

	// Maps
	fmt.Printf("\nMaps:\n")
	var map1 map[string]int = map[string]int{
		"apple":  5,
		"banana": 3,
		"orange": 8,
	}
	map2 := make(map[int]string)
	map2[1] = "one"
	map2[2] = "two"

	fmt.Printf("map1: %v (size: %d bytes)\n", map1, unsafe.Sizeof(map1))
	fmt.Printf("map2: %v\n", map2)

	// Map operations
	value, exists := map1["apple"]
	fmt.Printf("map1['apple']: %d (exists: %t)\n", value, exists)

	delete(map1, "banana")
	fmt.Printf("After delete: %v\n", map1)

	fmt.Println()
}

func demonstrateTypeConversions() {
	fmt.Println("--- TYPE CONVERSIONS ---")

	// Numeric conversions
	var intVal int = 42
	var floatVal float64 = 3.14159
	var complexVal complex128 = 3 + 4i

	fmt.Printf("Numeric conversions:\n")
	fmt.Printf("int to float64: %d -> %f\n", intVal, float64(intVal))
	fmt.Printf("float64 to int: %f -> %d\n", floatVal, int(floatVal))
	fmt.Printf("int to string: %d -> %q\n", intVal, string(rune(intVal))) // ASCII conversion

	// String conversions
	fmt.Printf("\nString conversions:\n")
	str := "123"
	fmt.Printf("String to bytes: %q -> %v\n", str, []byte(str))
	fmt.Printf("String to runes: %q -> %v\n", str, []rune(str))
	fmt.Printf("Bytes to string: %v -> %q\n", []byte{72, 101, 108, 108, 111}, string([]byte{72, 101, 108, 108, 111}))

	// Type assertions with interfaces
	fmt.Printf("\nType assertions:\n")
	var i interface{} = "Hello, World!"

	// Safe type assertion
	if str, ok := i.(string); ok {
		fmt.Printf("Interface contains string: %q\n", str)
	}

	// Type switch
	switch v := i.(type) {
	case string:
		fmt.Printf("Type switch - string: %q (length: %d)\n", v, len(v))
	case int:
		fmt.Printf("Type switch - int: %d\n", v)
	case float64:
		fmt.Printf("Type switch - float64: %f\n", v)
	default:
		fmt.Printf("Type switch - unknown type: %T\n", v)
	}

	// Complex number conversions
	fmt.Printf("\nComplex conversions:\n")
	fmt.Printf("Complex to real: %v -> %f\n", complexVal, real(complexVal))
	fmt.Printf("Complex to imag: %v -> %f\n", complexVal, imag(complexVal))

	// Pointer conversions
	fmt.Printf("\nPointer operations:\n")
	x := 42
	ptr := &x
	fmt.Printf("Value: %d, Pointer: %p, Dereferenced: %d\n", x, ptr, *ptr)

	fmt.Println()
}

func demonstrateCustomTypes() {
	fmt.Println("--- CUSTOM TYPES ---")

	// Custom type definitions
	var userID UserID = 12345
	var temp Temperature = 23.5
	var status Status = "active"

	fmt.Printf("Custom types:\n")
	fmt.Printf("UserID: %d (type: %T)\n", userID, userID)
	fmt.Printf("Temperature: %.1f (type: %T)\n", temp, temp)
	fmt.Printf("Status: %s (type: %T)\n", status, status)

	// Struct types
	fmt.Printf("\nStruct types:\n")
	person := Person{
		Name: "Alice",
		Age:  30,
	}

	employee := Employee{
		Person: Person{Name: "Bob", Age: 25},
		ID:     UserID(67890),
		Salary: 75000.0,
	}

	fmt.Printf("Person: %+v (size: %d bytes)\n", person, unsafe.Sizeof(person))
	fmt.Printf("Employee: %+v (size: %d bytes)\n", employee, unsafe.Sizeof(employee))
	fmt.Printf("Employee name (embedded): %s\n", employee.Name) // Accessing embedded field

	// Interface types
	fmt.Printf("\nInterface types:\n")
	rect := Rectangle{Width: 5.0, Height: 3.0}
	var shape Shape = rect

	fmt.Printf("Rectangle: %+v\n", rect)
	fmt.Printf("As Shape interface - Area: %.2f, Perimeter: %.2f\n",
		shape.Area(), shape.Perimeter())

	// Function types
	fmt.Printf("\nFunction types:\n")
	var calc Calculator = func(a, b int) int {
		return a + b
	}

	var validator Validator = func(s string) bool {
		return len(s) > 0
	}

	fmt.Printf("Calculator result: %d\n", calc(10, 20))
	fmt.Printf("Validator result: %t\n", validator("test"))
	fmt.Printf("Validator result (empty): %t\n", validator(""))

	// Method on custom type
	fmt.Printf("\nMethods on custom types:\n")
	fmt.Printf("Temperature in Celsius: %.1f°C\n", temp)
	fmt.Printf("Temperature in Fahrenheit: %.1f°F\n", temp.ToFahrenheit())
	fmt.Printf("Temperature in Kelvin: %.1f K\n", temp.ToKelvin())

	// Type embedding and composition
	fmt.Printf("\nType embedding:\n")
	fmt.Printf("Employee ID: %d\n", employee.ID)
	fmt.Printf("Employee Person.Name: %s\n", employee.Person.Name)
	fmt.Printf("Employee Name (promoted): %s\n", employee.Name)

	fmt.Println()
}

// Methods for custom types
func (t Temperature) ToFahrenheit() float64 {
	return float64(t)*9/5 + 32
}

func (t Temperature) ToKelvin() float64 {
	return float64(t) + 273.15
}

func demonstrateMemoryLayout() {
	fmt.Println("--- MEMORY LAYOUT & PERFORMANCE ---")

	// Size of basic types
	fmt.Printf("Size of basic types:\n")
	fmt.Printf("bool:       %d bytes\n", unsafe.Sizeof(bool(true)))
	fmt.Printf("int8:       %d bytes\n", unsafe.Sizeof(int8(0)))
	fmt.Printf("int16:      %d bytes\n", unsafe.Sizeof(int16(0)))
	fmt.Printf("int32:      %d bytes\n", unsafe.Sizeof(int32(0)))
	fmt.Printf("int64:      %d bytes\n", unsafe.Sizeof(int64(0)))
	fmt.Printf("int:        %d bytes\n", unsafe.Sizeof(int(0)))
	fmt.Printf("uint8:      %d bytes\n", unsafe.Sizeof(uint8(0)))
	fmt.Printf("uint16:     %d bytes\n", unsafe.Sizeof(uint16(0)))
	fmt.Printf("uint32:     %d bytes\n", unsafe.Sizeof(uint32(0)))
	fmt.Printf("uint64:     %d bytes\n", unsafe.Sizeof(uint64(0)))
	fmt.Printf("uint:       %d bytes\n", unsafe.Sizeof(uint(0)))
	fmt.Printf("uintptr:    %d bytes\n", unsafe.Sizeof(uintptr(0)))
	fmt.Printf("float32:    %d bytes\n", unsafe.Sizeof(float32(0)))
	fmt.Printf("float64:    %d bytes\n", unsafe.Sizeof(float64(0)))
	fmt.Printf("complex64:  %d bytes\n", unsafe.Sizeof(complex64(0)))
	fmt.Printf("complex128: %d bytes\n", unsafe.Sizeof(complex128(0)))
	fmt.Printf("string:     %d bytes\n", unsafe.Sizeof(string("")))

	// Size of composite types
	fmt.Printf("\nSize of composite types:\n")
	fmt.Printf("[]int:      %d bytes (slice header)\n", unsafe.Sizeof([]int{}))
	fmt.Printf("[5]int:     %d bytes (array)\n", unsafe.Sizeof([5]int{}))
	fmt.Printf("map[string]int: %d bytes (map header)\n", unsafe.Sizeof(map[string]int{}))
	fmt.Printf("chan int:   %d bytes (channel header)\n", unsafe.Sizeof(make(chan int)))
	fmt.Printf("*int:       %d bytes (pointer)\n", unsafe.Sizeof((*int)(nil)))
	fmt.Printf("interface{}: %d bytes (interface header)\n", unsafe.Sizeof(interface{}(nil)))
	fmt.Printf("func():     %d bytes (function pointer)\n", unsafe.Sizeof(func() {}))

	// Struct alignment and padding
	fmt.Printf("\nStruct alignment and padding:\n")

	type SmallStruct struct {
		a bool  // 1 byte
		b int32 // 4 bytes
		c bool  // 1 byte
	}

	type OptimizedStruct struct {
		a bool  // 1 byte
		c bool  // 1 byte (packed together)
		b int32 // 4 bytes
	}

	type LargeStruct struct {
		a bool    // 1 byte
		b int64   // 8 bytes
		c bool    // 1 byte
		d float64 // 8 bytes
		e bool    // 1 byte
	}

	fmt.Printf("SmallStruct:    %d bytes (bool + int32 + bool with padding)\n",
		unsafe.Sizeof(SmallStruct{}))
	fmt.Printf("OptimizedStruct: %d bytes (optimized layout)\n",
		unsafe.Sizeof(OptimizedStruct{}))
	fmt.Printf("LargeStruct:    %d bytes (multiple padding gaps)\n",
		unsafe.Sizeof(LargeStruct{}))

	// Zero values
	fmt.Printf("\nZero values:\n")
	var zeroInt int
	var zeroFloat float64
	var zeroString string
	var zeroBool bool
	var zeroSlice []int
	var zeroMap map[string]int
	var zeroPtr *int
	var zeroInterface interface{}

	fmt.Printf("int:        %d\n", zeroInt)
	fmt.Printf("float64:    %f\n", zeroFloat)
	fmt.Printf("string:     %q\n", zeroString)
	fmt.Printf("bool:       %t\n", zeroBool)
	fmt.Printf("[]int:      %v (nil: %t)\n", zeroSlice, zeroSlice == nil)
	fmt.Printf("map:        %v (nil: %t)\n", zeroMap, zeroMap == nil)
	fmt.Printf("*int:       %v (nil: %t)\n", zeroPtr, zeroPtr == nil)
	fmt.Printf("interface{}: %v (nil: %t)\n", zeroInterface, zeroInterface == nil)

	// Type reflection
	fmt.Printf("\nType reflection:\n")
	values := []interface{}{
		42,
		3.14,
		"hello",
		true,
		[]int{1, 2, 3},
		map[string]int{"key": 42},
		Person{Name: "Alice", Age: 30},
	}

	for _, v := range values {
		t := reflect.TypeOf(v)
		val := reflect.ValueOf(v)
		fmt.Printf("Value: %-20v Type: %-15s Kind: %-10s Size: %d bytes\n",
			v, t.String(), val.Kind().String(), t.Size())
	}

	// Performance considerations
	fmt.Printf("\nPerformance considerations:\n")
	fmt.Printf("- Use appropriate integer sizes (int8 vs int64)\n")
	fmt.Printf("- Consider struct field ordering for memory efficiency\n")
	fmt.Printf("- Prefer value types over pointers for small data\n")
	fmt.Printf("- Use slices instead of arrays for dynamic data\n")
	fmt.Printf("- Be aware of interface{} overhead\n")
	fmt.Printf("- Consider memory alignment for performance\n")

	// Timing example
	fmt.Printf("\nTiming example:\n")
	start := time.Now()

	// Simulate some work
	sum := 0
	for i := 0; i < 1000000; i++ {
		sum += i
	}

	duration := time.Since(start)
	fmt.Printf("Calculated sum: %d in %v\n", sum, duration)

	fmt.Println("\n=== END OF DATA TYPES EXAMPLES ===")
}
